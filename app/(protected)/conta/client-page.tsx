"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { ProfileForm } from "@/components/profile-form";
// Removido import do SharedLayout

export default function ClientPage() {
  const { user, loading, profileLoading, userProfile } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Se o usuário não estiver autenticado e não estiver carregando, redirecionar para o login
    if (!loading && !user) {
      router.push("/login");
    } else if (!loading && !profileLoading && user && userProfile) {
      // Perfil carregado
      console.log("Perfil carregado na página de conta:", userProfile);
    }
  }, [user, loading, profileLoading, userProfile, router]);

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold tracking-tight">Conta</h1>
            <p className="text-muted-foreground mt-2">
              Gerencie as suas informações pessoais
            </p>
          </div>

          <div className="max-w-3xl">
            <ProfileForm isAccountPage={true} />
          </div>
        </div>
      </div>
    </div>
  );
}
