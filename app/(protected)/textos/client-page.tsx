"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout
import { TextosTable } from "@/components/textos-table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { IconPlus } from "@tabler/icons-react";
import { SearchBar } from "@/components/search-bar";
import { TextInput } from "@/components/text-input";
import {
  addTexto,
  getTextos,
  updateTexto,
  deleteTexto,
  listenToTextos,
  Texto
} from "@/services/textos-service";

export default function ClientPage() {
  const { user, loading, profileLoading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [textos, setTextos] = useState<Texto[]>([]);
  const [filteredTextos, setFilteredTextos] = useState<Texto[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Carregar textos do Firestore
  const loadTextos = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const data = await getTextos();
      setTextos(data);
      setFilteredTextos(data);
    } catch (error) {
      console.error("Erro ao carregar textos:", error);
    } finally {
      setIsLoading(false);
    }
  }, [user, setTextos, setFilteredTextos, setIsLoading]);

  // Configurar listener em tempo real para textos
  useEffect(() => {
    // Se o usuário não estiver autenticado e não estiver carregando, redirecionar para o login
    if (!loading && !user) {
      router.push("/login");
    } else if (!loading && !profileLoading && user) {
      // Inicialmente, carregar textos normalmente para mostrar algo rapidamente
      loadTextos();

      // Configurar listener em tempo real para atualizações
      const unsubscribe = listenToTextos((updatedTextos) => {
        setTextos(updatedTextos);
        setFilteredTextos(updatedTextos);
        setIsLoading(false);
      });

      // Limpar listener quando o componente for desmontado
      return () => {
        unsubscribe();
      };
    }
  }, [user, loading, profileLoading, router, loadTextos]);

  // Efeito para filtrar textos com base na pesquisa
  useEffect(() => {
    if (!textos.length) return;

    if (!searchQuery.trim()) {
      setFilteredTextos(textos);
      return;
    }

    const query = searchQuery.toLowerCase().trim();
    const filtered = textos.filter(texto =>
      texto.title.toLowerCase().includes(query)
    );

    setFilteredTextos(filtered);
  }, [searchQuery, textos]);

  // Função para lidar com a pesquisa
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Manipular o salvamento de um texto
  const handleSaveComplete = async (textData: {
    title: string;
    content: string;
    createdBy: string;
  }) => {
    try {
      // Adicionar o texto ao Firestore
      await addTexto(textData);
      // Recarregar a lista de textos
      loadTextos();
      // Fechar o modal
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Erro ao salvar texto:", error);
    }
  };

  // Manipular a exclusão de um texto
  const handleDelete = async (id: string) => {
    if (!user?.uid) {
      console.error("Usuário não autenticado");
      throw new Error("Usuário não autenticado");
    }

    try {
      await deleteTexto(id, user.uid);
      // Atualizar a lista local removendo o texto excluído
      const updatedTextos = textos.filter(t => t.id !== id);
      setTextos(updatedTextos);
      // Atualizar também a lista filtrada
      setFilteredTextos(prev => prev.filter(t => t.id !== id));
    } catch (error) {
      console.error("Erro ao excluir texto:", error);
      throw error;
    }
  };

  // Manipular a edição de um texto
  const handleEdit = async (id: string, newTitle: string, newContent: string) => {
    try {
      await updateTexto(id, { title: newTitle, content: newContent }, user?.uid);
      // Atualizar a lista local com o novo título e conteúdo
      const updatedTextos = textos.map(t =>
        t.id === id ? { ...t, title: newTitle, content: newContent } : t
      );
      setTextos(updatedTextos);

      // Atualizar também a lista filtrada
      setFilteredTextos(prev =>
        prev.map(t => t.id === id ? { ...t, title: newTitle, content: newContent } : t)
      );
    } catch (error) {
      console.error("Erro ao atualizar texto:", error);
      throw error;
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Textos</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie os textos disponíveis no sistema
              </p>
            </div>
            <Button onClick={() => setIsAddModalOpen(true)} className="shrink-0">
              <IconPlus className="size-4 mr-2" />
              Adicionar Texto
            </Button>
          </div>

          {/* Barra de pesquisa */}
          <div className="mb-6">
            <SearchBar
              onSearch={handleSearch}
              placeholder="Pesquisar por título..."
              className="max-w-full sm:max-w-md"
            />
          </div>

          {/* Seção de listagem */}
          <div className="mt-2">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              </div>
            ) : (
              <TextosTable
                textos={filteredTextos}
                onDelete={handleDelete}
                onEdit={handleEdit}
              />
            )}
          </div>

          {/* Modal de Adição */}
          <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
            <DialogContent className="sm:max-w-2xl md:max-w-3xl lg:max-w-4xl h-auto max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Adicionar Novo Texto</DialogTitle>
              </DialogHeader>
              <TextInput onSaveComplete={handleSaveComplete} />
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
