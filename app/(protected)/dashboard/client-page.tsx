"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout

import { ActivityFeed } from "@/components/activity-feed";

export default function ClientPage() {
  const { user, userProfile, profileLoading, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Evitar verificar antes que o carregamento do perfil esteja concluído
    if (loading || profileLoading) return;

    // Se o perfil estiver carregado e não estiver completo, redirecionar para a página de perfil
    if (user && userProfile && userProfile.profileCompleted === false) {
      console.log("Profile not complete, redirecting to complete-profile");
      // Usar setTimeout para evitar redirecionamentos muito rápidos
      const redirectTimeout = setTimeout(() => {
        router.push("/complete-profile");
      }, 300);

      return () => clearTimeout(redirectTimeout);
    }

    // Se o usuário não estiver autenticado, redirecionar para o login
    // Isso é um fallback, pois o RouteGuard já deve ter feito esse redirecionamento
    if (!user) {
      console.log("User not authenticated, redirecting to login");
      // Usar setTimeout para evitar redirecionamentos muito rápidos
      const redirectTimeout = setTimeout(() => {
        router.push("/login");
      }, 300);

      return () => clearTimeout(redirectTimeout);
    }
  }, [user, userProfile, loading, profileLoading, router]);

  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      <div className="px-4 lg:px-6">
        <ActivityFeed />
      </div>
    </div>
  );
}
