"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout
import { CondutorForm } from "@/components/condutor-form";
import { CondutorCard } from "@/components/condutor-card";

import { FinalizarServicoForm } from "@/components/finalizar-servico-form";
import { AbastecimentoForm } from "@/components/abastecimento-form";
import { IconCar } from "@tabler/icons-react";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast, showInfoToast } from "@/lib/toast-utils";
import { CondutoresFilter, FilterOptions } from "@/components/condutores-filter";

import {
  addCondutor,
  updateCondutor,
  deleteCondutor,
  listenToCondutores,
  finalizarServico,
  Condutor,
  Abastecimento
} from "@/services/condutores-service";

import {
  listenTo<PERSON><PERSON>turas,
  Viatura
} from "@/services/viaturas-service";

import { getSystemUsers } from "@/services/users-service";
import { getTeams } from "@/services/teams-service";
import { Team } from "@/types/team";

export default function ClientPage() {
  const { user, userProfile, loading, profileLoading, isAdmin, userTeamId } = useAuth();
  const router = useRouter();

  // Estados para gerenciar os dados
  const [isLoading, setIsLoading] = useState(true);
  const [condutores, setCondutores] = useState<Condutor[]>([]);
  const [allCondutores, setAllCondutores] = useState<Condutor[]>([]);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    nome: "",
    viaturaId: "all",
    dataInicio: null,
    dataFim: null,
    teamId: "all",
    userId: "all",
    status: "all",
    hasAbastecimento: null,
  });
  const [viaturas, setViaturas] = useState<Viatura[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [systemUsers, setSystemUsers] = useState<{id: string, name: string}[]>([]);

  // Estados para gerenciar modais e edição
  const [isFinalizarModalOpen, setIsFinalizarModalOpen] = useState(false);
  const [isAbastecerModalOpen, setIsAbastecerModalOpen] = useState(false);
  const [selectedCondutor, setSelectedCondutor] = useState<Condutor | null>(null);
  const [editingCondutor, setEditingCondutor] = useState<Condutor | null>(null);
  const [formKey, setFormKey] = useState("initial"); // Chave para forçar a recriação do formulário

  // Configurar listeners para mudanças nos dados
  useEffect(() => {
    if (loading || profileLoading) return;

    if (!user) {
      router.push("/login");
      return;
    }

    // Listener para condutores
    let unsubscribeCondutores;

    // Log para debug
    console.log("Configuração de listener para condutores:");
    console.log("- isAdmin:", isAdmin);
    console.log("- userTeamId:", userTeamId);
    console.log("- userId:", user.uid);

    if (!isAdmin && user) {
      if (userTeamId) {
        // Se não for admin mas tiver teamId, filtrar por equipa
        console.log("Configurando listener de condutores para equipa:", userTeamId);
        unsubscribeCondutores = listenToCondutores((data) => {
          setAllCondutores(data);
          setIsLoading(false);
        }, userTeamId);
      } else {
        // Se não tiver teamId, filtrar por userId para ver registros onde o usuário é o responsável
        console.log("Configurando listener de condutores para usuário:", user.uid);
        unsubscribeCondutores = listenToCondutores((data) => {
          setAllCondutores(data);
          setIsLoading(false);
        }, undefined, user.uid);
      }
    } else if (isAdmin) {
      // Se for admin, ver todos os registos usando o teamId "admin"
      console.log("Configurando listener de condutores para admin");
      unsubscribeCondutores = listenToCondutores((data) => {
        setAllCondutores(data);
        setIsLoading(false);
      }, "admin");
    } else {
      // Fallback para caso o usuário não esteja definido
      console.warn("Usuário não está definido");
      setAllCondutores([]);
      setIsLoading(false);
      // Criar um unsubscribe dummy para evitar erros
      unsubscribeCondutores = () => {};
    }

    // Listener para viaturas
    const unsubscribeViaturas = listenToViaturas((data) => {
      setViaturas(data);
    });

    return () => {
      unsubscribeCondutores();
      unsubscribeViaturas();
    };
  }, [user, loading, profileLoading, router]);

  // Buscar equipas e usuários para os filtros de administrador
  useEffect(() => {
    if (isAdmin && !loading && user) {
      // Buscar equipas
      getTeams().then(teamsData => {
        setTeams(teamsData);
      }).catch(error => {
        console.error("Erro ao buscar equipas:", error);
      });

      // Buscar usuários
      getSystemUsers().then(usersData => {
        const formattedUsers = usersData.map(user => ({
          id: user.id,
          name: user.name || "Usuário sem nome"
        }));
        setSystemUsers(formattedUsers);
      }).catch(error => {
        console.error("Erro ao buscar usuários:", error);
      });
    }
  }, [isAdmin, loading, user]);

  // Aplicar filtros aos condutores
  useEffect(() => {
    if (!allCondutores.length) {
      setCondutores([]);
      return;
    }

    let filteredData = [...allCondutores];

    // Filtrar por nome do condutor
    if (filterOptions.nome) {
      const searchTerm = filterOptions.nome.toLowerCase().trim();

      filteredData = filteredData.filter(condutor => {
        // Verificar o nome do condutor (userName)
        if (condutor.userName && condutor.userName.toLowerCase().includes(searchTerm)) {
          return true;
        }

        // Verificar o ID do usuário
        if (condutor.userId && condutor.userId.toLowerCase().includes(searchTerm)) {
          return true;
        }

        // Verificar a informação da viatura
        if (condutor.viaturaInfo && condutor.viaturaInfo.toLowerCase().includes(searchTerm)) {
          return true;
        }

        // Verificar a matrícula da viatura (se disponível)
        const viatura = viaturas.find(v => v.id === condutor.viaturaId);
        if (viatura && viatura.matricula && viatura.matricula.toLowerCase().includes(searchTerm)) {
          return true;
        }

        // Verificar o modelo da viatura (se disponível)
        if (viatura && viatura.modelo && viatura.modelo.toLowerCase().includes(searchTerm)) {
          return true;
        }

        return false;
      });
    }

    // Filtrar por viatura
    if (filterOptions.viaturaId && filterOptions.viaturaId !== 'all') {
      filteredData = filteredData.filter(condutor =>
        condutor.viaturaId === filterOptions.viaturaId
      );
    }

    // Filtrar por data de início
    if (filterOptions.dataInicio) {
      const startDate = new Date(filterOptions.dataInicio);
      startDate.setHours(0, 0, 0, 0);

      filteredData = filteredData.filter(condutor => {
        if (!condutor.dataHoraInicio) return false;
        const condutorDate = new Date(condutor.dataHoraInicio);
        return condutorDate >= startDate;
      });
    }

    // Filtrar por data de fim
    if (filterOptions.dataFim) {
      const endDate = new Date(filterOptions.dataFim);
      endDate.setHours(23, 59, 59, 999);

      filteredData = filteredData.filter(condutor => {
        if (!condutor.dataHoraInicio) return false;
        const condutorDate = new Date(condutor.dataHoraInicio);
        return condutorDate <= endDate;
      });
    }

    // Filtros adicionais para administradores
    if (isAdmin) {
      // Filtrar por equipa
      if (filterOptions.teamId && filterOptions.teamId !== 'all') {
        filteredData = filteredData.filter(condutor => {
          if (filterOptions.teamId === 'admin') {
            return condutor.teamId === 'admin' || !condutor.teamId;
          }
          return condutor.teamId === filterOptions.teamId;
        });
      }

      // Filtrar por responsável (userId)
      if (filterOptions.userId && filterOptions.userId !== 'all') {
        filteredData = filteredData.filter(condutor =>
          condutor.userId === filterOptions.userId
        );
      }

      // Filtrar por status (ativo/finalizado)
      if (filterOptions.status && filterOptions.status !== 'all') {
        filteredData = filteredData.filter(condutor => {
          if (filterOptions.status === 'active') {
            return condutor.dataHoraFim === null;
          } else if (filterOptions.status === 'finished') {
            return condutor.dataHoraFim !== null;
          }
          return true;
        });
      }

      // Filtrar por abastecimento
      if (filterOptions.hasAbastecimento !== null) {
        filteredData = filteredData.filter(condutor => {
          if (filterOptions.hasAbastecimento === true) {
            return condutor.abastecimento !== null;
          } else {
            return condutor.abastecimento === null;
          }
        });
      }


    }

    // Ordenar por data mais recente primeiro
    filteredData.sort((a, b) => {
      if (!a.dataHoraInicio) return 1;
      if (!b.dataHoraInicio) return -1;
      return new Date(b.dataHoraInicio).getTime() - new Date(a.dataHoraInicio).getTime();
    });

    setCondutores(filteredData);
  }, [allCondutores, filterOptions.nome, filterOptions.viaturaId, filterOptions.dataInicio, filterOptions.dataFim,
     filterOptions.teamId, filterOptions.userId, filterOptions.status, filterOptions.hasAbastecimento,
     viaturas, isAdmin]);

  // Função para adicionar ou atualizar um condutor
  const handleAddCondutor = async (condutorData: Omit<Condutor, "id" | "createdAt" | "updatedAt">) => {
    // Adicionar o ID da equipa
    if (!isAdmin && userTeamId) {
      // Se não for admin mas tiver teamId, usar o teamId do usuário
      condutorData.teamId = userTeamId;
      console.log("Adicionando teamId ao condutor:", userTeamId);
    } else if (isAdmin) {
      // Se for admin, usar "admin" como teamId
      condutorData.teamId = "admin";
      console.log("Adicionando teamId 'admin' para o condutor (usuário é admin)");
    } else {
      console.log("Não foi definido teamId para condutor. isAdmin:", isAdmin, "userTeamId:", userTeamId);
    }

    // Log detalhado dos dados do condutor
    console.log("Dados do condutor a serem salvos:", {
      userId: condutorData.userId,
      userName: condutorData.userName,
      viaturaId: condutorData.viaturaId,
      teamId: condutorData.teamId || "[não definido]",
    });
    // Criar o toast de carregamento fora do bloco try para garantir que possamos descartá-lo em qualquer situação
    const loadingToast = showLoadingToast(editingCondutor ? "A atualizar registo..." : "A adicionar registo...");

    try {
      if (editingCondutor) {
        // Atualizar condutor existente
        // Verificar se o usuário foi alterado e obter o teamId do novo usuário
        let newTeamId = condutorData.teamId;

        // Se o usuário foi alterado, precisamos obter o teamId do novo usuário
        if (condutorData.userId !== editingCondutor.userId) {
          try {
            // Obter usuários para encontrar o teamId do novo usuário
            const users = await getSystemUsers();
            const selectedUser = users.find(u => u.id === condutorData.userId);
            if (selectedUser?.teamId) {
              console.log(`Novo teamId encontrado para o usuário: ${selectedUser.teamId}`);
              newTeamId = selectedUser.teamId;
            }
          } catch (userError) {
            console.error("Erro ao obter informações do novo usuário:", userError);
          }
        }

        // Se somos admin, garantir que temos um teamId válido ou uma string vazia
        // Isso evita problemas quando o admin não tem equipa associada
        if (isAdmin) {
          newTeamId = newTeamId || "admin"; // Usar "admin" como teamId para administradores
        } else {
          newTeamId = newTeamId || editingCondutor.teamId || ""; // Usar o teamId do novo usuário, ou o original, ou string vazia
        }

        const updateData = {
          ...condutorData,
          teamId: newTeamId
        };

        console.log("Atualizando condutor com ID:", editingCondutor.id);
        console.log("TeamId original:", editingCondutor.teamId || "[não definido]");
        console.log("TeamId a ser usado:", updateData.teamId || "[não definido]");

        await updateCondutor(editingCondutor.id, updateData, user?.uid);
        showSuccessToast("Registo atualizado");
        setEditingCondutor(null);
        // Gerar uma nova chave para forçar a recriação do formulário
        setFormKey(Date.now().toString());
      } else {
        // Adicionar novo condutor
        await addCondutor(condutorData);
        showSuccessToast("Registo adicionado");
        // Gerar uma nova chave para forçar a recriação do formulário
        setFormKey(Date.now().toString());
      }
    } catch (error) {
      console.error("Erro ao salvar condutor:", error);
      showErrorToast("Erro ao guardar registo");
    } finally {
      // Garantir que o toast de carregamento seja sempre descartado, mesmo em caso de erro
      dismissToast(loadingToast);
    }
  };



  // Função para excluir um condutor
  const handleDeleteCondutor = async (id: string) => {
    if (!user?.uid) {
      console.error("Usuário não autenticado");
      showErrorToast("Usuário não autenticado");
      return;
    }

    try {
      await deleteCondutor(id, user.uid);
      showSuccessToast("Registo excluído");
    } catch (error) {
      console.error("Erro ao excluir condutor:", error);
      showErrorToast("Erro ao excluir registo");
    }
  };

  // Função para editar um condutor
  const handleEditCondutor = (condutor: Condutor) => {
    // Se já estiver editando, perguntar se deseja descartar as alterações
    if (editingCondutor && editingCondutor.id !== condutor.id) {
      if (!confirm("Deseja descartar as alterações no registro atual?")) {
        return;
      }
    }

    // Atualizar o estado de edição
    setEditingCondutor(condutor);
    // Gerar uma nova chave para forçar a recriação do formulário
    setFormKey(Date.now().toString());
    // Rolar para o formulário
    window.scrollTo({ top: 0, behavior: "smooth" });

    // Mostrar mensagem de ajuda
    showInfoToast("Editando registo");
  };

  // Função para cancelar a edição
  const handleCancelEdit = () => {
    // Limpar o estado de edição
    setEditingCondutor(null);

    // Forçar uma atualização da lista de condutores para garantir que os dados exibidos estão atualizados
    const currentFilters = {...filterOptions};
    setFilterOptions(currentFilters);

    // Forçar uma nova renderização do formulário para garantir que ele seja completamente resetado
    // Isso é feito adicionando uma chave única ao componente
    setFormKey(Date.now().toString());
  };

  // Função para abrir o modal de finalização
  const handleOpenFinalizarModal = (condutor: Condutor) => {
    setSelectedCondutor(condutor);
    setIsFinalizarModalOpen(true);
  };

  // Função para abrir o modal de abastecimento
  const handleOpenAbastecerModal = (condutor: Condutor) => {
    setSelectedCondutor(condutor);
    setIsAbastecerModalOpen(true);
  };

  // Função para finalizar um serviço
  const handleFinalizarServico = async (
    id: string,
    quilometrosFinais: number,
    dataHoraFim: Date,
    abastecimento: Abastecimento | null
  ) => {
    try {
      await finalizarServico(
        id,
        quilometrosFinais,
        dataHoraFim,
        abastecimento,
        userProfile?.registrationNumber || "N/A"
      );
      showSuccessToast("Serviço finalizado");
    } catch (error) {
      console.error("Erro ao finalizar serviço:", error);
      showErrorToast("Erro ao finalizar serviço");
      throw error;
    }
  };

  // Função para registrar ou editar um abastecimento
  const handleRegistrarAbastecimento = async (id: string, abastecimento: Abastecimento) => {
    // Criar o toast de carregamento fora do bloco try para garantir que possamos descartá-lo em qualquer situação
    const loadingToast = showLoadingToast("A guardar abastecimento...");

    try {
      // Obter o condutor atual
      const condutor = condutores.find(c => c.id === id);

      // Se o condutor não for encontrado na lista local, mas somos admin, tentamos atualizar mesmo assim
      if (!condutor && !isAdmin) {
        showErrorToast("Condutor não encontrado");
        return;
      }

      // Preparar os dados para atualização
      const updateData: Partial<Omit<Condutor, "id" | "createdAt" | "updatedAt">> = {
        abastecimento,
        updatedBy: userProfile?.registrationNumber || "N/A"
      };

      // Se somos admin e o condutor não foi encontrado localmente, adicionamos mais informações
      if (isAdmin && !condutor) {
        console.log("Admin está atualizando um condutor que não está na lista local");
        // Adicionar userId do admin como fallback
        updateData.userId = user?.uid || "";
        updateData.userName = userProfile?.fullName || "Admin";
      }

      // Atualizar o condutor com os dados de abastecimento
      await updateCondutor(id, updateData, user?.uid);

      showSuccessToast("Abastecimento registado");
    } catch (error) {
      console.error("Erro ao registrar abastecimento:", error);
      showErrorToast("Erro ao registar abastecimento");
    } finally {
      // Garantir que o toast de carregamento seja sempre descartado, mesmo em caso de erro
      dismissToast(loadingToast);
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Condutores</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie os registos de condutores e viaturas
              </p>
            </div>
          </div>

          {/* Formulário de Condutor */}
          <div className="mb-8 border rounded-lg p-4 bg-card">
            <CondutorForm
              key={formKey} // Usar a chave para forçar a recriação do componente
              onSaveComplete={handleAddCondutor}
              initialData={editingCondutor || undefined}
              onCancel={handleCancelEdit}
            />
          </div>

          {/* Listagem de Registos */}
          <div className="mt-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
              <h2 className="text-lg font-semibold">Registos de Condutores</h2>
            </div>

            {/* Filtro de Condutores */}
            <CondutoresFilter
              viaturas={viaturas}
              onFilter={setFilterOptions}
              isAdmin={isAdmin}
              teams={teams}
              users={systemUsers}
            />

            {isLoading ? (
              <div className="flex min-h-[200px] items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-muted-foreground">A carregar registos...</p>
                </div>
              </div>
            ) : condutores.length === 0 ? (
              <div className="flex min-h-[200px] items-center justify-center border rounded-lg">
                <div className="text-center p-6">
                  <IconCar className="size-10 text-muted-foreground/50 mx-auto mb-4" />
                  <h3 className="text-lg font-medium">Nenhum registo encontrado</h3>
                  <p className="text-muted-foreground mt-2">
                    Adicione um novo registo de condutor utilizando o formulário acima.
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {condutores.map((condutor) => (
                  <CondutorCard
                    key={condutor.id}
                    condutor={condutor}
                    onEdit={handleEditCondutor}
                    onDelete={handleDeleteCondutor}
                    onFinalize={handleOpenFinalizarModal}
                    onAbastecer={handleOpenAbastecerModal}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>



      {/* Modal para finalizar serviço */}
      <FinalizarServicoForm
        open={isFinalizarModalOpen}
        onOpenChange={setIsFinalizarModalOpen}
        condutor={selectedCondutor}
        onFinalize={handleFinalizarServico}
      />

      {/* Modal para registrar abastecimento */}
      <AbastecimentoForm
        open={isAbastecerModalOpen}
        onOpenChange={setIsAbastecerModalOpen}
        condutor={selectedCondutor}
        onSave={handleRegistrarAbastecimento}
      />
    </div>
  );
}
