"use client";

import { useState } from "react";
import { showErrorToast } from "@/lib/toast-utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/auth-context";
import { Team } from "@/types/team";

interface TeamFormProps {
  initialData?: Team;
  onSaveComplete: (teamData: any) => void;
}

export function TeamForm({ initialData, onSaveComplete }: TeamFormProps) {
  const { user } = useAuth();
  const [name, setName] = useState(initialData?.name || "");
  const [description, setDescription] = useState(initialData?.description || "");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      showErrorToast("Nome da equipa é obrigatório");
      return;
    }

    setIsSubmitting(true);

    try {
      const teamData = {
        name,
        description,
        leaders: initialData?.leaders || [],
        members: initialData?.members || []
      };

      onSaveComplete(teamData);
    } catch (error) {
      console.error("Erro ao salvar equipa:", error);
      showErrorToast("Não foi possível guardar os dados da equipa");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Nome da Equipa</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Digite o nome da equipa"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Digite uma descrição para a equipa (opcional)"
          rows={3}
        />
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "A guardar..." : "Guardar"}
        </Button>
      </div>
    </form>
  );
}
