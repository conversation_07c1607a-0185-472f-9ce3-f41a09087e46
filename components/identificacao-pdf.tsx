"use client";

import React, { useState } from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Font,
  Image,
  pdf
} from '@react-pdf/renderer';
import { Identificacao } from '@/services/identificacoes-service';
import { Button } from '@/components/ui/button';
import { IconFileDownload } from '@tabler/icons-react';

// Não precisamos registrar fontes externas, vamos usar as fontes padrão
// O react-pdf já vem com algumas fontes padrão como 'Helvetica', 'Times-Roman', etc.

// Estilos para o PDF
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
    position: 'relative',
  },
  header: {
    marginBottom: 20,
    borderBottom: '1px solid #EEEEEE',
    paddingBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'column',
  },
  headerRight: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#111827',
  },
  subtitle: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 10,
  },
  section: {
    marginBottom: 15,
    marginTop: 0,
    padding: 10,
    borderRadius: 5,
    border: '1px solid #EEEEEE',
    breakInside: 'avoid',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#111827',
    borderBottom: '1px solid #EEEEEE',
    paddingBottom: 5,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 5,
  },
  column: {
    flexDirection: 'column',
    flexBasis: '50%',
    marginBottom: 8,
  },
  columnThird: {
    flexDirection: 'column',
    flexBasis: '33%',
    marginBottom: 8,
  },
  label: {
    fontSize: 10,
    color: '#6B7280',
    marginBottom: 2,
  },
  value: {
    fontSize: 11,
    color: '#111827',
  },
  footer: {
    position: 'absolute',
    bottom: 15,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 8,
    color: '#9CA3AF',
    borderTop: '1px solid #EEEEEE',
    paddingTop: 8,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 8,
    color: '#9CA3AF',
  },
  logo: {
    width: 120,
    marginBottom: 10,
  },
  fullWidth: {
    width: '100%',
  },
  observations: {
    marginTop: 5,
    padding: 8,
    borderRadius: 4,
    fontSize: 10,
    border: '1px solid #EEEEEE',
  },
  documentInfo: {
    fontSize: 10,
    color: '#6B7280',
    marginTop: 5,
  },
  badge: {
    backgroundColor: '#E6F0FF', // Versão mais clara do Picton Blue
    borderRadius: 4,
    padding: '4px 8px',
    color: '#3B7DD8', // Picton Blue 600 aproximado em hex
    fontSize: 10,
    alignSelf: 'flex-start',
    marginBottom: 5,
  },
  // Estilo da marca d'água removido
});

// Componente do PDF
const IdentificacaoPDF = ({ identificacao }: { identificacao: Identificacao }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Removida a marca d'água conforme solicitado */}

      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>Relatório de Identificação</Text>
          <Text style={styles.subtitle}>
            Documento gerado em {new Date().toLocaleDateString('pt-PT')} às {new Date().toLocaleTimeString('pt-PT')}
          </Text>
        </View>
        <View style={styles.headerRight}>
          <View style={styles.badge}>
            <Text>Ref: {identificacao.id.substring(0, 8).toUpperCase()}</Text>
          </View>
          <Text style={styles.documentInfo}>Documento oficial</Text>
        </View>
      </View>

      {/* Dados Pessoais */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Dados Pessoais</Text>
        <View style={styles.row}>
          <View style={styles.fullWidth}>
            <Text style={styles.label}>Nome Completo</Text>
            <Text style={styles.value}>{identificacao.nomeCompleto || '-'}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.columnThird}>
            <Text style={styles.label}>Documento</Text>
            <Text style={styles.value}>{identificacao.documentoIdentificacao || '-'}</Text>
          </View>
          <View style={styles.columnThird}>
            <Text style={styles.label}>Número</Text>
            <Text style={styles.value}>{identificacao.numeroCC || '-'}</Text>
          </View>
          <View style={styles.columnThird}>
            <Text style={styles.label}>Validade</Text>
            <Text style={styles.value}>{identificacao.validadeCC || '-'}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.columnThird}>
            <Text style={styles.label}>NIF</Text>
            <Text style={styles.value}>{identificacao.nif || '-'}</Text>
          </View>
          <View style={styles.columnThird}>
            <Text style={styles.label}>Nacionalidade</Text>
            <Text style={styles.value}>{identificacao.nacionalidade || '-'}</Text>
          </View>
          <View style={styles.columnThird}>
            <Text style={styles.label}>Data de Nascimento</Text>
            <Text style={styles.value}>{identificacao.dataNascimento || '-'}</Text>
          </View>
        </View>
      </View>

      {/* Contato e Endereço */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Contato e Endereço</Text>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.label}>Telefone</Text>
            <Text style={styles.value}>{identificacao.telefone || '-'}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.fullWidth}>
            <Text style={styles.label}>Morada</Text>
            <Text style={styles.value}>{identificacao.morada || '-'}</Text>
          </View>
        </View>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.label}>Código Postal</Text>
            <Text style={styles.value}>{identificacao.codigoPostal || '-'}</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.label}>Freguesia</Text>
            <Text style={styles.value}>{identificacao.freguesia || '-'}</Text>
          </View>
        </View>
      </View>

      {/* Informações Complementares */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Informações Complementares</Text>
        <View style={styles.row}>
          <View style={styles.columnThird}>
            <Text style={styles.label}>Estado Civil</Text>
            <Text style={styles.value}>{identificacao.estadoCivil || '-'}</Text>
          </View>
          <View style={styles.columnThird}>
            <Text style={styles.label}>Profissão</Text>
            <Text style={styles.value}>{identificacao.profissao || '-'}</Text>
          </View>
          <View style={styles.columnThird}>
            <Text style={styles.label}>Escolaridade</Text>
            <Text style={styles.value}>{identificacao.escolaridade || '-'}</Text>
          </View>
        </View>
      </View>

      {/* Filiação */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Filiação</Text>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.label}>Nome da Mãe</Text>
            <Text style={styles.value}>{identificacao.nomeMae || '-'}</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.label}>Nome do Pai</Text>
            <Text style={styles.value}>{identificacao.nomePai || '-'}</Text>
          </View>
        </View>
      </View>

      {/* Dados da Ocorrência */}
      <View style={[styles.section, { marginBottom: 30 }]}>
        <Text style={styles.sectionTitle}>Dados da Ocorrência</Text>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.label}>Data da Ocorrência</Text>
            <Text style={styles.value}>{identificacao.dataOcorrencia || '-'}</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.label}>Hora da Ocorrência</Text>
            <Text style={styles.value}>{identificacao.horaOcorrencia || '-'}</Text>
          </View>
        </View>
        <View style={[styles.row, { marginBottom: 0 }]}>
          <View style={styles.fullWidth}>
            <Text style={styles.label}>Local da Ocorrência</Text>
            <Text style={styles.value}>{identificacao.localOcorrencia || '-'}</Text>
          </View>
        </View>
      </View>

      {/* Observações */}
      {identificacao.observacoes && (
        <View style={[styles.section, { marginTop: 15 }]}>
          <Text style={styles.sectionTitle}>Observações</Text>
          <View style={styles.observations}>
            <Text>{identificacao.observacoes}</Text>
          </View>
        </View>
      )}

      {/* Espaçador para evitar que o rodapé fique muito próximo da última seção */}
      <View style={{ height: 40 }}></View>

      <View fixed style={styles.footer}>
        <Text style={styles.footerText}>Sistema de Gestão</Text>
        <Text style={styles.footerText} render={({ pageNumber, totalPages }) => (
          `Página ${pageNumber} de ${totalPages}`
        )} />
        <Text style={styles.footerText}>ID: {identificacao.id.substring(0, 8)}</Text>
      </View>
    </Page>
  </Document>
);

// Componente de botão para download do PDF
export const IdentificacaoPDFDownloadButton = ({ identificacao }: { identificacao: Identificacao }) => {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGeneratePDF = async () => {
    try {
      setIsGenerating(true);

      // Usar o PDFDownloadLink de forma programática
      const blob = await pdf(<IdentificacaoPDF identificacao={identificacao} />).toBlob();
      const url = URL.createObjectURL(blob);

      // Criar um link temporário para download
      const link = document.createElement('a');
      link.href = url;
      link.download = `identificacao-${identificacao.nomeCompleto.replace(/\s+/g, '-').toLowerCase()}.pdf`;
      link.click();

      // Limpar o URL criado
      setTimeout(() => URL.revokeObjectURL(url), 100);

      // Mostrar mensagem de sucesso
      import('sonner').then(({ toast }) => {
        toast.success('PDF gerado com sucesso', {
          description: 'O documento foi baixado para o seu dispositivo.',
        });
      });
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);

      // Mostrar mensagem de erro
      import('sonner').then(({ toast }) => {
        toast.error('Erro ao gerar PDF', {
          description: 'Ocorreu um erro ao gerar o documento. Tente novamente.',
        });
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      disabled={isGenerating}
      className="flex items-center gap-1 h-8 px-2 text-xs sm:text-sm whitespace-nowrap"
      onClick={handleGeneratePDF}
    >
      <IconFileDownload className="size-4 mr-1" />
      <span className="hidden xs:inline">{isGenerating ? 'A gerar PDF...' : 'Exportar PDF'}</span>
      <span className="xs:hidden">{isGenerating ? 'A gerar...' : 'PDF'}</span>
    </Button>
  );
};
