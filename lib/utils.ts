import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Formatar tamanho de arquivo para exibição amigável
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// Formatar data para exibição amigável
export function formatDate(date: any): string {
  if (!(date instanceof Date)) {
    // Se for um timestamp do Firestore, converter para Date
    if (date && typeof date.toDate === 'function') {
      date = date.toDate();
    } else if (date) {
      date = new Date(date);
    } else {
      return "";
    }
  }

  return date.toLocaleDateString("pt-PT", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  });
}
