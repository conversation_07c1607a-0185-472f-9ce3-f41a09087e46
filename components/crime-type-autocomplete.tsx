"use client";

import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { IconSearch, IconX, IconPlus, IconAlertTriangle } from "@tabler/icons-react";
import { cn } from "@/lib/utils";
import { searchCrimeTypes, CrimeType, addCrimeType } from "@/services/crime-types-service";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CrimeTypeAutocompleteProps {
  value: string;
  onChange: (value: string, crimeTypeId?: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  readOnly?: boolean; // Adicionar propriedade para modo somente leitura
}

export function CrimeTypeAutocomplete({
  value,
  onChange,
  placeholder = "Introduza o tipo de crime",
  disabled = false,
  className,
  readOnly = true, // Por padrão, o componente é somente leitura
}: CrimeTypeAutocompleteProps) {
  const { user } = useAuth();
  // Usar o valor externo como fonte da verdade
  const [inputValue, setInputValue] = useState(value);
  const [results, setResults] = useState<CrimeType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [debouncedValue, setDebouncedValue] = useState("");
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const [newCrimeCategory, setNewCrimeCategory] = useState<string>("");
  // Estado para controlar se o utilizador acabou de selecionar um item
  const [justSelected, setJustSelected] = useState(false);
  // Estado para controlar se o input foi modificado pelo utilizador
  const [userModified, setUserModified] = useState(false);
  const resultsRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Sincronizar o inputValue com o valor externo quando ele mudar
  useEffect(() => {
    if (value !== inputValue && !userModified) {
      setInputValue(value);
    }
  }, [value]);

  // Debounce input value apenas quando modificado pelo utilizador
  useEffect(() => {
    if (!userModified) return;

    const timer = setTimeout(() => {
      setDebouncedValue(inputValue);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, [inputValue, userModified]);

  // Fetch results when debounced value changes
  useEffect(() => {
    if (!debouncedValue || debouncedValue.length < 2) {
      setResults([]);
      setShowResults(false);
      return;
    }

    const fetchCrimeTypes = async () => {
      setIsLoading(true);
      try {
        const crimeTypeResults = await searchCrimeTypes(debouncedValue);

        if (crimeTypeResults.length > 0) {
          setResults(crimeTypeResults);
          setShowResults(true);
        } else {
          setResults([]);
          setShowResults(false);
        }
      } catch (error) {
        console.error("Erro ao buscar tipos de crime:", error);
        setResults([]);
        setShowResults(false);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCrimeTypes();
  }, [debouncedValue]);

  // Handle click/touch outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (
        resultsRef.current &&
        !resultsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowResults(false);
        setResults([]);
      }
    };

    // Adicionar um listener para quando o input perder o foco
    const handleBlur = (event: FocusEvent) => {
      // Em dispositivos móveis, adicionamos um pequeno atraso para permitir que o toque seja processado
      setTimeout(() => {
        // Verificar se o elemento que está recebendo o foco está dentro dos resultados
        const relatedTarget = event.relatedTarget as Node;
        if (resultsRef.current && resultsRef.current.contains(relatedTarget)) {
          // Se o foco está indo para um elemento dentro dos resultados, não fechar
          return;
        }

        // Fechar os resultados imediatamente
        setShowResults(false);
        setResults([]);

        // Em modo somente leitura, verificar se o valor atual é válido
        if (readOnly && inputValue && !justSelected) {
          // Verificar se o valor atual corresponde a um tipo de crime existente
          const matchingCrimeType = results.find(result =>
            result.name.toLowerCase() === inputValue.toLowerCase() ||
            result.name.toLowerCase().includes(inputValue.toLowerCase())
          );

          if (matchingCrimeType) {
            // Se encontrou uma correspondência, usar esse valor
            setInputValue(matchingCrimeType.name);
            onChange(matchingCrimeType.name, matchingCrimeType.id);
          } else {
            // Se não encontrou, restaurar o valor original ou limpar
            setInputValue(value || "");
            if (!value) {
              onChange("", undefined);
            }
          }
        }
      }, 100); // Pequeno atraso para dispositivos móveis
    };

    // Adicionar listeners para mouse e toque
    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("touchstart", handleClickOutside);
    inputRef.current?.addEventListener("blur", handleBlur);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchstart", handleClickOutside);
      inputRef.current?.removeEventListener("blur", handleBlur);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setUserModified(true);
    setJustSelected(false);

    if (newValue === "") {
      onChange("", undefined);
      setResults([]);
      setShowResults(false);
      setDebouncedValue("");
    } else if (readOnly) {
      // Em modo somente leitura, não notificar o componente pai até que um item válido seja selecionado
      // O valor só será atualizado quando o usuário selecionar um item da lista
    } else {
      // Em modo de edição, notificar o componente pai com o valor digitado
      onChange(newValue, undefined);
    }
  };

  const handleSelectCrimeType = (crimeType: CrimeType) => {
    // Atualizar o valor do input e notificar o componente pai
    const newValue = crimeType.name;

    // Usar setTimeout para garantir que a seleção funcione em dispositivos iOS
    setTimeout(() => {
      setInputValue(newValue);
      // Passar o ID do tipo de crime junto com o nome
      onChange(newValue, crimeType.id);

      // Forçar o fechamento dos resultados
      setShowResults(false);
      setResults([]);

      // Marcar que acabou de selecionar um item e resetar o estado de modificação
      setJustSelected(true);
      setUserModified(false);

      // Limpar o valor debounced para evitar nova pesquisa
      setDebouncedValue("");

      // Remover o foco do input imediatamente
      if (inputRef.current) {
        inputRef.current.blur();
      }

      // Adicionar um pequeno atraso antes de permitir que o input mostre resultados novamente
      setTimeout(() => {
        setJustSelected(false);
      }, 300);
    }, 10); // Pequeno atraso para iOS
  };

  const handleOpenConfirmDialog = () => {
    if (!inputValue.trim() || !user) return;
    setShowConfirmDialog(true);
    setShowResults(false);
  };

  const handleAddNewCrimeType = async () => {
    if (!inputValue.trim() || !user) return;

    setIsLoading(true);
    try {
      const crimeTypeId = await addCrimeType({
        name: inputValue.trim(),
        category: newCrimeCategory === "sem_categoria" ? undefined : newCrimeCategory,
        createdBy: user.uid,
      });

      onChange(inputValue.trim(), crimeTypeId);
      setShowResults(false);
      setShowConfirmDialog(false);
      setNewCrimeCategory("");
      toast.success("Tipo de crime adicionado com sucesso");
    } catch (error) {
      console.error("Erro ao adicionar novo tipo de crime:", error);
      toast.error("Erro ao adicionar tipo de crime");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setInputValue("");
    onChange("", undefined);
    setResults([]);
    setShowResults(false);
    inputRef.current?.focus();
  };

  return (
    <div className="relative">
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          className={cn("pr-8", className)}
          onFocus={() => {
            // Se acabou de selecionar um item, não mostrar os resultados novamente
            if (justSelected) {
              return;
            }

            // Resetar o estado de modificação quando o input recebe foco
            // para permitir que o usuário digite algo novo
            setUserModified(true);
          }}
        />
        {inputValue && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="absolute right-0 top-0 h-full px-3 py-2"
            onClick={handleClear}
            disabled={disabled}
          >
            <IconX className="size-4" />
            <span className="sr-only">Limpar</span>
          </Button>
        )}
      </div>

      {/* Forçar o controle de visibilidade apenas pelo estado showResults */}
      {showResults && (
        <div
          ref={resultsRef}
          className="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-popover p-1 shadow-md max-w-[95vw] sm:max-w-full"
        >
          <div className="text-xs text-muted-foreground px-2 py-1.5">
            {results.length > 0 ? "Resultados encontrados" : "Nenhum resultado encontrado"}
          </div>

          {/* Área de resultados com suporte a scroll em dispositivos móveis */}
          <div className="touch-pan-y">
            {results.map((result, index) => (
              <div
                key={index}
                className="flex w-full items-center justify-between group"
              >
                <button
                  className="flex flex-1 items-center px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground rounded-sm text-left overflow-hidden"
                  onClick={() => handleSelectCrimeType(result)}
                  type="button"
                >
                <IconSearch className="mr-2 size-3.5 text-muted-foreground flex-shrink-0" />
                <span className="truncate">{result.name}</span>
                {result.category && (
                  <span className="ml-1 text-xs text-muted-foreground truncate">
                    ({result.category})
                  </span>
                )}
              </button>

            </div>
          ))}

          </div>

          {/* Opção para adicionar novo tipo de crime (apenas se não existir e não estiver em modo somente leitura) */}
          {!readOnly && inputValue.trim() && !results.some(result =>
            result.name.toLowerCase() === inputValue.trim().toLowerCase()
          ) && (
            <button
              className="flex w-full items-center px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground rounded-sm text-left border-t mt-1 overflow-hidden"
              onClick={handleOpenConfirmDialog}
              type="button"
            >
              <IconPlus className="mr-2 size-3.5 text-primary flex-shrink-0" />
              <span className="truncate">Adicionar "{inputValue.trim()}"</span>
            </button>
          )}

          {/* Mensagem quando não há resultados e está em modo somente leitura */}
          {readOnly && inputValue.trim() && results.length === 0 && (
            <div className="px-2 py-1.5 text-sm text-muted-foreground">
              Nenhum tipo de crime encontrado. Por favor, selecione um tipo existente.
            </div>
          )}
        </div>
      )}

      {/* Diálogo de confirmação para adicionar novo tipo de crime */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md max-w-[95vw] w-full">
          <DialogHeader>
            <DialogTitle>Adicionar Novo Tipo de Crime</DialogTitle>
            <DialogDescription>
              Está prestes a adicionar um novo tipo de crime à base de dados.
              Por favor, confirme os detalhes abaixo.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="flex items-center gap-2">
              <IconAlertTriangle className="size-5 text-amber-500" />
              <p className="text-sm font-medium">Nome do tipo de crime:</p>
            </div>
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              className="font-medium"
            />

            <div className="mt-4">
              <Label className="text-sm font-medium">Categoria (opcional):</Label>
              <Select value={newCrimeCategory} onValueChange={setNewCrimeCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sem_categoria">Sem categoria</SelectItem>
                  <SelectItem value="Crimes contra as pessoas">Crimes contra as pessoas</SelectItem>
                  <SelectItem value="Crimes contra o património">Crimes contra o património</SelectItem>
                  <SelectItem value="Crimes contra a vida em sociedade">Crimes contra a vida em sociedade</SelectItem>
                  <SelectItem value="Crimes contra o Estado">Crimes contra o Estado</SelectItem>
                  <SelectItem value="Outros crimes">Outros crimes</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>Cancelar</Button>
            <Button onClick={handleAddNewCrimeType} disabled={isLoading}>
              {isLoading ? "A adicionar..." : "Confirmar e adicionar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {isLoading && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      )}
    </div>
  );
}
