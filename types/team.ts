/**
 * Interface for team data structure
 */
export interface Team {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  leaders: string[]; // Array of user IDs who are team leaders
  members: string[]; // Array of user IDs who are team members
}

/**
 * User roles in the system
 */
export enum UserRole {
  ADMIN = 'admin',
  TEAM_LEADER = 'team_leader',
  TEAM_MEMBER = 'team_member'
}

/**
 * Extended user profile with role and team information
 */
export interface ExtendedUserProfile {
  fullName?: string;
  registrationNumber?: string;
  category?: string;
  profileCompleted?: boolean;
  role?: UserRole;
  teamId?: string; // ID of the team the user belongs to
}
