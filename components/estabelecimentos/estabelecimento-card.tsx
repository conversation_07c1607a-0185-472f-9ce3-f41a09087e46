"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { IconPencil, IconTrash, IconMapPin, IconClock, IconPhone, IconWorld, IconBuildingStore, IconGlass, IconMusic } from "@tabler/icons-react";
import { Estabelecimento } from "@/types/estabelecimento";
import { toast } from "sonner";

interface EstabelecimentoCardProps {
  estabelecimento: Estabelecimento;
  onEdit: (estabelecimento: Estabelecimento) => void;
  onDelete: (id: string) => Promise<void>;
}

export function EstabelecimentoCard({ estabelecimento, onEdit, onDelete }: EstabelecimentoCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Função para lidar com a exclusão
  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onDelete(estabelecimento.id);
      toast.success("Estabelecimento excluído com sucesso");
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Erro ao excluir estabelecimento:", error);
      toast.error("Erro ao excluir estabelecimento");
    } finally {
      setIsDeleting(false);
    }
  };

  // Função para obter o ícone com base no tipo de estabelecimento
  const getIconByType = () => {
    switch (estabelecimento.tipo) {
      case "bar":
        return <IconGlass className="size-5 text-blue-500" />;
      case "discoteca":
        return <IconMusic className="size-5 text-purple-500" />;
      default:
        return <IconBuildingStore className="size-5 text-green-500" />;
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          {getIconByType()}
          <CardTitle className="text-lg">{estabelecimento.nome}</CardTitle>
        </div>
        <CardDescription className="capitalize">{estabelecimento.tipo}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="space-y-3 text-sm">
          <div className="flex items-start gap-2">
            <IconMapPin className="size-4 text-muted-foreground shrink-0 mt-0.5" />
            <span>{estabelecimento.endereco}</span>
          </div>
          
          {estabelecimento.descricao && (
            <p className="text-muted-foreground mt-2">{estabelecimento.descricao}</p>
          )}
          
          {estabelecimento.horarioFuncionamento && (
            <div className="flex items-start gap-2">
              <IconClock className="size-4 text-muted-foreground shrink-0 mt-0.5" />
              <span>{estabelecimento.horarioFuncionamento}</span>
            </div>
          )}
          
          {estabelecimento.telefone && (
            <div className="flex items-start gap-2">
              <IconPhone className="size-4 text-muted-foreground shrink-0 mt-0.5" />
              <span>{estabelecimento.telefone}</span>
            </div>
          )}
          
          {estabelecimento.website && (
            <div className="flex items-start gap-2">
              <IconWorld className="size-4 text-muted-foreground shrink-0 mt-0.5" />
              <a 
                href={estabelecimento.website.startsWith('http') ? estabelecimento.website : `https://${estabelecimento.website}`} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                {estabelecimento.website}
              </a>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2 border-t flex justify-between items-center">
        <div className="text-xs text-muted-foreground">
          {estabelecimento.criadoPor && `Registado por: ${estabelecimento.criadoPor}`}
        </div>
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEdit(estabelecimento)}
            title="Editar"
          >
            <IconPencil className="size-4" />
          </Button>
          
          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="text-destructive"
                title="Excluir"
              >
                <IconTrash className="size-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Excluir Estabelecimento</DialogTitle>
                <DialogDescription>
                  Tem certeza que deseja excluir o estabelecimento "{estabelecimento.nome}"? Esta ação não pode ser desfeita.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsDeleteDialogOpen(false)}
                  disabled={isDeleting}
                >
                  Cancelar
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={isDeleting}
                >
                  {isDeleting ? "A excluir..." : "Excluir"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardFooter>
    </Card>
  );
}
