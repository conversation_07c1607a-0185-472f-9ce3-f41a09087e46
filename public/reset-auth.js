// Script para limpar completamente o cache e forçar nova autenticação
(function() {
  // Função para limpar todos os caches e dados de autenticação
  const resetAuth = async () => {
    try {
      // 1. Limpar localStorage
      if (window.localStorage) {
        window.localStorage.clear();
      }

      // 2. Limpar sessionStorage
      if (window.sessionStorage) {
        window.sessionStorage.clear();
      }

      // 3. Limpar cookies
      document.cookie.split(';').forEach(function(c) {
        document.cookie = c.trim().split('=')[0] + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/';
      });
      
      // 4. Limpar caches de aplicação
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => {
            return caches.delete(cacheName);
          })
        );
      }

      // 5. Desregistrar Service Workers
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        for (let registration of registrations) {
          await registration.unregister();
        }
      }

      // 6. Limpar IndexedDB
      const databases = await window.indexedDB.databases();
      databases.forEach(db => {
        if (db.name) {
          window.indexedDB.deleteDatabase(db.name);
        }
      });
      
      // Redirecionar para a página de login após um pequeno atraso
      setTimeout(() => {
        window.location.href = '/login';
      }, 1000);
    } catch (error) {
      // Mesmo em caso de erro, tentar redirecionar para a página de login
      window.location.href = '/login';
    }
  };
  
  // Executar a limpeza quando a página carregar
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', resetAuth);
  } else {
    resetAuth();
  }
})();
