import { NextResponse } from 'next/server';

/**
 * API para fornecer as credenciais do Firebase de forma segura
 * 
 * Esta API retorna as credenciais do Firebase para o cliente,
 * evitando que as credenciais sejam expostas no código compilado.
 */
export async function GET() {
  // Retornar as credenciais do Firebase
  return NextResponse.json({
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  });
}
