"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { IconPlus } from "@tabler/icons-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth-context";
import { EstabelecimentoForm } from "@/components/estabelecimentos/estabelecimento-form";
import { Estabelecimento } from "@/types/estabelecimento";
import { addEstabelecimento, updateEstabelecimento, deleteEstabelecimento, listenToEstabelecimentos } from "@/services/estabelecimentos-service";
import { SearchBar } from "@/components/search-bar";
// Removido import do SharedLayout
import { EstabelecimentosTable } from "@/components/estabelecimentos-table";

export default function ClientPage() {
  const { loading, profileLoading, isAdmin, isTeamLeader } = useAuth();

  // Estados para gerenciar os dados
  const [estabelecimentos, setEstabelecimentos] = useState<Estabelecimento[]>([]);
  const [filteredEstabelecimentos, setFilteredEstabelecimentos] = useState<Estabelecimento[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentEstabelecimento, setCurrentEstabelecimento] = useState<Estabelecimento | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Carregar estabelecimentos
  useEffect(() => {
    if (loading || profileLoading) return;

    const unsubscribe = listenToEstabelecimentos((data) => {
      setEstabelecimentos(data);
      setFilteredEstabelecimentos(data);
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [loading, profileLoading]);

  // Função para pesquisar estabelecimentos
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilteredEstabelecimentos(estabelecimentos);
      return;
    }

    const queryLower = query.toLowerCase();
    const filtered = estabelecimentos.filter((estabelecimento) =>
      estabelecimento.nome.toLowerCase().includes(queryLower) ||
      estabelecimento.endereco.toLowerCase().includes(queryLower) ||
      estabelecimento.tipo.toLowerCase().includes(queryLower)
    );

    setFilteredEstabelecimentos(filtered);
  }, [estabelecimentos]);

  // Função para adicionar um novo estabelecimento
  const handleAddEstabelecimento = async (estabelecimentoData: Omit<Estabelecimento, "id" | "criadoPor" | "criadoEm" | "atualizadoPor" | "atualizadoEm" | "descricao" | "horarioFuncionamento" | "telefone" | "website">) => {
    setIsSubmitting(true);
    try {
      console.log("Adicionando estabelecimento com dados:", estabelecimentoData);

      // Verificar se as coordenadas são válidas
      if (estabelecimentoData.coordenadas.lat === 0 && estabelecimentoData.coordenadas.lng === 0) {
        console.warn("Tentando adicionar estabelecimento sem coordenadas válidas");
        toast.error("Não foi possível obter as coordenadas do endereço");
        return;
      }

      await addEstabelecimento(estabelecimentoData);
      toast.success("Estabelecimento adicionado com sucesso");
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Erro ao adicionar estabelecimento:", error);
      toast.error("Erro ao adicionar estabelecimento");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para editar um estabelecimento
  const handleEditEstabelecimento = async (estabelecimentoData: Omit<Estabelecimento, "id" | "criadoPor" | "criadoEm" | "atualizadoPor" | "atualizadoEm" | "descricao" | "horarioFuncionamento" | "telefone" | "website">) => {
    if (!currentEstabelecimento) return;

    setIsSubmitting(true);
    try {
      await updateEstabelecimento(currentEstabelecimento.id, estabelecimentoData);
      toast.success("Estabelecimento atualizado com sucesso");
      setIsEditModalOpen(false);
      setCurrentEstabelecimento(null);
    } catch (error) {
      console.error("Erro ao atualizar estabelecimento:", error);
      toast.error("Erro ao atualizar estabelecimento");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para excluir um estabelecimento
  const handleDeleteEstabelecimento = async (id: string): Promise<void> => {
    try {
      await deleteEstabelecimento(id);
    } catch (error) {
      console.error("Erro ao excluir estabelecimento:", error);
      throw error;
    }
  };

  // Função para abrir o modal de edição
  const handleOpenEditModal = (estabelecimento: Estabelecimento) => {
    setCurrentEstabelecimento(estabelecimento);
    setIsEditModalOpen(true);
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Estabelecimentos</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie bares, discotecas e outros estabelecimentos
              </p>
            </div>
            {(isAdmin || isTeamLeader) && (
              <Button onClick={() => setIsAddModalOpen(true)} className="shrink-0">
                <IconPlus className="size-4 mr-2" />
                Adicionar Estabelecimento
              </Button>
            )}
          </div>



          {/* Barra de pesquisa */}
          <div className="mb-6">
            <SearchBar
              onSearch={handleSearch}
              placeholder="Pesquisar por nome, endereço ou tipo..."
              className="max-w-full sm:max-w-md"
            />
          </div>

          {/* Lista de estabelecimentos */}
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
            </div>
          ) : filteredEstabelecimentos.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium">Nenhum estabelecimento encontrado</h3>
              <p className="text-muted-foreground mt-2">
                {searchQuery
                  ? "Tente uma pesquisa diferente ou adicione um novo estabelecimento."
                  : "Adicione seu primeiro estabelecimento clicando no botão acima."}
              </p>
            </div>
          ) : (
            <EstabelecimentosTable
              estabelecimentos={filteredEstabelecimentos}
              onEdit={handleOpenEditModal}
              onDelete={handleDeleteEstabelecimento}
            />
          )}
        </div>
      </div>

      {/* Modal para adicionar estabelecimento */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Adicionar Estabelecimento</DialogTitle>
          </DialogHeader>
          <EstabelecimentoForm
            onSubmit={handleAddEstabelecimento}
            onCancel={() => setIsAddModalOpen(false)}
            isSubmitting={isSubmitting}
          />
        </DialogContent>
      </Dialog>

      {/* Modal para editar estabelecimento */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Estabelecimento</DialogTitle>
          </DialogHeader>
          {currentEstabelecimento && (
            <EstabelecimentoForm
              onSubmit={handleEditEstabelecimento}
              onCancel={() => setIsEditModalOpen(false)}
              initialData={currentEstabelecimento}
              isSubmitting={isSubmitting}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
