"use client";

import { useState, useRef, useEffect } from "react";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { IconUpload, IconFile, IconX } from "@tabler/icons-react";
import { storage, auth } from "@/lib/firebase";
import { ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";
import { useAuth } from "@/contexts/auth-context";

interface FileUploadProps {
  onUploadComplete: (fileData: {
    name: string;
    title: string;
    url: string;
    type: string;
    size: number;
    uploadedAt: Date;
    uploadedBy: string;
    teamId?: string;
  }) => void;
}

export function FileUpload({ onUploadComplete }: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [title, setTitle] = useState("");
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const progressToastRef = useRef<string | number | null>(null);
  const lastProgressUpdateRef = useRef<number>(0);
  const { user, userTeamId } = useAuth();

  // Cleanup effect para garantir que toasts são dispensados quando o componente é desmontado
  useEffect(() => {
    return () => {
      if (progressToastRef.current) {
        dismissToast(progressToastRef.current);
        progressToastRef.current = null;
      }
    };
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  const resetForm = () => {
    setFile(null);
    setTitle("");
    setProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    // Limpar qualquer toast de progresso restante
    if (progressToastRef.current) {
      dismissToast(progressToastRef.current);
      progressToastRef.current = null;
    }
  };

  const handleUpload = async () => {
    if (!file) {
      showErrorToast("Por favor, selecione um ficheiro para carregar");
      return;
    }

    if (!title.trim()) {
      showErrorToast("Por favor, insira um título para o formulário");
      return;
    }

    if (!user) {
      showErrorToast("É necessário estar autenticado para carregar ficheiros");
      return;
    }

    // Verificar se o token de autenticação está disponível
    const currentUser = auth.currentUser;
    if (!currentUser) {
      showErrorToast("A sua sessão expirou. Por favor, faça login novamente");
      return;
    }

    setUploading(true);
    setProgress(0);
    lastProgressUpdateRef.current = 0;

    // Função para atualizar o toast de progresso com throttling
    const updateProgressToast = (message: string, force: boolean = false) => {
      const now = Date.now();
      // Throttle: só atualizar a cada 200ms, exceto se forçado
      if (!force && now - lastProgressUpdateRef.current < 200) {
        return;
      }

      lastProgressUpdateRef.current = now;

      if (progressToastRef.current) {
        dismissToast(progressToastRef.current);
      }
      progressToastRef.current = showLoadingToast(message);
    };

    const cleanupToast = () => {
      if (progressToastRef.current) {
        dismissToast(progressToastRef.current);
        progressToastRef.current = null;
      }
    };

    // Inicializar o toast de progresso
    updateProgressToast("A iniciar carregamento...", true);

    try {
      // Criar uma referência única para o arquivo no Firebase Storage
      const fileExtension = file.name.split('.').pop();
      const fileName = `${Date.now()}_${title.replace(/\\s+/g, '_')}.${fileExtension}`;

      // Usar uma estrutura simples já que todos os usuários autenticados têm acesso total
      const filePath = `formularios/${fileName}`;
      console.log("Tentando fazer upload para:", filePath);

      const storageRef = ref(storage, filePath);

      // Iniciar o upload
      const uploadTask = uploadBytesResumable(storageRef, file);

      // Monitorar o progresso do upload
      uploadTask.on(
        'state_changed',
        (snapshot) => {
          const progress = Math.round(
            (snapshot.bytesTransferred / snapshot.totalBytes) * 100
          );
          setProgress(progress);

          // Atualizar o toast com o progresso atual
          updateProgressToast(`A carregar ficheiro... ${progress}%`);
        },
        (error) => {
          // Em caso de erro, dispensar o toast de progresso
          console.error("Erro no upload:", error);
          cleanupToast();
          showErrorToast("Erro ao carregar ficheiro");
          setUploading(false);
          setProgress(0);
        },
        async () => {
          try {
            // Upload concluído com sucesso
            updateProgressToast("A finalizar carregamento...", true);

            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);

            // Preparar os dados do arquivo para salvar no Firestore
            const fileData: any = {
              name: file.name,
              title: title,
              url: downloadURL,
              type: file.type,
              size: file.size,
              uploadedAt: new Date(),
              uploadedBy: user.uid,
            };

            // Only include teamId if it has a valid value (not undefined)
            if (userTeamId) {
              fileData.teamId = userTeamId;
            }

            // Dispensar o toast de progresso antes de chamar o callback
            cleanupToast();

            // Notificar o componente pai sobre o upload concluído
            onUploadComplete(fileData);

            // Mostrar toast de sucesso
            showSuccessToast("Ficheiro carregado com sucesso");

            // Resetar o formulário
            resetForm();
            setUploading(false);
            setProgress(0);
          } catch (error) {
            console.error("Erro ao finalizar upload:", error);
            cleanupToast();
            showErrorToast("Erro ao finalizar carregamento do ficheiro");
            setUploading(false);
            setProgress(0);
          }
        }
      );
    } catch (error: any) {
      console.error("Erro ao iniciar upload:", error);
      cleanupToast();
      showErrorToast("Erro ao carregar ficheiro");
      setUploading(false);
      setProgress(0);
    }
  };

  // Função para obter o ícone com base no tipo de arquivo
  const getFileIcon = () => {
    if (!file) return null;

    const fileType = file.type;
    if (fileType.includes("pdf")) {
      return <IconFile className="text-red-500" />;
    } else if (fileType.includes("spreadsheet") || fileType.includes("excel") || file.name.endsWith(".xlsx") || file.name.endsWith(".xls")) {
      return <IconFile className="text-green-500" />;
    } else if (fileType.includes("word") || file.name.endsWith(".docx") || file.name.endsWith(".doc")) {
      return <IconFile className="text-blue-500" />;
    } else {
      return <IconFile className="text-gray-500" />;
    }
  };

  return (
    <div className="space-y-4 py-2">
        <div className="space-y-2">
          <Label htmlFor="title">Título do Formulário</Label>
          <Input
            id="title"
            placeholder="Insira um título para o formulário"
            value={title}
            onChange={handleTitleChange}
            disabled={uploading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="file">Ficheiro</Label>
          <div className="flex items-center gap-2">
            <Input
              ref={fileInputRef}
              id="file"
              type="file"
              onChange={handleFileChange}
              disabled={uploading}
              className="hidden"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
            />
            <div
              className="w-full h-24 flex flex-col items-center justify-center border border-dashed rounded-md cursor-pointer hover:bg-accent/50 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              {file ? (
                <div className="flex flex-col items-center gap-2">
                  <div className="flex items-center gap-2">
                    {getFileIcon()}
                    <span className="text-sm font-medium truncate max-w-[200px]">{file.name}</span>
                  </div>
                  <div
                    className="inline-flex h-6 items-center justify-center rounded-md px-2 text-xs font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      setFile(null);
                      if (fileInputRef.current) {
                        fileInputRef.current.value = "";
                      }
                    }}
                  >
                    <IconX className="size-3 mr-1" />
                    Remover
                  </div>
                </div>
              ) : (
                <>
                  <IconUpload className="size-6 mb-2 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Clique para selecionar ou arraste um ficheiro
                  </span>
                </>
              )}
            </div>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Formatos suportados: PDF, Word, Excel, PowerPoint, TXT
          </p>
        </div>
      <div className="flex justify-end gap-2 mt-6">
        <Button
          onClick={handleUpload}
          disabled={uploading || !file || !title.trim()}
        >
          {uploading ? `A carregar... ${progress}%` : "Carregar Formulário"}
        </Button>
      </div>
    </div>
  );
}
