"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserRole, Team } from "@/types/team";

interface User {
  id: string;
  name: string;
  email?: string;
  registrationNumber?: string;
  category?: string;
  role?: UserRole;
  teamId?: string;
}

interface UserRoleFormProps {
  user: User;
  teams: Team[];
  onSave: (userId: string, role: UserRole, teamId?: string) => void;
  isSelfEdit?: boolean;
}

export function UserRoleForm({ user, teams, onSave, isSelfEdit = false }: UserRoleFormProps) {
  console.log("UserRoleForm - Inicializando componente com dados:", {
    userId: user.id,
    role: user.role || "(sem papel)",
    teamId: user.teamId || "(sem equipa)"
  });

  // Inicializar os estados com os valores do utilizador diretamente
  const [selectedRole, setSelectedRole] = useState<UserRole | "">(user.role || "");
  const [selectedTeamId, setSelectedTeamId] = useState<string>(user.teamId || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showTeamSelect, setShowTeamSelect] = useState(
    user.role === UserRole.TEAM_LEADER || user.role === UserRole.TEAM_MEMBER
  );

  // Atualizar os estados quando o utilizador mudar
  useEffect(() => {
    console.log("UserRoleForm - useEffect executado com dados do utilizador:", {
      userId: user.id,
      role: user.role || "(sem papel)",
      teamId: user.teamId || "(sem equipa)"
    });

    // Definir o papel do utilizador
    setSelectedRole(user.role || "");

    // Definir a equipa do utilizador
    setSelectedTeamId(user.teamId || "");

    // Mostrar seletor de equipa se o papel for líder ou membro
    setShowTeamSelect(user.role === UserRole.TEAM_LEADER || user.role === UserRole.TEAM_MEMBER);
  }, [user]);

  // Atualizar visibilidade do seletor de equipa com base no papel selecionado
  useEffect(() => {
    console.log("UserRoleForm - Papel selecionado alterado:", selectedRole);
    setShowTeamSelect(selectedRole === UserRole.TEAM_LEADER || selectedRole === UserRole.TEAM_MEMBER);

    // Se o papel não requer equipa, limpar a seleção de equipa
    if (selectedRole === UserRole.ADMIN || selectedRole === "") {
      setSelectedTeamId("");
    }
  }, [selectedRole]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRole) {
      toast.error("Selecione um papel");
      return;
    }

    // Verificar se é necessário selecionar uma equipa
    if ((selectedRole === UserRole.TEAM_LEADER || selectedRole === UserRole.TEAM_MEMBER) && !selectedTeamId) {
      toast.error("Selecione uma equipa");
      return;
    }

    setIsSubmitting(true);

    try {
      // Passar teamId apenas se for necessário
      if (selectedRole === UserRole.TEAM_LEADER || selectedRole === UserRole.TEAM_MEMBER) {
        onSave(user.id, selectedRole, selectedTeamId);
      } else {
        onSave(user.id, selectedRole);
      }
    } catch (error) {
      console.error("Erro ao atualizar papel do utilizador:", error);
      toast.error("Erro ao atualizar papel do utilizador", {
        description: "Não foi possível atualizar o papel do utilizador.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <div className="mb-4">
          <h3 className="text-lg font-medium">{user.name}</h3>
          {user.registrationNumber && (
            <p className="text-sm text-muted-foreground">Matrícula: {user.registrationNumber}</p>
          )}
          {user.category && (
            <p className="text-sm text-muted-foreground">Categoria: {user.category}</p>
          )}
          {user.email && (
            <p className="text-sm text-muted-foreground">Email: {user.email}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="role">Papel</Label>
        <div className="mb-1 text-xs text-muted-foreground">
          Papel atual: {user.role ? (
            user.role === UserRole.ADMIN ? "Administrador" :
            user.role === UserRole.TEAM_LEADER ? "Líder de Equipa" :
            "Membro de Equipa"
          ) : "Sem papel definido"}
        </div>
        <Select
          value={selectedRole}
          onValueChange={(value) => {
            console.log("UserRoleForm - Papel alterado para:", value);
            setSelectedRole(value as UserRole);
          }}
          defaultValue={user.role}
        >
          <SelectTrigger id="role">
            <SelectValue placeholder="Selecione um papel">
              {selectedRole === UserRole.ADMIN && "Administrador"}
              {selectedRole === UserRole.TEAM_LEADER && "Líder de Equipa"}
              {selectedRole === UserRole.TEAM_MEMBER && "Membro de Equipa"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={UserRole.ADMIN}>Administrador</SelectItem>
            <SelectItem value={UserRole.TEAM_LEADER} disabled={isSelfEdit && user.role === UserRole.ADMIN}>Líder de Equipa</SelectItem>
            <SelectItem value={UserRole.TEAM_MEMBER} disabled={isSelfEdit && user.role === UserRole.ADMIN}>Membro de Equipa</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {showTeamSelect && (
        <div className="space-y-2">
          <Label htmlFor="teamId">Equipa</Label>
          <div className="mb-1 text-xs text-muted-foreground">
            Equipa atual: {user.teamId ? (
              teams.find(team => team.id === user.teamId)?.name || "Equipa desconhecida"
            ) : "Sem equipa definida"}
          </div>
          <Select
            value={selectedTeamId}
            onValueChange={(value) => {
              console.log("UserRoleForm - Equipa alterada para:", value);
              setSelectedTeamId(value);
            }}
            defaultValue={user.teamId}
          >
            <SelectTrigger id="teamId">
              <SelectValue placeholder="Selecione uma equipa">
                {selectedTeamId && teams.find(team => team.id === selectedTeamId)?.name}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {teams.length === 0 ? (
                <SelectItem value="none" disabled>
                  Nenhuma equipa disponível
                </SelectItem>
              ) : (
                teams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="flex justify-end gap-2 pt-4">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "A guardar..." : "Guardar"}
        </Button>
      </div>
    </form>
  );
}
