import { NextResponse } from 'next/server';
import { getFirebaseAdminStatus } from '@/lib/firebase-admin';

export async function GET() {
  console.log("=== 📊 API de status do Firebase chamada ===");
  
  try {
    const status = getFirebaseAdminStatus();
    console.log("📊 Status completo do Firebase Admin:", status);

    return NextResponse.json({
      success: true,
      message: 'Status do Firebase Admin obtido com sucesso',
      ...status,
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('💥 Erro ao obter status do Firebase Admin:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro ao obter status do Firebase Admin', 
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
