rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Funções auxiliares para verificação de papéis
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    function isTeamLeader() {
      return isAuthenticated() &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'team_leader';
    }

    function isTeamMember() {
      return isAuthenticated() &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'team_member';
    }

    // Regra padrão: permitir leitura para usuários autenticados
    match /{allPaths=**} {
      allow read: if isAuthenticated();
      allow write, delete: if isAdmin() || isTeamLeader();
    }

    // Regras para arquivos de identificações (fotos de documentos)
    match /identificacoes/{userId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (isAdmin() || request.auth.uid == userId);
      allow delete: if isAdmin();
    }

    // Regras para arquivos de formulários
    match /formularios/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write, delete: if isAdmin() || isTeamLeader();
    }
  }
}
