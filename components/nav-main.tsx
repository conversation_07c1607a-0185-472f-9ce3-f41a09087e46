"use client"


import Link from "next/link"
import { usePathname } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"

export function NavMain({
  categories,
}: {
  categories: {
    category: string
    items: {
      title: string
      url: string
    }[]
  }[]
}) {
  const pathname = usePathname();
  const { isMobile, setOpenMobile } = useSidebar();

  // Função para verificar se o item está ativo
  const isItemActive = (itemUrl: string) => {
    // Verificar se o pathname corresponde exatamente à URL do item
    if (itemUrl === pathname) return true;

    // Verificar se o pathname começa com a URL do item (para subpáginas)
    // Mas apenas se a URL do item não for a raiz e se for seguida por "/" ou for o final da string
    if (itemUrl !== "/" && pathname.startsWith(itemUrl)) {
      // Garantir que é uma correspondência exata ou uma subpágina real
      // Por exemplo: /nips deve corresponder a /nips/123 mas não a /nips-pessoas
      const nextChar = pathname[itemUrl.length];
      return nextChar === "/" || nextChar === undefined;
    }

    return false;
  };

  return (
    <div className="space-y-4">
      {categories.map((category) => (
        <SidebarGroup key={category.category}>
          <SidebarGroupLabel>{category.category}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {category.items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    tooltip={item.title}
                    asChild={item.url !== "#"}
                    isActive={item.url !== "#" && isItemActive(item.url)}
                  >
                    {item.url !== "#" ? (
                      <Link
                        href={item.url}
                        className="flex items-center gap-2"
                        onClick={() => {
                          // Verificar se estamos no processo de logout
                          if (typeof window !== 'undefined' && (window as unknown as Record<string, unknown>).__LOGGING_OUT) {
                            // Se estamos fazendo logout, não fazer nada para evitar erros de DOM
                            console.log('Link clicado durante logout, evitando manipulações do DOM');
                            return;
                          }

                          if (isMobile) {
                            setOpenMobile(false);
                          }
                        }}
                      >
                        <span>{item.title}</span>
                      </Link>
                    ) : (
                      <>
                        <span>{item.title}</span>
                      </>
                    )}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      ))}
    </div>
  )
}
