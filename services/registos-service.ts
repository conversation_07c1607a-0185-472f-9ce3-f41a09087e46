import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe,
  where
} from "firebase/firestore";
import {
  incrementCrimeTypeCount,
  decrementCrimeTypeCount,
  getCrimeTypeByName
} from "@/services/crime-types-service";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

// Interfaces para os tipos de registos
export interface Detencao {
  id: string;
  nppNuipc: string;
  responsavelId: string;
  responsavelNome: string;
  responsavelMatricula?: string; // Número de matrícula do responsável
  dataRegisto: string;
  horaRegisto: string;
  local: string;
  tipoCrime: string;
  tipoCrimeId?: string; // ID do tipo de crime selecionado
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  teamId?: string; // ID da equipa a que pertence o registo
  tipoRegisto: "detencao";
}

export interface AutoNoticia {
  id: string;
  nppNuipc: string;
  responsavelId: string;
  responsavelNome: string;
  responsavelMatricula?: string; // Número de matrícula do responsável
  dataRegisto: string;
  horaRegisto: string;
  local: string;
  tipoCrime: string;
  tipoCrimeId?: string; // ID do tipo de crime selecionado
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  teamId?: string; // ID da equipa a que pertence o registo
  tipoRegisto: "autoNoticia";
}

// Tipo união para representar qualquer tipo de registo
export type Registo = Detencao | AutoNoticia;

const COLLECTION_NAME = "registos";

// Adicionar uma nova detenção
export async function addDetencao(detencaoData: Omit<Detencao, "id" | "createdAt" | "updatedAt" | "tipoRegisto">): Promise<string> {
  try {
    // Log detalhado dos dados recebidos
    console.log("Adicionando detenção com dados:", {
      ...detencaoData,
      createdBy: detencaoData.createdBy ? "[definido]" : "[não definido]",
      teamId: detencaoData.teamId || "[não definido]",
    });

    // Obter a matrícula do responsável se não foi fornecida
    if (detencaoData.responsavelId && !detencaoData.responsavelMatricula) {
      try {
        const userDoc = await getDoc(doc(db, "users", detencaoData.responsavelId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.registrationNumber) {
            detencaoData.responsavelMatricula = userData.registrationNumber;
            console.log(`Matrícula do responsável obtida: ${userData.registrationNumber}`);
          }
        }
      } catch (userError) {
        console.error("Erro ao obter matrícula do responsável:", userError);
        // Continuar mesmo se houver erro ao obter a matrícula
      }
    }

    // Registrar o tipo de crime se existir
    if (detencaoData.tipoCrime) {
      try {
        // Se o ID do tipo de crime foi fornecido, usar diretamente
        if (detencaoData.tipoCrimeId) {
          console.log(`Usando tipo de crime existente com ID: ${detencaoData.tipoCrimeId}`);
          await incrementCrimeTypeCount(detencaoData.tipoCrimeId);
        } else {
          // Verificar se o tipo de crime já existe pelo nome
          const existingCrimeType = await getCrimeTypeByName(detencaoData.tipoCrime);

          if (existingCrimeType) {
            // Se o tipo de crime já existe, apenas incrementar o contador
            console.log(`Tipo de crime '${detencaoData.tipoCrime}' já existe. Incrementando contador.`);
            await incrementCrimeTypeCount(existingCrimeType.id);
          } else {
            // Se não existe, lançar um erro
            throw new Error(`Tipo de crime '${detencaoData.tipoCrime}' não existe. Por favor, selecione um tipo de crime existente.`);
          }
        }
      } catch (crimeTypeError) {
        console.error("Erro ao processar tipo de crime:", crimeTypeError);
        throw crimeTypeError; // Propagar o erro para que o cliente saiba que houve um problema
      }
    }

    // Criar um objeto limpo sem campos undefined
    const cleanData: Record<string, string | Date | boolean | number | undefined> = {};

    // Copiar apenas campos definidos
    Object.entries(detencaoData).forEach(([key, value]) => {
      if (value !== undefined) {
        cleanData[key] = value;
      }
    });

    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...cleanData,
      tipoRegisto: "detencao",
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    console.log("Detenção adicionada com sucesso. ID:", docRef.id);

    // Registar atividade de criação
    if (detencaoData.createdBy) {
      const userInfo = await getUser(detencaoData.createdBy);
      await logCreationActivity(
        ActivityType.REGISTO_CREATED,
        "Nova Detenção",
        `Criou uma nova detenção: ${detencaoData.nppNuipc}`,
        detencaoData.createdBy,
        detencaoData.teamId || userInfo?.teamId,
        {
          nppNuipc: detencaoData.nppNuipc,
          tipoRegisto: "detencao",
          local: detencaoData.local,
          tipoCrime: detencaoData.tipoCrime
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar detenção:", error);
    throw error;
  }
}

// Adicionar um novo auto de notícia
export async function addAutoNoticia(autoNoticiaData: Omit<AutoNoticia, "id" | "createdAt" | "updatedAt" | "tipoRegisto">): Promise<string> {
  try {
    // Log detalhado dos dados recebidos
    console.log("Adicionando auto de notícia com dados:", {
      ...autoNoticiaData,
      createdBy: autoNoticiaData.createdBy ? "[definido]" : "[não definido]",
      teamId: autoNoticiaData.teamId || "[não definido]",
    });

    // Obter a matrícula do responsável se não foi fornecida
    if (autoNoticiaData.responsavelId && !autoNoticiaData.responsavelMatricula) {
      try {
        const userDoc = await getDoc(doc(db, "users", autoNoticiaData.responsavelId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.registrationNumber) {
            autoNoticiaData.responsavelMatricula = userData.registrationNumber;
            console.log(`Matrícula do responsável obtida: ${userData.registrationNumber}`);
          }
        }
      } catch (userError) {
        console.error("Erro ao obter matrícula do responsável:", userError);
        // Continuar mesmo se houver erro ao obter a matrícula
      }
    }

    // Registrar o tipo de crime se existir
    if (autoNoticiaData.tipoCrime) {
      try {
        // Se o ID do tipo de crime foi fornecido, usar diretamente
        if (autoNoticiaData.tipoCrimeId) {
          console.log(`Usando tipo de crime existente com ID: ${autoNoticiaData.tipoCrimeId}`);
          await incrementCrimeTypeCount(autoNoticiaData.tipoCrimeId);
        } else {
          // Verificar se o tipo de crime já existe pelo nome
          const existingCrimeType = await getCrimeTypeByName(autoNoticiaData.tipoCrime);

          if (existingCrimeType) {
            // Se o tipo de crime já existe, apenas incrementar o contador
            console.log(`Tipo de crime '${autoNoticiaData.tipoCrime}' já existe. Incrementando contador.`);
            await incrementCrimeTypeCount(existingCrimeType.id);
          } else {
            // Se não existe, lançar um erro
            throw new Error(`Tipo de crime '${autoNoticiaData.tipoCrime}' não existe. Por favor, selecione um tipo de crime existente.`);
          }
        }
      } catch (crimeTypeError) {
        console.error("Erro ao processar tipo de crime:", crimeTypeError);
        throw crimeTypeError; // Propagar o erro para que o cliente saiba que houve um problema
      }
    }

    // Criar um objeto limpo sem campos undefined
    const cleanData: Record<string, string | Date | boolean | number | undefined> = {};

    // Copiar apenas campos definidos
    Object.entries(autoNoticiaData).forEach(([key, value]) => {
      if (value !== undefined) {
        cleanData[key] = value;
      }
    });

    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...cleanData,
      tipoRegisto: "autoNoticia",
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    console.log("Auto de notícia adicionado com sucesso. ID:", docRef.id);

    // Registar atividade de criação
    if (autoNoticiaData.createdBy) {
      const userInfo = await getUser(autoNoticiaData.createdBy);
      await logCreationActivity(
        ActivityType.REGISTO_CREATED,
        "Novo Auto de Notícia",
        `Criou um novo auto de notícia: ${autoNoticiaData.nppNuipc}`,
        autoNoticiaData.createdBy,
        autoNoticiaData.teamId || userInfo?.teamId,
        {
          nppNuipc: autoNoticiaData.nppNuipc,
          tipoRegisto: "autoNoticia",
          local: autoNoticiaData.local,
          tipoCrime: autoNoticiaData.tipoCrime
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar auto de notícia:", error);
    throw error;
  }
}

// Obter todos os registos
export async function getRegistos(teamId?: string, responsavelId?: string): Promise<Registo[]> {
  try {
    let q;

    // Prioridade 1: Se teamId for fornecido, filtrar APENAS por equipa
    // Isso garante que todos os membros da mesma equipa vejam os mesmos registos
    if (teamId) {
      console.log("Filtrando registos por teamId:", teamId);
      q = query(
        collection(db, COLLECTION_NAME),
        where("teamId", "==", teamId),
        orderBy("createdAt", "desc")
      );
    }
    // Prioridade 2: Se não houver teamId mas houver responsavelId, filtrar por responsável
    // Isso é para usuários que não pertencem a nenhuma equipa
    else if (responsavelId) {
      console.log("Filtrando registos por responsavelId:", responsavelId);
      q = query(
        collection(db, COLLECTION_NAME),
        where("responsavelId", "==", responsavelId),
        orderBy("createdAt", "desc")
      );
    } else {
      // Caso contrário, obter todos os registos (apenas para admin)
      console.log("Obtendo todos os registos (admin)");
      q = query(collection(db, COLLECTION_NAME), orderBy("createdAt", "desc"));
    }

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as Registo));
  } catch (error) {
    console.error("Erro ao obter registos:", error);
    throw error;
  }
}

// Atualizar um registo
export async function updateRegisto(id: string, registoData: Partial<Registo>): Promise<void> {
  try {
    console.log("Iniciando atualização do registo ID:", id);
    console.log("Dados para atualização:", registoData);

    // Primeiro, obter o documento atual para verificar os dados
    const registoRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(registoRef);

    if (!docSnap.exists()) {
      throw new Error(`Registo com ID ${id} não encontrado`);
    }

    const dadosAtuais = docSnap.data();
    console.log("Dados atuais do registo:", dadosAtuais);

    // Verificar se o responsável foi alterado
    let newTeamId = null; // Inicializar como null para forçar a atualização
    if (registoData.responsavelId) {
      console.log("Responsável alterado. Obtendo teamId e matrícula do novo responsável...");
      try {
        // Obter o teamId e matrícula do novo responsável
        const userDoc = await getDoc(doc(db, "users", registoData.responsavelId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.teamId) {
            console.log(`Novo teamId encontrado para o responsável: ${userData.teamId}`);
            newTeamId = userData.teamId;
          } else {
            console.log("Novo responsável não tem teamId definido.");
          }

          // Obter a matrícula do novo responsável
          if (userData.registrationNumber) {
            registoData.responsavelMatricula = userData.registrationNumber;
            console.log(`Matrícula do novo responsável obtida: ${userData.registrationNumber}`);
          } else {
            console.log("Novo responsável não tem matrícula definida.");
          }
        } else {
          console.log("Usuário responsável não encontrado.");
        }
      } catch (userError) {
        console.error("Erro ao obter informações do novo responsável:", userError);
        // Continuar mesmo se houver erro ao obter o teamId e matrícula
      }
    }

    // Verificar se o tipo de crime foi alterado
    if (registoData.tipoCrime && dadosAtuais.tipoCrime !== registoData.tipoCrime) {
      try {
        // Decrementar o contador do tipo de crime antigo
        if (dadosAtuais.tipoCrime) {
          const oldCrimeType = await getCrimeTypeByName(dadosAtuais.tipoCrime);
          if (oldCrimeType) {
            await decrementCrimeTypeCount(oldCrimeType.id);
          }
        }

        // Adicionar o novo tipo de crime e incrementar seu contador
        if (registoData.createdBy) {
          // Se o ID do tipo de crime foi fornecido, usar diretamente
          if (registoData.tipoCrimeId) {
            console.log(`Usando tipo de crime existente com ID: ${registoData.tipoCrimeId}`);
            await incrementCrimeTypeCount(registoData.tipoCrimeId);
          } else {
            // Verificar se o tipo de crime já existe pelo nome
            const existingCrimeType = await getCrimeTypeByName(registoData.tipoCrime);

            if (existingCrimeType) {
              // Se o tipo de crime já existe, apenas incrementar o contador
              console.log(`Tipo de crime '${registoData.tipoCrime}' já existe. Incrementando contador.`);
              await incrementCrimeTypeCount(existingCrimeType.id);
            } else {
              // Se não existe, lançar um erro
              throw new Error(`Tipo de crime '${registoData.tipoCrime}' não existe. Por favor, selecione um tipo de crime existente.`);
            }
          }
        }
      } catch (crimeTypeError) {
        console.error("Erro ao atualizar tipos de crime na atualização:", crimeTypeError);
        throw crimeTypeError; // Propagar o erro para que o cliente saiba que houve um problema
      }
    }

    // Criar um objeto limpo sem campos undefined
    const cleanData: Record<string, string | Date | boolean | number | undefined> = {};

    // Copiar apenas campos definidos
    Object.entries(registoData).forEach(([key, value]) => {
      if (value !== undefined) {
        cleanData[key] = value;
      }
    });

    // Garantir que o createdBy original seja preservado
    // E garantir que o teamId seja atualizado corretamente e nunca seja undefined
    const updateData = {
      ...cleanData,
      createdBy: dadosAtuais.createdBy, // Sempre preservar o createdBy original
      updatedAt: serverTimestamp(),
      // Usar o teamId do novo responsável ou uma string vazia
      // para evitar o erro "Unsupported field value: undefined"
      teamId: newTeamId || "",
    };

    // Log detalhado para debug
    console.log("Dados originais do registo:", {
      ...dadosAtuais,
      createdBy: dadosAtuais.createdBy ? "[definido]" : "[não definido]",
      teamId: dadosAtuais.teamId || "[não definido]",
    });
    console.log("TeamId fornecido para atualização:", registoData.teamId || "[não definido]");

    console.log("Dados finais para atualização:", updateData);
    console.log("TeamId final:", updateData.teamId);

    await updateDoc(registoRef, updateData);
    console.log("Registo atualizado com sucesso!");
    console.log("TeamId após atualização:", updateData.teamId);

    // Registar atividade de atualização
    if (dadosAtuais.createdBy) {
      const userInfo = await getUser(dadosAtuais.createdBy);
      await logUpdateActivity(
        ActivityType.REGISTO_UPDATED,
        `${dadosAtuais.tipoRegisto === 'detencao' ? 'Detenção' : 'Auto de Notícia'} Atualizado`,
        `Atualizou o registo: ${dadosAtuais.nppNuipc}`,
        dadosAtuais.createdBy,
        updateData.teamId || userInfo?.teamId,
        {
          nppNuipc: dadosAtuais.nppNuipc,
          tipoRegisto: dadosAtuais.tipoRegisto,
          local: registoData.local || dadosAtuais.local,
          tipoCrime: registoData.tipoCrime || dadosAtuais.tipoCrime
        }
      );
    }
  } catch (error) {
    console.error("Erro ao atualizar registo:", error);
    throw error;
  }
}

// Excluir um registo
export async function deleteRegisto(id: string, userId: string): Promise<void> {
  try {
    // Obter o registo antes de excluí-lo para poder decrementar o contador do tipo de crime
    const registoRef = doc(db, COLLECTION_NAME, id);
    const registoSnap = await getDoc(registoRef);

    if (!registoSnap.exists()) {
      throw new Error(`Registo com ID ${id} não encontrado`);
    }

    const registoData = registoSnap.data();

    // Decrementar o contador do tipo de crime se existir
    if (registoData.tipoCrime) {
      try {
        // Obter o tipo de crime pelo nome
        const crimeType = await getCrimeTypeByName(registoData.tipoCrime);
        if (crimeType) {
          // Decrementar o contador
          await decrementCrimeTypeCount(crimeType.id);
        }
      } catch (crimeTypeError) {
        console.error("Erro ao decrementar contador de tipo de crime:", crimeTypeError);
        // Continuar mesmo se houver erro ao decrementar o contador
      }
    }

    // Excluir o registo
    await deleteDoc(registoRef);

    // Registar atividade de eliminação
    const userInfo = await getUser(userId);
    await logDeletionActivity(
      ActivityType.REGISTO_DELETED,
      `${registoData.tipoRegisto === 'detencao' ? 'Detenção' : 'Auto de Notícia'} Eliminado`,
      `Eliminou o registo: ${registoData.nppNuipc}`,
      userId,
      userInfo?.teamId,
      {
        nppNuipc: registoData.nppNuipc,
        tipoRegisto: registoData.tipoRegisto,
        local: registoData.local,
        tipoCrime: registoData.tipoCrime
      }
    );
  } catch (error) {
    console.error("Erro ao excluir registo:", error);
    throw error;
  }
}

// Escutar mudanças em registos em tempo real
export function listenToRegistos(callback: (registos: Registo[]) => void, teamId?: string, responsavelId?: string): Unsubscribe {
  try {
    let q;

    // Prioridade 1: Se teamId for fornecido, filtrar APENAS por equipa
    // Isso garante que todos os membros da mesma equipa vejam os mesmos registos
    if (teamId) {
      console.log("Filtrando registos por teamId:", teamId);
      q = query(
        collection(db, COLLECTION_NAME),
        where("teamId", "==", teamId),
        orderBy("createdAt", "desc")
      );
    }
    // Prioridade 2: Se não houver teamId mas houver responsavelId, filtrar por responsável
    // Isso é para usuários que não pertencem a nenhuma equipa
    else if (responsavelId) {
      console.log("Filtrando registos por responsavelId:", responsavelId);
      q = query(
        collection(db, COLLECTION_NAME),
        where("responsavelId", "==", responsavelId),
        orderBy("createdAt", "desc")
      );
    } else {
      // Caso contrário, obter todos os registos (apenas para admin)
      console.log("Obtendo todos os registos (admin)");
      q = query(collection(db, COLLECTION_NAME), orderBy("createdAt", "desc"));
    }

    return onSnapshot(q, (querySnapshot) => {
      const registos = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as Registo));

      callback(registos);
    }, (error) => {
      console.error("Erro ao escutar registos:", error);
    });
  } catch (error) {
    console.error("Erro ao configurar listener para registos:", error);
    throw error;
  }
}

// Filtrar registos
export async function filtrarRegistos(
  tipoRegisto?: string,
  nppNuipc?: string,
  responsavelId?: string,
  dataInicio?: string,
  dataFim?: string,
  local?: string,
  tipoCrime?: string,
  teamId?: string
): Promise<Registo[]> {
  try {
    let q = query(collection(db, COLLECTION_NAME));

    // Adicionar filtros conforme necessário
    if (teamId) {
      q = query(q, where("teamId", "==", teamId));
    }

    if (tipoRegisto) {
      q = query(q, where("tipoRegisto", "==", tipoRegisto));
    }

    if (nppNuipc) {
      q = query(q, where("nppNuipc", "==", nppNuipc));
    }

    if (responsavelId) {
      q = query(q, where("responsavelId", "==", responsavelId));
    }

    if (local) {
      q = query(q, where("local", "==", local));
    }

    if (tipoCrime) {
      q = query(q, where("tipoCrime", "==", tipoCrime));
    }

    // Ordenar por data de criação (mais recente primeiro)
    q = query(q, orderBy("createdAt", "desc"));

    const querySnapshot = await getDocs(q);

    let registos = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as Registo));

    // Filtrar por data (se necessário) - isso precisa ser feito no cliente
    // porque o Firestore não suporta facilmente filtros de intervalo em vários campos
    if (dataInicio) {
      registos = registos.filter(registo => registo.dataRegisto >= dataInicio);
    }

    if (dataFim) {
      registos = registos.filter(registo => registo.dataRegisto <= dataFim);
    }

    return registos;
  } catch (error) {
    console.error("Erro ao filtrar registos:", error);
    throw error;
  }
}
