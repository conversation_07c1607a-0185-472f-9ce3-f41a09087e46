"use client"

import { useEffect, useState } from "react"
import dynamic from "next/dynamic"

// Importar o Toaster dinamicamente com SSR desativado
const DynamicToaster = dynamic(
  () => import("@/components/ui/sonner").then((mod) => mod.Toaster),
  { ssr: false }
)

export function ClientToaster() {
  // Usar um estado para controlar se estamos no cliente
  const [isMounted, setIsMounted] = useState(false)

  // Carregar o Toaster apenas no lado do cliente
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Não renderizar nada durante a renderização no servidor
  if (!isMounted) {
    return null
  }

  return (
    <DynamicToaster
      closeButton
      position="top-right"
      visibleToasts={3}
      duration={5000}
    />
  )
}
