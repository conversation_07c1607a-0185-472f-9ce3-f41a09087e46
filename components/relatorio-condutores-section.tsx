"use client";

import { useState } from "react";
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  IconCar,
  IconGasStation,
  IconRoad,
  IconClock,
  IconUser,
  IconCheck,
} from "@tabler/icons-react";
import { Condutor } from "@/services/condutores-service";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface RelatorioCondutoresSectionProps {
  condutores: Condutor[];
  isLoading: boolean;
}

export function RelatorioCondutoresSection({ condutores, isLoading }: RelatorioCondutoresSectionProps) {
  // Função para formatar data e hora
  const formatDateTime = (date: Date | null): string => {
    if (!date) return "Não definido";
    return new Intl.DateTimeFormat("pt-PT", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  };

  // Função para verificar se o serviço está finalizado
  const isFinalized = (condutor: Condutor) => {
    return condutor.dataHoraFim !== null && condutor.quilometrosFinais !== null;
  };

  // Função para calcular a distância percorrida (não usada diretamente, mas mantida para referência)
  const getDistanciaPercorrida = (condutor: Condutor) => {
    if (condutor.quilometrosFinais === null) return "Em progresso";
    return `${condutor.quilometrosFinais - condutor.quilometrosIniciais} km`;
  };

  // Renderizar esqueletos durante o carregamento
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={`skeleton-${index}`} className="@container/card flex flex-col">
            <CardHeader className="px-6 pb-3">
              <div className="flex justify-between items-start">
                <div className="flex flex-col gap-1.5">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-40" />
                </div>
                <Skeleton className="h-6 w-24" />
              </div>
            </CardHeader>
            <CardContent className="px-6 pt-0 flex-1">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-start gap-2">
                  <Skeleton className="h-4 w-4 shrink-0 mt-0.5" />
                  <div className="flex flex-col min-w-0 flex-1">
                    <Skeleton className="h-3 w-16 mb-1" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Skeleton className="h-4 w-4 shrink-0 mt-0.5" />
                  <div className="flex flex-col min-w-0 flex-1">
                    <Skeleton className="h-3 w-16 mb-1" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Skeleton className="h-4 w-4 shrink-0 mt-0.5" />
                  <div className="flex flex-col min-w-0 flex-1">
                    <Skeleton className="h-3 w-16 mb-1" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div>
      {condutores.length === 0 ? (
        <div className="text-center py-8 bg-muted/20 rounded-md">
          <p className="text-muted-foreground">Nenhum registo de condutor encontrado para o período selecionado.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {condutores.map((condutor) => (
            <Card key={condutor.id} className="@container/card dark:*:data-[slot=card]:bg-card *:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs flex flex-col">
              <CardHeader className="px-6 pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex flex-col gap-2">
                    <CardTitle className="leading-none font-semibold truncate">
                      {condutor.userName}
                    </CardTitle>
                    <CardDescription className="text-sm truncate">
                      {condutor.viaturaInfo}
                    </CardDescription>
                  </div>
                  <Badge
                    variant={isFinalized(condutor) ? "default" : "outline"}
                    className="whitespace-nowrap shrink-0"
                  >
                    {isFinalized(condutor) ? "Finalizado" : "Em Progresso"}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="px-6 pt-0 flex-1">
                <div className="grid grid-cols-1 gap-3 mb-3">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex flex-col">
                      <span className="text-xs text-muted-foreground">Início</span>
                      <span className="text-sm">{formatDateTime(condutor.dataHoraInicio)}</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-xs text-muted-foreground">Fim</span>
                      <span className="text-sm">{formatDateTime(condutor.dataHoraFim)}</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex flex-col">
                      <span className="text-xs text-muted-foreground">Km Iniciais</span>
                      <span className="text-sm">{condutor.quilometrosIniciais} km</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-xs text-muted-foreground">Km Finais</span>
                      <span className="text-sm">
                        {condutor.quilometrosFinais !== null ? `${condutor.quilometrosFinais} km` : "Não definido"}
                      </span>
                    </div>
                  </div>
                </div>

                {isFinalized(condutor) && (
                  <div className="flex items-center justify-between mb-3 border-t pt-3">
                    <span className="text-sm font-medium">Distância Percorrida</span>
                    <Badge variant="secondary">{condutor.quilometrosFinais! - condutor.quilometrosIniciais} km</Badge>
                  </div>
                )}

                {condutor.abastecimento && (
                  <div className="border-t pt-3 mb-3">
                    <span className="text-sm font-medium mb-2 block">Abastecimento</span>
                    <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Quilómetros</span>
                        <span className="text-sm">{condutor.abastecimento.quilometros} km</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Litros</span>
                        <span className="text-sm">{condutor.abastecimento.litros} L</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Preço por Litro</span>
                        <span className="text-sm">{condutor.abastecimento.precoLitro.toFixed(2)} €/L</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-muted-foreground">Preço Total</span>
                        <span className="text-sm">{condutor.abastecimento.precoTotal.toFixed(2)} €</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
