"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Estabelecimento, tiposEstabelecimento } from "@/types/estabelecimento";
import { LocationAutocomplete } from "@/components/location-autocomplete";
import { getCoordinates } from "@/services/location-service";
import { toast } from "sonner";

interface EstabelecimentoFormProps {
  onSubmit: (estabelecimento: Omit<Estabelecimento, "id" | "criadoPor" | "criadoEm" | "atualizadoPor" | "atualizadoEm" | "descricao" | "horarioFuncionamento" | "telefone" | "website">) => Promise<void>;
  onCancel: () => void;
  initialData?: Partial<Estabelecimento>;
  isSubmitting?: boolean;
}

export function EstabelecimentoForm({
  onSubmit,
  onCancel,
  initialData,
  isSubmitting = false
}: EstabelecimentoFormProps) {
  const [nome, setNome] = useState(initialData?.nome || "");
  const [tipo, setTipo] = useState<Estabelecimento["tipo"]>(initialData?.tipo || "bar");

  // Função para lidar com a mudança de tipo
  const handleTipoChange = (value: string) => {
    setTipo(value as Estabelecimento["tipo"]);
  };
  const [endereco, setEndereco] = useState(initialData?.endereco || "");
  const [coordenadas, setCoordenadas] = useState(initialData?.coordenadas || { lat: 0, lng: 0 });
  const [isLoadingCoordinates, setIsLoadingCoordinates] = useState(false);

  // Função para obter coordenadas a partir do endereço
  const handleEnderecoChange = async (value: string) => {
    console.log("Endereço selecionado:", value);

    // Detectar se estamos no Safari/iOS de forma mais robusta
    const isSafari = typeof navigator !== 'undefined' && (
      (/^((?!chrome|android).)*safari/i.test(navigator.userAgent) ||
      /iPad|iPhone|iPod/.test(navigator.userAgent)) &&
      !/CriOS/.test(navigator.userAgent) && // Excluir Chrome no iOS
      !/FxiOS/.test(navigator.userAgent) && // Excluir Firefox no iOS
      !/EdgiOS/.test(navigator.userAgent)   // Excluir Edge no iOS
    );

    console.log("Navegador detectado:", isSafari ? "Safari/iOS" : "Outro");

    // Para Safari, usar setTimeout para garantir que o valor seja aplicado corretamente
    if (isSafari) {
      setTimeout(() => {
        setEndereco(value);
      }, 0);
    } else {
      setEndereco(value);
    }

    // Usar um atraso maior para Safari/iOS
    const delay = isSafari ? 500 : 100;

    if (value.length > 5) {
      setIsLoadingCoordinates(true);
      try {
        // Adicionar um atraso para garantir que o valor seja processado corretamente
        await new Promise(resolve => setTimeout(resolve, delay));

        const coords = await getCoordinates(value);
        if (coords) {
          console.log("Coordenadas obtidas:", coords);
          setCoordenadas(coords);
        } else {
          console.warn("Não foi possível obter coordenadas para o endereço:", value);
          // Tentar novamente com uma busca mais ampla
          const retryCoords = await getCoordinates(value.split(',')[0]);
          if (retryCoords) {
            console.log("Coordenadas obtidas na segunda tentativa:", retryCoords);
            setCoordenadas(retryCoords);
          }
        }
      } catch (error) {
        console.error("Erro ao obter coordenadas:", error);
      } finally {
        setIsLoadingCoordinates(false);
      }
    }
  };

  // Função para validar o formulário
  const validateForm = (): boolean => {
    if (!nome.trim()) {
      toast.error("O nome do estabelecimento é obrigatório");
      return false;
    }

    if (!endereco.trim()) {
      toast.error("O endereço é obrigatório");
      return false;
    }

    if (coordenadas.lat === 0 && coordenadas.lng === 0) {
      // Se não foi possível obter as coordenadas, tentar obter novamente
      toast.promise(
        getCoordinates(endereco).then(coords => {
          if (coords) {
            setCoordenadas(coords);
            return true;
          }
          throw new Error("Não foi possível obter as coordenadas");
        }),
        {
          loading: 'Obtendo coordenadas...',
          success: 'Coordenadas obtidas com sucesso!',
          error: 'Não foi possível obter as coordenadas do endereço'
        }
      );
      return false;
    }

    return true;
  };

  // Função para lidar com o envio do formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const estabelecimentoData = {
      nome,
      tipo: tipo as Estabelecimento["tipo"],
      endereco,
      coordenadas
    };

    await onSubmit(estabelecimentoData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="nome">Nome do Estabelecimento *</Label>
          <Input
            id="nome"
            value={nome}
            onChange={(e) => setNome(e.target.value)}
            placeholder="Nome do estabelecimento"
            disabled={isSubmitting}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="tipo">Tipo de Estabelecimento *</Label>
          <Select
            value={tipo}
            onValueChange={handleTipoChange}
            disabled={isSubmitting}
          >
            <SelectTrigger id="tipo">
              <SelectValue placeholder="Selecione o tipo" />
            </SelectTrigger>
            <SelectContent>
              {tiposEstabelecimento.map((tipo) => (
                <SelectItem key={tipo.id} value={tipo.id}>
                  {tipo.nome}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="endereco">Endereço *</Label>
        <LocationAutocomplete
          value={endereco}
          onChange={handleEnderecoChange}
          placeholder="Endereço completo"
          disabled={isSubmitting}
          useNormalizedAddresses={false} // Usar o endereço completo, não normalizado
        />
        {isLoadingCoordinates && (
          <div className="text-xs text-muted-foreground">
            Obtendo coordenadas...
          </div>
        )}
        {coordenadas.lat !== 0 && coordenadas.lng !== 0 && (
          <div className="text-xs text-muted-foreground">
            Coordenadas: {coordenadas.lat.toFixed(6)}, {coordenadas.lng.toFixed(6)}
          </div>
        )}
      </div>



      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? "A guardar..." : initialData?.id ? "Atualizar" : "Guardar"}
        </Button>
      </div>
    </form>
  );
}
