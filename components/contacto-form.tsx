"use client";

import { useState } from "react";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/auth-context";
import { IconPlus, IconTrash, IconStar, IconStarFilled } from "@tabler/icons-react";
import { ContactInfo } from "@/services/contactos-service";

interface ContactoFormProps {
  onSaveComplete: (contactoData: {
    nome: string;
    telefones: ContactInfo[];
    emails: ContactInfo[];
    createdBy: string;
  }) => void;
}

export function ContactoForm({ onSaveComplete }: ContactoFormProps) {
  const [nome, setNome] = useState("");
  const [telefones, setTelefones] = useState<ContactInfo[]>([{ value: "", primary: true }]);
  const [emails, setEmails] = useState<ContactInfo[]>([{ value: "", primary: true }]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();

  const handleNomeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNome(e.target.value);
  };

  const handleTelefoneChange = (index: number, value: string) => {
    const newTelefones = [...telefones];
    newTelefones[index] = { ...newTelefones[index], value };
    setTelefones(newTelefones);
  };

  const handleEmailChange = (index: number, value: string) => {
    const newEmails = [...emails];
    newEmails[index] = { ...newEmails[index], value };
    setEmails(newEmails);
  };

  const addTelefone = () => {
    setTelefones([...telefones, { value: "", primary: false }]);
  };

  const addEmail = () => {
    setEmails([...emails, { value: "", primary: false }]);
  };

  const removeTelefone = (index: number) => {
    if (telefones.length > 1) {
      const newTelefones = telefones.filter((_, i) => i !== index);

      // Se removermos o telefone primário, definir o primeiro como primário
      if (telefones[index].primary && newTelefones.length > 0) {
        newTelefones[0] = { ...newTelefones[0], primary: true };
      }

      setTelefones(newTelefones);
    }
  };

  const removeEmail = (index: number) => {
    if (emails.length > 1) {
      const newEmails = emails.filter((_, i) => i !== index);

      // Se removermos o email primário, definir o primeiro como primário
      if (emails[index].primary && newEmails.length > 0) {
        newEmails[0] = { ...newEmails[0], primary: true };
      }

      setEmails(newEmails);
    }
  };

  const setPrimaryTelefone = (index: number) => {
    const newTelefones = telefones.map((tel, i) => ({
      ...tel,
      primary: i === index
    }));
    setTelefones(newTelefones);
  };

  const setPrimaryEmail = (index: number) => {
    const newEmails = emails.map((email, i) => ({
      ...email,
      primary: i === index
    }));
    setEmails(newEmails);
  };

  const resetForm = () => {
    setNome("");
    setTelefones([{ value: "", primary: true }]);
    setEmails([{ value: "", primary: true }]);
  };

  const validateForm = () => {
    if (!nome.trim()) {
      showErrorToast("Por favor, insira o nome do contacto");
      return false;
    }

    // Verificar se pelo menos um telefone válido foi inserido
    const validTelefones = telefones.filter(tel => tel.value.trim() !== "");
    if (validTelefones.length === 0) {
      showErrorToast("Por favor, insira pelo menos um número de telefone");
      return false;
    }

    // Verificar se pelo menos um email válido foi inserido
    const validEmails = emails.filter(email => email.value.trim() !== "");
    if (validEmails.length === 0) {
      showErrorToast("Por favor, insira pelo menos um endereço de email");
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    if (!user) {
      showErrorToast("É necessário estar autenticado para guardar contactos");
      return;
    }

    setIsSubmitting(true);
    const loadingToast = showLoadingToast("A guardar contacto...");

    try {
      // Filtrar telefones e emails vazios
      const filteredTelefones = telefones.filter(tel => tel.value.trim() !== "");
      const filteredEmails = emails.filter(email => email.value.trim() !== "");

      // Garantir que pelo menos um telefone e um email são primários
      if (!filteredTelefones.some(tel => tel.primary)) {
        filteredTelefones[0].primary = true;
      }

      if (!filteredEmails.some(email => email.primary)) {
        filteredEmails[0].primary = true;
      }

      // Preparar os dados do contacto
      const contactoData = {
        nome: nome,
        telefones: filteredTelefones,
        emails: filteredEmails,
        createdBy: user.uid,
      };

      // Notificar o componente pai sobre o contacto guardado
      onSaveComplete(contactoData);

      dismissToast(loadingToast);
      showSuccessToast("Contacto guardado");

      resetForm();
    } catch (error: any) {
      dismissToast(loadingToast);
      showErrorToast("Erro ao guardar contacto");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4 py-2">
      <div className="space-y-2">
        <Label htmlFor="nome">Nome</Label>
        <Input
          id="nome"
          placeholder="Insira o nome do contacto"
          value={nome}
          onChange={handleNomeChange}
          disabled={isSubmitting}
        />
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Telefones</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addTelefone}
            disabled={isSubmitting}
          >
            <IconPlus className="size-4 mr-1" />
            Adicionar
          </Button>
        </div>

        {telefones.map((telefone, index) => (
          <div key={`form-telefone-${index}`} className="flex items-center gap-2">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="size-8 shrink-0"
              onClick={() => setPrimaryTelefone(index)}
              disabled={isSubmitting}
              title={telefone.primary ? "Telefone principal" : "Definir como telefone principal"}
            >
              {telefone.primary ? (
                <IconStarFilled className="size-4 text-yellow-500" />
              ) : (
                <IconStar className="size-4" />
              )}
            </Button>

            <Input
              placeholder="Insira o número de telefone"
              value={telefone.value}
              onChange={(e) => handleTelefoneChange(index, e.target.value)}
              disabled={isSubmitting}
              className="flex-1"
            />

            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="size-8 shrink-0"
              onClick={() => removeTelefone(index)}
              disabled={isSubmitting || telefones.length <= 1}
              title="Remover telefone"
            >
              <IconTrash className="size-4 text-destructive" />
            </Button>
          </div>
        ))}
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Emails</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addEmail}
            disabled={isSubmitting}
          >
            <IconPlus className="size-4 mr-1" />
            Adicionar
          </Button>
        </div>

        {emails.map((email, index) => (
          <div key={`form-email-${index}`} className="flex items-center gap-2">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="size-8 shrink-0"
              onClick={() => setPrimaryEmail(index)}
              disabled={isSubmitting}
              title={email.primary ? "Email principal" : "Definir como email principal"}
            >
              {email.primary ? (
                <IconStarFilled className="size-4 text-yellow-500" />
              ) : (
                <IconStar className="size-4" />
              )}
            </Button>

            <Input
              placeholder="Insira o endereço de email"
              value={email.value}
              onChange={(e) => handleEmailChange(index, e.target.value)}
              disabled={isSubmitting}
              className="flex-1"
            />

            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="size-8 shrink-0"
              onClick={() => removeEmail(index)}
              disabled={isSubmitting || emails.length <= 1}
              title="Remover email"
            >
              <IconTrash className="size-4 text-destructive" />
            </Button>
          </div>
        ))}
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-end gap-2 mt-6">
        <Button
          variant="outline"
          type="button"
          onClick={resetForm}
          disabled={isSubmitting || (!nome.trim() && telefones.every(t => !t.value.trim()) && emails.every(e => !e.value.trim()))}
          className="sm:order-first"
        >
          Limpar Campos
        </Button>
        <Button
          onClick={handleSave}
          disabled={isSubmitting}
        >
          {isSubmitting ? "A guardar..." : "Guardar Contacto"}
        </Button>
      </div>
    </div>
  );
}
