"use client";

import { AppSidebar } from "@/components/app-sidebar";
import { SiteHeader } from "@/components/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useLoading } from "./loading-provider";
import { useAuth } from "@/contexts/auth-context";
import { useEffect, useState } from "react";

export function SharedLayout({ children }: { children: React.ReactNode }) {
  const { isRouteChanging } = useLoading();
  const { loading, profileLoading } = useAuth();
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Detectar quando estamos em transição entre páginas
  useEffect(() => {
    if (isRouteChanging) {
      setIsTransitioning(true);
    } else {
      // Pequeno atraso para evitar flickering
      const timeout = setTimeout(() => {
        setIsTransitioning(false);
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [isRouteChanging]);

  // Estado de carregamento global
  const isLoading = isTransitioning || loading || profileLoading;

  // Verificar se o usuário está autenticado
  const { user } = useAuth();

  // Usar um estado local para controlar a renderização inicial
  const [hasRendered, setHasRendered] = useState(false);

  // Efeito para marcar que o componente foi renderizado
  useEffect(() => {
    if (!hasRendered) {
      setHasRendered(true);
    }
  }, [hasRendered]);

  // Se não estiver autenticado e não estiver carregando, não renderizar o layout
  // O RouteGuard irá redirecionar para a página de login
  // Mas apenas após a primeira renderização para evitar flashes
  if (hasRendered && !isLoading && !user) {
    return null;
  }

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            {isLoading ? (
              <div className="flex min-h-[calc(100vh-var(--header-height))] items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-muted-foreground">A carregar...</p>
                </div>
              </div>
            ) : (
              children
            )}
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
