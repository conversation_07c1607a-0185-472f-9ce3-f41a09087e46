"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { IconArrowLeft, IconEdit, IconTrash, IconUserPlus, IconUserMinus, IconUserCheck, IconUserX, IconDotsVertical } from "@tabler/icons-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Team } from "@/types/team";
import { getTeam, updateTeam, deleteTeam, addTeamMember, removeTeamMember, addTeamLeader, removeTeamLeader } from "@/services/teams-service";
import { getSystemUsers } from "@/services/users-service";
import { TeamForm } from "@/components/teams/team-form";
import { TeamMembersList } from "@/components/teams/team-members-list";
import { TeamMemberForm } from "@/components/teams/team-member-form";
import { toast } from "sonner";

interface ClientPageProps {
  teamId: string;
}

export default function ClientPage({ teamId }: ClientPageProps) {
  const { user, loading, profileLoading, isAdmin } = useAuth();
  const router = useRouter();

  // Estados
  const [isLoading, setIsLoading] = useState(true);
  const [team, setTeam] = useState<Team | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [isAddMemberModalOpen, setIsAddMemberModalOpen] = useState(false);
  const [isAddLeaderModalOpen, setIsAddLeaderModalOpen] = useState(false);
  const [users, setUsers] = useState<{ id: string; name: string; registrationNumber?: string; category?: string }[]>([]);
  const [activeTab, setActiveTab] = useState("members");

  // Efeito para verificar se o usuário é admin
  useEffect(() => {
    if (!loading && !profileLoading && !isAdmin) {
      toast.error("Acesso negado", {
        description: "Você não tem permissão para acessar esta página.",
      });
      router.push("/dashboard");
    }
  }, [loading, profileLoading, isAdmin, router]);

  // Efeito para carregar dados da equipa
  useEffect(() => {
    const loadTeam = async () => {
      try {
        setIsLoading(true);
        const teamData = await getTeam(teamId);

        if (!teamData) {
          toast.error("Equipa não encontrada", {
            description: "A equipa solicitada não existe ou foi removida.",
          });
          router.push("/admin/teams");
          return;
        }

        setTeam(teamData);
      } catch (error) {
        console.error("Erro ao carregar equipa:", error);
        toast.error("Erro ao carregar equipa", {
          description: "Não foi possível carregar os dados da equipa.",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (user && isAdmin) {
      loadTeam();
    }
  }, [user, isAdmin, teamId, router]);

  // Efeito para carregar usuários do sistema
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const usersData = await getSystemUsers();
        setUsers(usersData);
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
        toast.error("Erro ao carregar usuários", {
          description: "Não foi possível carregar a lista de usuários.",
        });
      }
    };

    if (user && isAdmin) {
      loadUsers();
    }
  }, [user, isAdmin]);

  // Função para atualizar a equipa
  const handleUpdateTeam = async (teamData: Partial<Omit<Team, "id" | "createdAt" | "createdBy">>) => {
    if (!team) return;

    try {
      await updateTeam(team.id, teamData);

      // Atualizar o estado local
      setTeam(prev => prev ? { ...prev, ...teamData } : null);

      toast.success("Equipa atualizada com sucesso!");
      setIsEditModalOpen(false);
    } catch (error) {
      console.error("Erro ao atualizar equipa:", error);
      toast.error("Erro ao atualizar equipa", {
        description: "Não foi possível atualizar a equipa.",
      });
    }
  };

  // Função para iniciar o processo de exclusão da equipa
  const handleInitiateDelete = () => {
    setIsDeleteModalOpen(false);
    // Limpar o texto de confirmação antes de abrir o segundo modal
    setDeleteConfirmText("");
    setTimeout(() => {
      setIsDeleteConfirmModalOpen(true);
    }, 100);
  };

  // Função para verificar e excluir a equipa
  const handleDeleteTeam = async () => {
    if (!team) return;

    // Verificar se o texto de confirmação corresponde ao nome da equipa
    if (deleteConfirmText !== team.name) {
      toast.error("Nome da equipa incorreto", {
        description: "O nome digitado não corresponde ao nome da equipa.",
      });
      return;
    }

    try {
      await deleteTeam(team.id);
      toast.success("Equipa excluída com sucesso!");
      setIsDeleteConfirmModalOpen(false);
      router.push("/admin/teams");
    } catch (error) {
      console.error("Erro ao excluir equipa:", error);
      toast.error("Erro ao excluir equipa", {
        description: "Não foi possível excluir a equipa.",
      });
    }
  };

  // Função para adicionar membro à equipa
  const handleAddMember = async (userId: string) => {
    if (!team) return;

    try {
      await addTeamMember(team.id, userId);

      // Atualizar o estado local
      setTeam(prev => {
        if (!prev) return null;
        return {
          ...prev,
          members: [...prev.members, userId]
        };
      });

      toast.success("Membro adicionado com sucesso!");
      setIsAddMemberModalOpen(false);

      // Recarregar a lista de usuários para refletir as mudanças
      const usersData = await getSystemUsers();
      setUsers(usersData);
    } catch (error) {
      console.error("Erro ao adicionar membro:", error);
      toast.error("Erro ao adicionar membro", {
        description: "Não foi possível adicionar o membro à equipa.",
      });
    }
  };

  // Função para remover membro da equipa
  const handleRemoveMember = async (userId: string) => {
    if (!team) return;

    try {
      await removeTeamMember(team.id, userId);

      // Atualizar o estado local
      setTeam(prev => {
        if (!prev) return null;
        return {
          ...prev,
          members: prev.members.filter(id => id !== userId)
        };
      });

      toast.success("Membro removido com sucesso!");

      // Recarregar a lista de usuários para refletir as mudanças
      const usersData = await getSystemUsers();
      setUsers(usersData);
    } catch (error) {
      console.error("Erro ao remover membro:", error);
      toast.error("Erro ao remover membro", {
        description: "Não foi possível remover o membro da equipa.",
      });
    }
  };

  // Função para adicionar líder à equipa
  const handleAddLeader = async (userId: string) => {
    if (!team) return;

    try {
      await addTeamLeader(team.id, userId);

      // Atualizar o estado local
      setTeam(prev => {
        if (!prev) return null;
        return {
          ...prev,
          leaders: [...prev.leaders, userId]
        };
      });

      toast.success("Líder adicionado com sucesso!");
      setIsAddLeaderModalOpen(false);

      // Recarregar a lista de usuários para refletir as mudanças
      const usersData = await getSystemUsers();
      setUsers(usersData);
    } catch (error) {
      console.error("Erro ao adicionar líder:", error);
      toast.error("Erro ao adicionar líder", {
        description: "Não foi possível adicionar o líder à equipa.",
      });
    }
  };

  // Função para remover líder da equipa
  const handleRemoveLeader = async (userId: string) => {
    if (!team) return;

    try {
      await removeTeamLeader(team.id, userId);

      // Atualizar o estado local
      setTeam(prev => {
        if (!prev) return null;
        return {
          ...prev,
          leaders: prev.leaders.filter(id => id !== userId)
        };
      });

      toast.success("Líder removido com sucesso!");

      // Recarregar a lista de usuários para refletir as mudanças
      const usersData = await getSystemUsers();
      setUsers(usersData);
    } catch (error) {
      console.error("Erro ao remover líder:", error);
      toast.error("Erro ao remover líder", {
        description: "Não foi possível remover o líder da equipa.",
      });
    }
  };

  // Função para promover membro a líder
  const handlePromoteMember = async (userId: string) => {
    if (!team) return;

    try {
      // Remover dos membros primeiro para evitar duplicação
      await removeTeamMember(team.id, userId);

      // Adicionar como líder
      await addTeamLeader(team.id, userId);

      // Atualizar o estado local
      setTeam(prev => {
        if (!prev) return null;
        return {
          ...prev,
          leaders: [...prev.leaders, userId],
          members: prev.members.filter(id => id !== userId)
        };
      });

      toast.success("Membro promovido a líder com sucesso!");

      // Recarregar a lista de usuários para refletir as mudanças
      const usersData = await getSystemUsers();
      setUsers(usersData);
    } catch (error) {
      console.error("Erro ao promover membro:", error);
      toast.error("Erro ao promover membro", {
        description: "Não foi possível promover o membro a líder.",
      });
    }
  };

  // Função para rebaixar líder a membro
  const handleDemoteLeader = async (userId: string) => {
    if (!team) return;

    try {
      // Remover dos líderes primeiro para evitar duplicação
      await removeTeamLeader(team.id, userId);

      // Adicionar como membro
      await addTeamMember(team.id, userId);

      // Atualizar o estado local
      setTeam(prev => {
        if (!prev) return null;
        return {
          ...prev,
          members: [...prev.members, userId],
          leaders: prev.leaders.filter(id => id !== userId)
        };
      });

      toast.success("Líder rebaixado a membro com sucesso!");

      // Recarregar a lista de usuários para refletir as mudanças
      const usersData = await getSystemUsers();
      setUsers(usersData);
    } catch (error) {
      console.error("Erro ao rebaixar líder:", error);
      toast.error("Erro ao rebaixar líder", {
        description: "Não foi possível rebaixar o líder a membro.",
      });
    }
  };

  // Filtrar usuários que não são membros nem líderes da equipa
  const getAvailableUsers = () => {
    if (!team) return [];

    const teamUserIds = [...team.members, ...team.leaders];
    return users.filter(user => !teamUserIds.includes(user.id));
  };

  // Filtrar usuários que são membros da equipa
  const getTeamMembers = () => {
    if (!team) return [];

    return users.filter(user => team.members.includes(user.id));
  };

  // Filtrar usuários que são líderes da equipa
  const getTeamLeaders = () => {
    if (!team) return [];

    return users.filter(user => team.leaders.includes(user.id));
  };

  // Contar membros válidos (que existem no sistema)
  const countValidMembers = () => {
    if (!team) return 0;
    return users.filter(user => team.members.includes(user.id)).length;
  };

  // Contar líderes válidos (que existem no sistema)
  const countValidLeaders = () => {
    if (!team) return 0;
    return users.filter(user => team.leaders.includes(user.id)).length;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
      </div>
    );
  }

  if (!team) {
    return (
      <div className="px-4 py-6 lg:px-6">
        <Alert variant="destructive">
          <AlertTitle>Equipa não encontrada</AlertTitle>
          <AlertDescription>
            A equipa solicitada não existe ou foi removida.
          </AlertDescription>
        </Alert>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push("/admin/teams")}
        >
          <IconArrowLeft className="mr-2 size-4" />
          Voltar para a lista de equipas
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      <div className="px-4 lg:px-6">
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{team.name}</h1>
            {team.description && (
              <p className="text-muted-foreground mt-2">{team.description}</p>
            )}
          </div>
          <div className="flex gap-2 justify-end">
            {/* Botão de voltar - visível em todos os dispositivos */}
            <Button
              variant="outline"
              onClick={() => router.push("/admin/teams")}
              className="hidden sm:flex"
              size="sm"
            >
              <IconArrowLeft className="mr-2 size-4" />
              Voltar
            </Button>

            {/* Botão de voltar apenas com ícone em dispositivos móveis */}
            <Button
              variant="outline"
              onClick={() => router.push("/admin/teams")}
              className="sm:hidden"
              size="icon"
            >
              <IconArrowLeft className="size-4" />
              <span className="sr-only">Voltar</span>
            </Button>

            {/* Botões de ação em desktop */}
            <div className="hidden sm:flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsEditModalOpen(true)}
                size="sm"
              >
                <IconEdit className="mr-2 size-4" />
                Editar
              </Button>
              <Button
                variant="destructive"
                onClick={() => setIsDeleteModalOpen(true)}
                size="sm"
              >
                <IconTrash className="mr-2 size-4" />
                Excluir
              </Button>
            </div>

            {/* Menu dropdown para dispositivos móveis */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild className="sm:hidden">
                <Button variant="outline" size="icon">
                  <IconDotsVertical className="size-4" />
                  <span className="sr-only">Mais ações</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setIsEditModalOpen(true)}>
                  <IconEdit className="mr-2 size-4" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setIsDeleteModalOpen(true)}
                  className="text-destructive focus:text-destructive"
                >
                  <IconTrash className="mr-2 size-4" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Detalhes da Equipa</CardTitle>
            <CardDescription>Informações básicas sobre a equipa</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Nome</h3>
                <p>{team.name}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Criado em</h3>
                <p>{team.createdAt.toLocaleDateString()}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Última atualização</h3>
                <p>{team.updatedAt.toLocaleDateString()}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Membros</h3>
                <p>{countValidMembers()} membros, {countValidLeaders()} líderes</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="members">Membros</TabsTrigger>
            <TabsTrigger value="leaders">Líderes</TabsTrigger>
          </TabsList>

          <TabsContent value="members" className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Membros da Equipa</h2>
              <Button onClick={() => setIsAddMemberModalOpen(true)}>
                <IconUserPlus className="mr-2 size-4" />
                Adicionar Membro
              </Button>
            </div>

            <TeamMembersList
              users={getTeamMembers()}
              onRemove={handleRemoveMember}
              onPromote={handlePromoteMember}
              showPromoteButton
            />
          </TabsContent>

          <TabsContent value="leaders" className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Líderes da Equipa</h2>
              <Button onClick={() => setIsAddLeaderModalOpen(true)}>
                <IconUserPlus className="mr-2 size-4" />
                Adicionar Líder
              </Button>
            </div>

            <TeamMembersList
              users={getTeamLeaders()}
              onRemove={handleRemoveLeader}
              onDemote={handleDemoteLeader}
              showDemoteButton
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Modal de Edição */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Editar Equipa</DialogTitle>
          </DialogHeader>
          <TeamForm
            initialData={team}
            onSaveComplete={handleUpdateTeam}
          />
        </DialogContent>
      </Dialog>

      {/* Primeiro Modal de Exclusão - Aviso Inicial */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Excluir Equipa</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta equipa? Esta ação não pode ser desfeita e resultará na perda de todos os dados associados à equipa.
            </DialogDescription>
          </DialogHeader>
          <div className="p-4 my-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <p className="text-sm text-yellow-800 dark:text-yellow-400">
              <strong>Atenção:</strong> A exclusão de uma equipa removerá todos os membros e líderes associados a ela. Os utilizadores permanecerão no sistema, mas perderão o acesso aos dados da equipa.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleInitiateDelete}>
              Continuar com a Exclusão
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Segundo Modal de Exclusão - Confirmação Final */}
      <Dialog open={isDeleteConfirmModalOpen} onOpenChange={setIsDeleteConfirmModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmação Final de Exclusão</DialogTitle>
            <DialogDescription>
              Para confirmar a exclusão, digite o nome exato da equipa: <strong>{team?.name}</strong>
            </DialogDescription>
          </DialogHeader>
          <div className="p-4 my-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <p className="text-sm text-red-800 dark:text-red-400">
              <strong>Aviso:</strong> Esta ação é irreversível! Todos os dados associados a esta equipa serão permanentemente perdidos.
            </p>
          </div>
          <div className="my-4">
            <label htmlFor="confirm-text" className="text-sm font-medium">
              Nome da equipa:
            </label>
            <input
              id="confirm-text"
              type="text"
              value={deleteConfirmText}
              onChange={(e) => setDeleteConfirmText(e.target.value)}
              className="w-full mt-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder={`Digite "${team?.name}" para confirmar`}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteConfirmModalOpen(false)}>
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteTeam}
              disabled={deleteConfirmText !== team?.name}
            >
              Excluir Permanentemente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Adicionar Membro */}
      <Dialog open={isAddMemberModalOpen} onOpenChange={setIsAddMemberModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Adicionar Membro</DialogTitle>
          </DialogHeader>
          <TeamMemberForm
            users={getAvailableUsers()}
            onSave={handleAddMember}
          />
        </DialogContent>
      </Dialog>

      {/* Modal de Adicionar Líder */}
      <Dialog open={isAddLeaderModalOpen} onOpenChange={setIsAddLeaderModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Adicionar Líder</DialogTitle>
          </DialogHeader>
          <TeamMemberForm
            users={getAvailableUsers()}
            onSave={handleAddLeader}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
