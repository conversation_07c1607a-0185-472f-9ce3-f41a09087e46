// Função para obter a data e hora atuais no formato adequado para inputs HTML
export function getCurrentDateTime() {
  const now = new Date();
  
  // Formatar data como YYYY-MM-DD para input type="date"
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const date = `${year}-${month}-${day}`;
  
  // Formatar hora como HH:MM para input type="time"
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const time = `${hours}:${minutes}`;
  
  return { date, time };
}

// Função para formatar data para exibição
export function formatDate(date: Date | null | undefined): string {
  if (!date) return "Não definido";
  
  // Se for uma string de timestamp do Firestore, converter para Date
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat("pt-PT", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  }).format(dateObj);
}

// Função para formatar data e hora para exibição
export function formatDateTime(date: Date | null | undefined): string {
  if (!date) return "Não definido";
  
  // Se for uma string de timestamp do Firestore, converter para Date
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat("pt-PT", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(dateObj);
}

// Função para formatar apenas a hora
export function formatTime(date: Date | null | undefined): string {
  if (!date) return "Não definido";
  
  // Se for uma string de timestamp do Firestore, converter para Date
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat("pt-PT", {
    hour: "2-digit",
    minute: "2-digit",
  }).format(dateObj);
}
