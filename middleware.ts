import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Adicionar cabeçalhos para impedir indexação
  const response = NextResponse.next()

  // Cabeçalhos completos para impedir indexação
  response.headers.set('X-Robots-Tag', 'noindex, nofollow, noarchive, nosnippet, noimageindex')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'no-referrer')

  // Adicionar cabeçalho de segurança para evitar que o site seja exibido em frames
  response.headers.set('X-Frame-Options', 'DENY')

  // Adicionar cabeçalho para evitar problemas de cache com Service Worker
  response.headers.set('Cache-Control', 'no-store, max-age=0')

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',

    // Também aplicar a arquivos na pasta public que não são recursos estáticos
    '/robots.txt',
    '/sitemap.xml',
    '/.well-known/:path*',
  ],
}