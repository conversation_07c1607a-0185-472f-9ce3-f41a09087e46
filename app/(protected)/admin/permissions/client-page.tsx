"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";
import { SYSTEM_PAGES } from "@/types/permissions";
import { getSystemUsers } from "@/services/users-service";
import { getTeams } from "@/services/teams-service";
import {
  updateTargetPermissions,
  getUserPagePermissions,
  getPermissionsSummary
} from "@/services/permissions-service";
import {
  IconEyeOff,
  IconEdit,
  IconX,
  IconDotsVertical
} from "@tabler/icons-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { SearchBar } from "@/components/search-bar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function ClientPage() {
  const { isAdmin, loading } = useAuth();
  const router = useRouter();

  // Estados para armazenar dados
  const [users, setUsers] = useState<{
    id: string;
    name: string;
    registrationNumber?: string;
    role?: string;
    teamId?: string;
    teamName?: string;
  }[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [userHiddenPages, setUserHiddenPages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Estados para pesquisa e paginação
  const [userSearchQuery, setUserSearchQuery] = useState("");

  // Estado para armazenar o resumo de permissões
  const [permissionsSummary, setPermissionsSummary] = useState<{
    users: { id: string; name: string; hiddenPagesCount: number }[];
  }>({ users: [] });

  // Efeito para verificar se o usuário é administrador
  useEffect(() => {
    if (!loading && !isAdmin) {
      toast.error("Acesso negado");
      router.push("/dashboard");
    }
  }, [isAdmin, loading, router]);

  // Efeito para carregar usuários e resumo de permissões
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Carregar equipes para obter informações de equipe para os usuários
        const teamsData = await getTeams();

        // Carregar usuários com informações completas
        const usersData = await getSystemUsers();
        const usersWithTeamNames = usersData.map(user => {
          // Encontrar o nome da equipe para cada usuário
          const team = teamsData.find(t => t.id === user.teamId);
          return {
            id: user.id,
            name: user.name,
            registrationNumber: user.registrationNumber,
            role: user.role,
            teamId: user.teamId,
            teamName: team ? team.name : undefined
          };
        });
        setUsers(usersWithTeamNames);

        // Carregar resumo de permissões
        const summary = await getPermissionsSummary();
        setPermissionsSummary({
          users: summary.users
        });

        setIsLoading(false);
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        toast.error("Erro ao carregar dados");
        setIsLoading(false);
      }
    };

    if (isAdmin) {
      loadData();
    }
  }, [isAdmin]);

  // Função para carregar permissões de um usuário
  const loadUserPermissions = async (userId: string) => {
    try {
      setIsLoading(true);

      // Carregar permissões do usuário
      const permissions = await getUserPagePermissions(userId);

      // Extrair páginas ocultas
      const hiddenPages = permissions
        .filter(permission => permission.hidden)
        .map(permission => permission.pageId);

      setUserHiddenPages(hiddenPages);
      setIsLoading(false);
    } catch (error) {
      console.error("Erro ao carregar permissões do usuário:", error);
      toast.error("Erro ao carregar permissões");
      setIsLoading(false);
    }
  };

  // Variável para controlar o debounce do autosave
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  // Função para alternar a visibilidade de uma página para um usuário com autosave
  const toggleUserPageVisibility = async (pageId: string) => {
    // Atualiza o estado local primeiro para feedback imediato
    setUserHiddenPages(prev => {
      const newHiddenPages = prev.includes(pageId)
        ? prev.filter(id => id !== pageId)
        : [...prev, pageId];

      // Limpa o timeout anterior se existir
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }

      // Configura um novo timeout para salvar (debounce)
      const newTimeout = setTimeout(() => {
        saveUserPermissionsWithPages(newHiddenPages);
      }, 500); // Aumentado para 500ms para evitar salvamentos muito frequentes

      setSaveTimeout(newTimeout);

      return newHiddenPages;
    });
  };

  // Função para salvar permissões com páginas específicas
  const saveUserPermissionsWithPages = async (hiddenPages: string[]) => {
    if (!selectedUserId) return;

    try {
      setIsSaving(true);

      // Preparar dados de permissões
      const permissions = SYSTEM_PAGES.map(page => ({
        pageId: page.id,
        hidden: hiddenPages.includes(page.id),
      }));

      // Obter nome do usuário
      const userName = users.find(u => u.id === selectedUserId)?.name || "Utilizador";

      // Salvar permissões
      await updateTargetPermissions("user", selectedUserId, userName, permissions);

      // Atualizar o resumo de permissões
      const summary = await getPermissionsSummary();
      setPermissionsSummary(summary);

      // Não mostrar notificação para o autosave, apenas o indicador visual

      setIsSaving(false);
    } catch (error) {
      console.error("Erro ao salvar permissões do usuário:", error);
      toast.error("Erro ao guardar permissões");
      setIsSaving(false);
    }
  };

  // Renderizar páginas agrupadas por categoria
  const renderPagesByCategory = (hiddenPages: string[], toggleFunction: (pageId: string) => void) => {
    // Agrupar páginas por categoria
    const categorizedPages: Record<string, typeof SYSTEM_PAGES> = {};

    SYSTEM_PAGES.forEach(page => {
      if (!categorizedPages[page.category]) {
        categorizedPages[page.category] = [];
      }
      categorizedPages[page.category].push(page);
    });

    return Object.entries(categorizedPages).map(([category, pages]) => (
      <div key={category} className="mb-8">
        <div className="flex items-center mb-4">
          <h3 className="text-lg font-semibold">{category}</h3>
          <Separator className="flex-1 ml-4" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pages.map(page => (
            <div key={page.id} className="flex items-center justify-between space-x-4 p-4 rounded-md border bg-card hover:bg-muted/50 transition-colors">
              <Label htmlFor={`page-${page.id}`} className="font-medium cursor-pointer">
                {page.name}
              </Label>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground mr-1">
                  {!hiddenPages.includes(page.id) ? "Visível" : "Oculta"}
                </span>
                <Switch
                  id={`page-${page.id}`}
                  checked={!hiddenPages.includes(page.id)}
                  onCheckedChange={() => toggleFunction(page.id)}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    ));
  };

  // Função para filtrar usuários com base na pesquisa
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(userSearchQuery.toLowerCase())
  );
  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      <div className="px-4 lg:px-6">
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="hidden sm:block">
            <h1 className="text-2xl font-bold tracking-tight">Permissões</h1>
            <p className="text-muted-foreground mt-2">
              Gerir permissões de acesso às páginas
            </p>
          </div>
        </div>
      </div>

      <div className="px-4 lg:px-6">
        <div className="w-full">
          {selectedUserId ? (
            // Modo de edição de permissões
            <Card>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl">
                      {users.find(u => u.id === selectedUserId)?.name || "Utilizador"}
                    </CardTitle>
                    <CardDescription className="mt-1.5">
                      Selecione as páginas visíveis para o utilizador
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedUserId("")}
                    className="h-9 sm:px-4"
                  >
                    <IconX className="h-4 w-4 sm:mr-2" />
                    <span className="hidden sm:inline">Voltar</span>
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                  </div>
                ) : (
                  <>
                    <div className="space-y-6">
                      {renderPagesByCategory(userHiddenPages, toggleUserPageVisibility)}
                    </div>

                    {isSaving && (
                      <div className="fixed bottom-4 right-4 bg-background shadow-lg rounded-md px-4 py-2 flex items-center z-50 border">
                        <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent mr-2" />
                        <span className="text-sm font-medium">A guardar alterações...</span>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          ) : (
            // Lista de usuários
            <>
              <div className="mb-6">
                <SearchBar
                  onSearch={(query) => setUserSearchQuery(query)}
                  placeholder="Pesquisar Utilizadores"
                  className="max-w-full sm:max-w-md"
                />
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
                </div>
              ) : (
                <div className="relative flex flex-col gap-4">
                  <div className="overflow-hidden rounded-lg border">
                    <Table className="w-full">
                      <TableHeader className="bg-muted sticky top-0 z-10">
                        <TableRow>
                          <TableHead className="w-[25%]">Utilizador</TableHead>
                          <TableHead className="w-[12%]">Matrícula</TableHead>
                          <TableHead className="w-[18%]">Equipa</TableHead>
                          <TableHead className="w-[15%]">Papel</TableHead>
                          <TableHead className="w-[20%]">Páginas Ocultas</TableHead>
                          <TableHead className="w-[10%] text-right"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody className="**:data-[slot=table-cell]:first:w-8">
                        {filteredUsers.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={6} className="h-24 text-center text-muted-foreground">
                              Nenhum Utilizador Encontrado
                            </TableCell>
                          </TableRow>
                        ) : (
                          filteredUsers.map(user => {
                            const userPermission = permissionsSummary.users.find(u => u.id === user.id);
                            const hiddenPagesCount = userPermission?.hiddenPagesCount || 0;

                            // Formatar o papel do usuário
                            let roleDisplay = "";
                            let roleBadgeVariant = "outline";

                            if (user.role === "admin") {
                              roleDisplay = "Administrador";
                              roleBadgeVariant = "destructive";
                            } else if (user.role === "team_leader") {
                              roleDisplay = "Líder de Equipa";
                              roleBadgeVariant = "default";
                            } else if (user.role === "team_member") {
                              roleDisplay = "Membro de Equipa";
                              roleBadgeVariant = "secondary";
                            }

                            return (
                              <TableRow key={user.id} className="hover:bg-muted/50">
                                <TableCell>
                                  <div className="flex items-center gap-3">
                                    <Avatar className="size-8">
                                      <AvatarFallback>
                                        {user.name.split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2)}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div className="min-w-0 flex-1">
                                      <div className="font-medium truncate">{user.name}</div>
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="truncate">{user.registrationNumber || "N/A"}</div>
                                </TableCell>
                                <TableCell>
                                  <div className="truncate">
                                    {user.teamName ? (
                                      <Badge variant="outline" className="font-normal">
                                        {user.teamName}
                                      </Badge>
                                    ) : (
                                      <span className="text-muted-foreground text-sm">Sem Equipa</span>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge variant={roleBadgeVariant as "outline" | "destructive" | "default" | "secondary"}>
                                    {roleDisplay || "Sem Papel"}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  {hiddenPagesCount > 0 ? (
                                    <Badge variant="secondary">
                                      <IconEyeOff className="mr-1 h-3 w-3" />
                                      {hiddenPagesCount} {hiddenPagesCount === 1 ? 'página' : 'páginas'}
                                    </Badge>
                                  ) : (
                                    <span className="text-muted-foreground text-sm">Nenhuma</span>
                                  )}
                                </TableCell>
                                <TableCell className="text-right">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                                        size="icon"
                                      >
                                        <IconDotsVertical className="size-4" />
                                        <span className="sr-only">Menu</span>
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="w-40">
                                      <DropdownMenuItem onClick={() => {
                                        setSelectedUserId(user.id);
                                        loadUserPermissions(user.id);
                                      }}>
                                        <IconEdit className="size-4 mr-2" />
                                        Editar Permissões
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </TableCell>
                              </TableRow>
                            );
                          })
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
