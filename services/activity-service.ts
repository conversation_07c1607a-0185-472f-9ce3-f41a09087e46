import { db } from "@/lib/firebase";
import {
  collection,
  query,
  orderBy,
  limit,
  where,
  getDocs,
  addDoc,
  serverTimestamp
} from "firebase/firestore";
import { getUser } from "./users-service";

export interface Activity {
  id: string;
  type: ActivityType;
  title: string;
  description: string;
  userId: string;
  userName: string;
  userRegistrationNumber?: string;
  teamId?: string;
  teamName?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export enum ActivityType {
  // CREATE activities
  REGISTO_CREATED = 'registo_created',
  FORMULARIO_UPLOADED = 'formulario_uploaded',
  CONDUTOR_CREATED = 'condutor_created',
  IDENTIFICACAO_CREATED = 'identificacao_created',
  CONTACTO_CREATED = 'contacto_created',
  NIP_CREATED = 'nip_created',
  NIP_PESSOA_CREATED = 'nip_pessoa_created',
  TEXTO_CREATED = 'texto_created',
  VIATURA_CREATED = 'viatura_created',
  ESTABELECIMENTO_CREATED = 'estabelecimento_created',
  USER_REGISTERED = 'user_registered',
  TEAM_CREATED = 'team_created',

  // UPDATE activities
  REGISTO_UPDATED = 'registo_updated',
  FORMULARIO_UPDATED = 'formulario_updated',
  CONDUTOR_UPDATED = 'condutor_updated',
  IDENTIFICACAO_UPDATED = 'identificacao_updated',
  CONTACTO_UPDATED = 'contacto_updated',
  NIP_UPDATED = 'nip_updated',
  NIP_PESSOA_UPDATED = 'nip_pessoa_updated',
  TEXTO_UPDATED = 'texto_updated',
  VIATURA_UPDATED = 'viatura_updated',
  ESTABELECIMENTO_UPDATED = 'estabelecimento_updated',
  USER_ROLE_CHANGED = 'user_role_changed',

  // DELETE activities
  REGISTO_DELETED = 'registo_deleted',
  FORMULARIO_DELETED = 'formulario_deleted',
  CONDUTOR_DELETED = 'condutor_deleted',
  IDENTIFICACAO_DELETED = 'identificacao_deleted',
  CONTACTO_DELETED = 'contacto_deleted',
  NIP_DELETED = 'nip_deleted',
  NIP_PESSOA_DELETED = 'nip_pessoa_deleted',
  TEXTO_DELETED = 'texto_deleted',
  VIATURA_DELETED = 'viatura_deleted',
  ESTABELECIMENTO_DELETED = 'estabelecimento_deleted'
}



/**
 * Busca atividades recentes com base no papel do usuário
 */
export async function getRecentActivities(
  isAdmin: boolean,
  userTeamId?: string,
  limitCount: number = 20
): Promise<Activity[]> {
  try {
    let q;

    if (isAdmin) {
      // Admin vê todas as atividades
      q = query(
        collection(db, "activities"),
        orderBy("timestamp", "desc"),
        limit(limitCount)
      );
    } else if (userTeamId) {
      // Membros da equipa veem apenas atividades da sua equipa
      q = query(
        collection(db, "activities"),
        where("teamId", "==", userTeamId),
        orderBy("timestamp", "desc"),
        limit(limitCount)
      );
    } else {
      return [];
    }

    const querySnapshot = await getDocs(q);
    const activities: Activity[] = [];

    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();

      // Converter timestamp do Firestore para Date
      const timestamp = data.timestamp?.toDate() || new Date();

      activities.push({
        id: docSnapshot.id,
        type: data.type,
        title: data.title,
        description: data.description,
        userId: data.userId,
        userName: data.userName,
        userRegistrationNumber: data.userRegistrationNumber,
        teamId: data.teamId,
        teamName: data.teamName,
        timestamp,
        metadata: data.metadata || {}
      });
    }

    return activities;
  } catch (error) {
    return [];
  }
}





/**
 * Escuta atividades em tempo real
 */
export function listenToActivities(
  callback: (activities: Activity[]) => void,
  isAdmin: boolean,
  userTeamId?: string,
  limitCount: number = 20
): () => void {
  // Para simplificar, vamos fazer polling a cada 30 segundos
  // Em uma implementação mais avançada, poderíamos usar múltiplos listeners
  const interval = setInterval(async () => {
    const activities = await getRecentActivities(isAdmin, userTeamId, limitCount);
    callback(activities);
  }, 30000);

  // Buscar atividades iniciais
  getRecentActivities(isAdmin, userTeamId, limitCount).then(callback);

  // Retornar função de cleanup
  return () => clearInterval(interval);
}

/**
 * Formatar timestamp para exibição
 */
export function formatActivityTimestamp(timestamp: Date): string {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return "Agora mesmo";
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} min atrás`;
  } else if (diffInMinutes < 1440) { // 24 horas
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h atrás`;
  } else {
    const days = Math.floor(diffInMinutes / 1440);
    return `${days}d atrás`;
  }
}

/**
 * Obter ícone para tipo de atividade
 */
export function getActivityIcon(type: ActivityType): string {
  switch (type) {
    // CREATE activities
    case ActivityType.REGISTO_CREATED:
      return "IconFileText";
    case ActivityType.FORMULARIO_UPLOADED:
      return "IconUpload";
    case ActivityType.CONDUTOR_CREATED:
      return "IconUserShield";
    case ActivityType.IDENTIFICACAO_CREATED:
      return "IconId";
    case ActivityType.CONTACTO_CREATED:
      return "IconAddressBook";
    case ActivityType.NIP_CREATED:
      return "IconMapPin";
    case ActivityType.NIP_PESSOA_CREATED:
      return "IconUser";
    case ActivityType.TEXTO_CREATED:
      return "IconFileText";
    case ActivityType.VIATURA_CREATED:
      return "IconCar";
    case ActivityType.ESTABELECIMENTO_CREATED:
      return "IconBuilding";
    case ActivityType.USER_REGISTERED:
      return "IconUserPlus";
    case ActivityType.TEAM_CREATED:
      return "IconUsers";

    // UPDATE activities
    case ActivityType.REGISTO_UPDATED:
      return "IconEdit";
    case ActivityType.FORMULARIO_UPDATED:
      return "IconEdit";
    case ActivityType.CONDUTOR_UPDATED:
      return "IconEdit";
    case ActivityType.IDENTIFICACAO_UPDATED:
      return "IconEdit";
    case ActivityType.CONTACTO_UPDATED:
      return "IconEdit";
    case ActivityType.NIP_UPDATED:
      return "IconEdit";
    case ActivityType.NIP_PESSOA_UPDATED:
      return "IconEdit";
    case ActivityType.TEXTO_UPDATED:
      return "IconEdit";
    case ActivityType.VIATURA_UPDATED:
      return "IconEdit";
    case ActivityType.ESTABELECIMENTO_UPDATED:
      return "IconEdit";
    case ActivityType.USER_ROLE_CHANGED:
      return "IconShield";

    // DELETE activities
    case ActivityType.REGISTO_DELETED:
    case ActivityType.FORMULARIO_DELETED:
    case ActivityType.CONDUTOR_DELETED:
    case ActivityType.IDENTIFICACAO_DELETED:
    case ActivityType.CONTACTO_DELETED:
    case ActivityType.NIP_DELETED:
    case ActivityType.NIP_PESSOA_DELETED:
    case ActivityType.TEXTO_DELETED:
    case ActivityType.VIATURA_DELETED:
    case ActivityType.ESTABELECIMENTO_DELETED:
      return "IconTrash";
    default:
      return "IconBell";
  }
}

/**
 * Obter cor para tipo de atividade
 */
export function getActivityColor(type: ActivityType): string {
  switch (type) {
    // CREATE activities - green tones
    case ActivityType.REGISTO_CREATED:
      return "text-blue-500";
    case ActivityType.FORMULARIO_UPLOADED:
      return "text-green-500";
    case ActivityType.CONDUTOR_CREATED:
      return "text-purple-500";
    case ActivityType.IDENTIFICACAO_CREATED:
      return "text-indigo-500";
    case ActivityType.CONTACTO_CREATED:
      return "text-teal-500";
    case ActivityType.NIP_CREATED:
      return "text-emerald-500";
    case ActivityType.NIP_PESSOA_CREATED:
      return "text-cyan-500";
    case ActivityType.TEXTO_CREATED:
      return "text-lime-500";
    case ActivityType.VIATURA_CREATED:
      return "text-sky-500";
    case ActivityType.ESTABELECIMENTO_CREATED:
      return "text-violet-500";
    case ActivityType.USER_REGISTERED:
      return "text-orange-500";
    case ActivityType.TEAM_CREATED:
      return "text-cyan-500";

    // UPDATE activities - yellow/amber tones
    case ActivityType.REGISTO_UPDATED:
    case ActivityType.FORMULARIO_UPDATED:
    case ActivityType.CONDUTOR_UPDATED:
    case ActivityType.IDENTIFICACAO_UPDATED:
    case ActivityType.CONTACTO_UPDATED:
    case ActivityType.NIP_UPDATED:
    case ActivityType.NIP_PESSOA_UPDATED:
    case ActivityType.TEXTO_UPDATED:
    case ActivityType.VIATURA_UPDATED:
    case ActivityType.ESTABELECIMENTO_UPDATED:
      return "text-yellow-500";
    case ActivityType.USER_ROLE_CHANGED:
      return "text-amber-500";

    // DELETE activities - red tones
    case ActivityType.REGISTO_DELETED:
    case ActivityType.FORMULARIO_DELETED:
    case ActivityType.CONDUTOR_DELETED:
    case ActivityType.IDENTIFICACAO_DELETED:
    case ActivityType.CONTACTO_DELETED:
    case ActivityType.NIP_DELETED:
    case ActivityType.NIP_PESSOA_DELETED:
    case ActivityType.TEXTO_DELETED:
    case ActivityType.VIATURA_DELETED:
    case ActivityType.ESTABELECIMENTO_DELETED:
      return "text-red-500";
    default:
      return "text-gray-500";
  }
}

/**
 * Remove undefined values from an object recursively
 */
function cleanMetadata(obj: Record<string, any>): Record<string, any> {
  const cleaned: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined && value !== null) {
      if (typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        // Recursively clean nested objects
        const cleanedNested = cleanMetadata(value);
        if (Object.keys(cleanedNested).length > 0) {
          cleaned[key] = cleanedNested;
        }
      } else {
        cleaned[key] = value;
      }
    }
  }

  return cleaned;
}

/**
 * Registar uma atividade de criação
 */
export async function logCreationActivity(
  type: ActivityType,
  title: string,
  description: string,
  userId: string,
  teamId?: string,
  metadata?: Record<string, any>
): Promise<void> {
  await logActivity(type, title, description, userId, teamId, metadata);
}

/**
 * Registar uma atividade de atualização
 */
export async function logUpdateActivity(
  type: ActivityType,
  title: string,
  description: string,
  userId: string,
  teamId?: string,
  metadata?: Record<string, any>
): Promise<void> {
  await logActivity(type, title, description, userId, teamId, metadata);
}

/**
 * Registar uma atividade de eliminação
 */
export async function logDeletionActivity(
  type: ActivityType,
  title: string,
  description: string,
  userId: string,
  teamId?: string,
  metadata?: Record<string, any>
): Promise<void> {
  await logActivity(type, title, description, userId, teamId, metadata);
}

/**
 * Função universal para registar atividades
 */
async function logActivity(
  type: ActivityType,
  title: string,
  description: string,
  userId: string,
  teamId?: string,
  metadata?: Record<string, any>
): Promise<void> {
  try {
    const userInfo = await getUser(userId);
    if (!userInfo) {
      return;
    }

    // Determine the final teamId and teamName
    const finalTeamId = teamId || userInfo.teamId;
    let teamName: string | undefined;

    // Handle different user types for teamId assignment
    let activityTeamId: string | undefined;

    if (finalTeamId) {
      // User has a valid teamId
      activityTeamId = finalTeamId;
      try {
        const { getTeam } = await import("./teams-service");
        const teamInfo = await getTeam(finalTeamId);
        teamName = teamInfo?.name;
      } catch (error) {
        teamName = undefined;
      }
    } else if (userInfo.role === 'admin') {
      // Admin users without teamId get special handling
      activityTeamId = "admin";
      teamName = "Administração";
    } else {
      // Regular users without teamId - this shouldn't happen but handle gracefully
      activityTeamId = undefined;
      teamName = undefined;
    }

    // Clean metadata to remove undefined values
    const cleanedMetadata = metadata ? cleanMetadata(metadata) : {};

    // Prepare activity data, only including teamId if it has a value
    const activityData: any = {
      type,
      title,
      description,
      userId,
      userName: userInfo.name,
      userRegistrationNumber: userInfo.registrationNumber,
      timestamp: serverTimestamp(),
      metadata: cleanedMetadata
    };

    // Only include teamId and teamName if they have valid values
    if (activityTeamId) {
      activityData.teamId = activityTeamId;
    }
    if (teamName) {
      activityData.teamName = teamName;
    }

    await addDoc(collection(db, "activities"), activityData);
  } catch (error) {
    // Silently handle activity logging errors
  }
}


