"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { ThemeToggle } from "@/components/theme-toggle"
import { usePathname } from "next/navigation"
import { useMemo } from "react"

export function SiteHeader() {
  const pathname = usePathname();

  // Determinar o título com base no pathname atual
  const pageTitle = useMemo(() => {
    // Dividir o pathname em segmentos
    const segments = pathname.split("/").filter(segment => segment);

    // Verificar se estamos em uma página de administração
    if (segments[0] === "admin") {
      // Páginas de administração
      if (segments.length === 1) {
        return "Administração";
      }

      // Subpáginas de administração
      switch (segments[1]) {
        case "teams":
          // Verificar se estamos na página de detalhes de uma equipe
          if (segments.length > 2) {
            return "Detalhes da Equipa";
          }
          return "Gestão de Equipas";
        case "users":
          return "Gestão de Utilizadores";

        default:
          return "Administração";
      }
    }

    // Para outras páginas, usar o primeiro segmento
    const path = segments[0];

    // Mapear os caminhos para títulos legíveis
    switch (path) {
      case "dashboard":
        return "Dashboard";
      case "formularios":
        return "Formulários";
      case "textos":
        return "Textos";
      case "nips":
        return "Nips Locais";
      case "nips-pessoas":
        return "NIPs Pessoas";
      case "identificacoes":
        return "Identificações";
      case "contactos":
        return "Contactos";
      case "estabelecimentos":
        return "Estabelecimentos";
      case "registos":
        return "Registo Expediente";
      case "condutores":
        return "Condutores";
      case "viaturas":
        return "Viaturas";
      case "tipos-crimes":
        return "Tipos de Crimes";
      case "encavadelas":
        return "Encavadelas";
      case "conta":
        return "Conta";
      case "complete-profile":
        return "Completar Perfil";
      case "location-example":
        return "Exemplo de Localização";
      default:
        return "Sistema de Gestão";
    }
  }, [pathname]);

  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        <h1 className="text-base font-medium">{pageTitle}</h1>
        <div className="ml-auto flex items-center gap-2">
          <ThemeToggle />
        </div>
      </div>
    </header>
  )
}
