"use client";

import { useLoading } from "@/app/loading-provider";
import { useEffect, useState } from "react";

export function RouteProgress() {
  const { isRouteChanging } = useLoading();
  const [progress, setProgress] = useState(0);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (isRouteChanging) {
      setVisible(true);
      setProgress(0);
      
      const interval = setInterval(() => {
        setProgress((prev) => {
          const next = prev + Math.random() * 15;
          return next > 90 ? 90 : next;
        });
      }, 300);

      return () => clearInterval(interval);
    } else {
      if (visible) {
        setProgress(100);
        const timeout = setTimeout(() => {
          setVisible(false);
        }, 200);
        return () => clearTimeout(timeout);
      }
    }
  }, [isRouteChanging, visible]);

  if (!visible) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-1">
      <div 
        className="h-full bg-primary transition-all duration-300 ease-out"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
}
