import { db } from "@/lib/firebase";
import { PagePermission, SYSTEM_PAGES } from "@/types/permissions";
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp,
  Unsubscribe,
  onSnapshot,
  writeBatch
} from "firebase/firestore";
import { getUserInfo } from "./auth-service";

// Nome da coleção no Firestore
const COLLECTION_NAME = "page_permissions";

/**
 * Adiciona uma nova permissão de página
 */
export async function addPagePermission(permission: Omit<PagePermission, "id" | "createdAt" | "updatedAt" | "createdBy">): Promise<string> {
  try {
    const userInfo = await getUserInfo();

    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...permission,
      createdBy: userInfo?.uid || "Sistema",
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar permissão de página:", error);
    throw error;
  }
}

/**
 * Atualiza uma permissão de página existente
 */
export async function updatePagePermission(id: string, permission: Partial<Omit<PagePermission, "id" | "createdAt" | "createdBy">>): Promise<void> {
  try {
    await updateDoc(doc(db, COLLECTION_NAME, id), {
      ...permission,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error("Erro ao atualizar permissão de página:", error);
    throw error;
  }
}

/**
 * Exclui uma permissão de página
 */
export async function deletePagePermission(id: string): Promise<void> {
  try {
    await deleteDoc(doc(db, COLLECTION_NAME, id));
  } catch (error) {
    console.error("Erro ao excluir permissão de página:", error);
    throw error;
  }
}

/**
 * Obtém todas as permissões de página
 */
export async function getAllPagePermissions(): Promise<PagePermission[]> {
  try {
    const q = query(collection(db, COLLECTION_NAME), orderBy("createdAt", "desc"));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        pageId: data.pageId,
        pageName: data.pageName,
        hidden: data.hidden,
        targetType: data.targetType,
        targetId: data.targetId,
        targetName: data.targetName,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy
      } as PagePermission;
    });
  } catch (error) {
    console.error("Erro ao obter permissões de página:", error);
    throw error;
  }
}

/**
 * Obtém permissões de página para um usuário específico
 */
export async function getUserPagePermissions(userId: string): Promise<PagePermission[]> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where("targetType", "==", "user"),
      where("targetId", "==", userId)
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        pageId: data.pageId,
        pageName: data.pageName,
        hidden: data.hidden,
        targetType: data.targetType,
        targetId: data.targetId,
        targetName: data.targetName,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy
      } as PagePermission;
    });
  } catch (error) {
    console.error("Erro ao obter permissões de página do usuário:", error);
    throw error;
  }
}

/**
 * Obtém permissões de página para uma equipe específica
 */
export async function getTeamPagePermissions(teamId: string): Promise<PagePermission[]> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where("targetType", "==", "team"),
      where("targetId", "==", teamId)
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        pageId: data.pageId,
        pageName: data.pageName,
        hidden: data.hidden,
        targetType: data.targetType,
        targetId: data.targetId,
        targetName: data.targetName,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy
      } as PagePermission;
    });
  } catch (error) {
    console.error("Erro ao obter permissões de página da equipe:", error);
    throw error;
  }
}

/**
 * Obtém todas as permissões de página para um usuário, incluindo as da sua equipe
 */
export async function getAllUserPermissions(userId: string, teamId?: string): Promise<PagePermission[]> {
  try {
    const permissions: PagePermission[] = [];

    // Obter permissões do usuário
    const userPermissions = await getUserPagePermissions(userId);
    permissions.push(...userPermissions);

    // Se o usuário pertence a uma equipe, obter permissões da equipe
    if (teamId) {
      const teamPermissions = await getTeamPagePermissions(teamId);
      permissions.push(...teamPermissions);
    }

    return permissions;
  } catch (error) {
    console.error("Erro ao obter todas as permissões do usuário:", error);
    throw error;
  }
}

/**
 * Verifica se uma página deve ser ocultada para um usuário
 */
export function shouldHidePage(pageId: string, userPermissions: PagePermission[]): boolean {
  // Verificar permissões do usuário primeiro (prioridade mais alta)
  const userPermission = userPermissions.find(p =>
    p.pageId === pageId && p.targetType === 'user'
  );

  if (userPermission) {
    return userPermission.hidden;
  }

  // Verificar permissões da equipe
  const teamPermission = userPermissions.find(p =>
    p.pageId === pageId && p.targetType === 'team'
  );

  if (teamPermission) {
    return teamPermission.hidden;
  }

  // Se não houver permissões específicas, a página é visível por padrão
  return false;
}

/**
 * Atualiza ou cria permissões de página para um alvo (usuário ou equipe)
 */
export async function updateTargetPermissions(
  targetType: 'user' | 'team',
  targetId: string,
  targetName: string,
  permissions: { pageId: string, hidden: boolean }[]
): Promise<void> {
  try {
    const batch = writeBatch(db);
    const userInfo = await getUserInfo();

    // Obter permissões existentes
    const q = query(
      collection(db, COLLECTION_NAME),
      where("targetType", "==", targetType),
      where("targetId", "==", targetId)
    );
    const querySnapshot = await getDocs(q);

    // Mapear permissões existentes por pageId
    const existingPermissions = new Map<string, string>();
    querySnapshot.docs.forEach(doc => {
      const data = doc.data();
      existingPermissions.set(data.pageId, doc.id);
    });

    // Processar cada permissão
    for (const permission of permissions) {
      const { pageId, hidden } = permission;
      const page = SYSTEM_PAGES.find(p => p.id === pageId);

      if (!page) continue;

      if (existingPermissions.has(pageId)) {
        // Atualizar permissão existente
        const docId = existingPermissions.get(pageId)!;
        const docRef = doc(db, COLLECTION_NAME, docId);
        batch.update(docRef, {
          hidden,
          updatedAt: serverTimestamp()
        });
      } else {
        // Criar nova permissão
        const newPermission = {
          pageId,
          pageName: page.name,
          hidden,
          targetType,
          targetId,
          targetName,
          createdBy: userInfo?.uid || "Sistema",
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        const docRef = doc(collection(db, COLLECTION_NAME));
        batch.set(docRef, newPermission);
      }
    }

    // Executar o batch
    await batch.commit();
  } catch (error) {
    console.error("Erro ao atualizar permissões do alvo:", error);
    throw error;
  }
}

/**
 * Obtém um resumo das permissões modificadas
 * Retorna uma lista de usuários e equipes que têm permissões modificadas
 */
export async function getPermissionsSummary(): Promise<{
  users: { id: string; name: string; hiddenPagesCount: number }[];
  teams: { id: string; name: string; hiddenPagesCount: number }[];
}> {
  try {
    const permissions = await getAllPagePermissions();

    // Agrupar permissões por usuário
    const userPermissions = permissions.filter(p => p.targetType === 'user');
    const userMap = new Map<string, { id: string; name: string; hiddenPages: string[] }>();

    userPermissions.forEach(p => {
      if (!userMap.has(p.targetId)) {
        userMap.set(p.targetId, { id: p.targetId, name: p.targetName, hiddenPages: [] });
      }

      if (p.hidden) {
        userMap.get(p.targetId)!.hiddenPages.push(p.pageId);
      }
    });

    // Agrupar permissões por equipe
    const teamPermissions = permissions.filter(p => p.targetType === 'team');
    const teamMap = new Map<string, { id: string; name: string; hiddenPages: string[] }>();

    teamPermissions.forEach(p => {
      if (!teamMap.has(p.targetId)) {
        teamMap.set(p.targetId, { id: p.targetId, name: p.targetName, hiddenPages: [] });
      }

      if (p.hidden) {
        teamMap.get(p.targetId)!.hiddenPages.push(p.pageId);
      }
    });

    // Converter para arrays e adicionar contagem de páginas ocultas
    const users = Array.from(userMap.values()).map(u => ({
      id: u.id,
      name: u.name,
      hiddenPagesCount: u.hiddenPages.length
    }));

    const teams = Array.from(teamMap.values()).map(t => ({
      id: t.id,
      name: t.name,
      hiddenPagesCount: t.hiddenPages.length
    }));

    return { users, teams };
  } catch (error) {
    console.error("Erro ao obter resumo de permissões:", error);
    throw error;
  }
}

/**
 * Configura um listener para mudanças nas permissões de página
 */
export function listenToPagePermissions(callback: (permissions: PagePermission[]) => void): Unsubscribe {
  const q = query(collection(db, COLLECTION_NAME), orderBy("createdAt", "desc"));

  return onSnapshot(q, (querySnapshot) => {
    const permissions = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        pageId: data.pageId,
        pageName: data.pageName,
        hidden: data.hidden,
        targetType: data.targetType,
        targetId: data.targetId,
        targetName: data.targetName,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy
      } as PagePermission;
    });

    callback(permissions);
  });
}
