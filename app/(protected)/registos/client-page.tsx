"use client";

import { useEffect, useState, use<PERSON>allback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout
import { RegistosTable } from "@/components/registos-table";
import { RegistosFilter, FilterOptions } from "@/components/registos-filter";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardAction,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  IconUserShield,
  IconFileText,
} from "@tabler/icons-react";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import { DetencaoForm } from "@/components/detencao-form";
import { AutoNoticiaForm } from "@/components/auto-noticia-form";
import {
  addDetencao,
  addAutoNoticia,
  getRegistos,
  deleteRegisto,
  updateRegisto,
  listenTo<PERSON>eg<PERSON><PERSON>,
  <PERSON><PERSON>
} from "@/services/registos-service";
import { getSystemUsers } from "@/services/users-service";
import { getTeams } from "@/services/teams-service";
import { Team } from "@/types/team";

export default function ClientPage() {
  const { user, loading, profileLoading, userTeamId, isAdmin } = useAuth();
  const router = useRouter();

  // Estado de carregamento usado internamente
  const [isLoading, setIsLoading] = useState(true);
  const [registos, setRegistos] = useState<Registo[]>([]);
  const [filteredRegistos, setFilteredRegistos] = useState<Registo[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [registoToEdit, setRegistoToEdit] = useState<Registo | null>(null);

  // Lista de usuários para os seletores de responsável
  const [users, setUsers] = useState<{ id: string; name: string }[]>([]);

  // Lista de equipas para os filtros de administrador
  const [teams, setTeams] = useState<Team[]>([]);

  // Função para carregar registos
  const loadRegistos = useCallback(async () => {
    if (!user) return;

    try {
      console.log("Carregando registos para usuário:", user.uid);

      let data: Registo[] = [];
      if (!isAdmin && user) {
        if (userTeamId) {
          // Se não for admin mas tiver teamId, filtrar por equipa
          console.log("Carregando registos para equipa:", userTeamId);
          data = await getRegistos(userTeamId);
        } else {
          // Se não tiver teamId, filtrar por responsavelId
          console.log("Carregando registos para responsável:", user.uid);
          data = await getRegistos(undefined, user.uid);
        }
      } else if (isAdmin) {
        // Admin vê todos os registos
        data = await getRegistos();
      } else {
        // Fallback para caso o usuário não esteja definido
        data = [];
        console.warn("Usuário não está definido");
      }

      setRegistos(data);
      setFilteredRegistos(data);
    } catch (error) {
      console.error("Erro ao carregar registos:", error);
      showErrorToast("Erro ao carregar registos");
    } finally {
      setIsLoading(false);
    }
  }, [user, isAdmin, userTeamId]);

  // Efeito para redirecionar se não estiver autenticado
  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    }
  }, [user, loading, router]);

  // Efeito para carregar equipas (apenas para administradores)
  useEffect(() => {
    // Evitar execução durante o carregamento inicial
    if (loading || profileLoading) return;

    // Carregar equipas apenas para administradores
    if (isAdmin && user) {
      getTeams().then(teamsData => {
        setTeams(teamsData);
      }).catch(error => {
        console.error("Erro ao carregar equipas:", error);
      });
    }
  }, [isAdmin, user, loading, profileLoading]);

  // Efeito para carregar usuários do sistema
  useEffect(() => {
    // Evitar execução durante o carregamento inicial
    if (loading || profileLoading) return;

    async function loadUsers() {
      try {
        // Se for admin, carregar todos os usuários; caso contrário, apenas os da equipe
        console.log("Carregando usuários com teamId:", userTeamId);

        let usersData: { id: string; name: string; registrationNumber?: string; role?: string; teamId?: string; email?: string; }[] = [];
        if (isAdmin) {
          // Admin vê todos os usuários
          usersData = await getSystemUsers();
        } else if (userTeamId) {
          // Membros e líderes de equipe veem apenas os usuários da sua equipe
          usersData = await getSystemUsers(userTeamId);
        } else {
          // Se não for admin e não tiver teamId, retornar lista vazia
          usersData = [];
          console.warn("Usuário não é admin e não tem teamId definido");
        }

        console.log(`Carregados ${usersData.length} usuários`);
        setUsers(usersData);
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
        showErrorToast("Não foi possível carregar a lista de responsáveis");
      }
    }

    if (user) {
      loadUsers();
    }
  }, [user, isAdmin, userTeamId, loading, profileLoading]);

  // Memoizar a função de configuração do listener para evitar recriação
  const setupRegistosListener = useCallback(() => {
    if (!user) return () => {};

    // Log para debug
    console.log("Configuração de listener para registos:");
    console.log("- isAdmin:", isAdmin);
    console.log("- userTeamId:", userTeamId);
    console.log("- userId:", user.uid);

    // Configurar listener em tempo real para atualizações
    let unsubscribe;

    if (!isAdmin && user) {
      if (userTeamId) {
        // Se não for admin mas tiver teamId, filtrar por equipa
        console.log("Configurando listener para equipa:", userTeamId);
        unsubscribe = listenToRegistos((updatedRegistos) => {
          setRegistos(updatedRegistos);
          setFilteredRegistos(updatedRegistos);
          setIsLoading(false);
        }, userTeamId);
      } else {
        // Se não tiver teamId, filtrar por responsavelId para ver registros onde o usuário é o responsável
        console.log("Configurando listener para usuário:", user.uid);
        unsubscribe = listenToRegistos((updatedRegistos) => {
          setRegistos(updatedRegistos);
          setFilteredRegistos(updatedRegistos);
          setIsLoading(false);
        }, undefined, user.uid);
      }
    } else if (isAdmin) {
      // Se for admin, ver todos os registos
      console.log("Configurando listener para admin");
      unsubscribe = listenToRegistos((updatedRegistos) => {
        setRegistos(updatedRegistos);
        setFilteredRegistos(updatedRegistos);
        setIsLoading(false);
      });
    } else {
      // Fallback para caso o usuário não esteja definido
      console.warn("Usuário não está definido");
      setRegistos([]);
      setFilteredRegistos([]);
      setIsLoading(false);
      // Criar um unsubscribe dummy para evitar erros
      unsubscribe = () => {};
    }

    return unsubscribe;
  }, [user, isAdmin, userTeamId, setRegistos, setFilteredRegistos, setIsLoading]);

  // Configurar listener em tempo real para registos
  useEffect(() => {
    if (!loading && !profileLoading && user) {
      // Inicialmente, carregar registos normalmente
      loadRegistos();

      // Configurar listener em tempo real para atualizações
      const unsubscribe = setupRegistosListener();

      // Limpar listener quando o componente for desmontado
      return () => {
        unsubscribe();
      };
    }
  }, [user, loading, profileLoading, loadRegistos, setupRegistosListener]);

  // Função para lidar com a adição de uma detenção
  const handleAddDetencao = async (detencaoData: {
    id?: string;
    nppNuipc: string;
    responsavelId: string;
    responsavelNome: string;
    dataRegisto: string;
    horaRegisto: string;
    local: string;
    tipoCrime: string;
    tipoCrimeId?: string; // ID do tipo de crime selecionado
    createdBy: string;
    teamId?: string;
  }, isEdit: boolean) => {
    // Adicionar o ID da equipa se o usuário não for admin
    if (!isAdmin && userTeamId) {
      detencaoData.teamId = userTeamId;
      console.log("Definindo teamId para detenção:", userTeamId);
    } else {
      console.log("Não foi definido teamId para detenção. isAdmin:", isAdmin, "userTeamId:", userTeamId);
    }

    // Garantir que o responsavelId está definido
    if (!detencaoData.responsavelId) {
      detencaoData.responsavelId = user?.uid || "";
    }
    try {
      const loadingToast = showLoadingToast(isEdit ? "A atualizar detenção..." : "A guardar detenção...");

      if (isEdit && detencaoData.id) {
        // Atualizar registo existente
        const { id, ...dataToUpdate } = detencaoData;

        // Obter o registro original para preservar o createdBy original
        const registoOriginal = registoToEdit;

        // Log detalhado para debug
        console.log("Registro original:", registoOriginal);
        console.log("Dados para atualização:", {
          ...dataToUpdate,
          createdBy: registoOriginal?.createdBy || user?.uid,
          tipoRegisto: "detencao"
        });
        console.log("ID do usuário atual:", user?.uid);
        console.log("ID do responsável:", dataToUpdate.responsavelId);

        // Simplificar a atualização para apenas os campos essenciais
        await updateRegisto(id, {
          nppNuipc: dataToUpdate.nppNuipc,
          responsavelId: dataToUpdate.responsavelId,
          responsavelNome: dataToUpdate.responsavelNome,
          dataRegisto: dataToUpdate.dataRegisto,
          horaRegisto: dataToUpdate.horaRegisto,
          local: dataToUpdate.local,
          tipoCrime: dataToUpdate.tipoCrime,
          tipoCrimeId: dataToUpdate.tipoCrimeId, // Incluir o ID do tipo de crime
          createdBy: registoOriginal?.createdBy || user?.uid, // Preservar o createdBy original
          // Não definir o teamId explicitamente, permitindo que a função updateRegisto
          // determine o teamId correto com base no novo responsável
          tipoRegisto: "detencao"
        });
        dismissToast(loadingToast);
        showSuccessToast("Detenção atualizada");
      } else {
        // Adicionar novo registo
        await addDetencao(detencaoData);
        dismissToast(loadingToast);
        showSuccessToast("Detenção adicionada");
      }
      // Desativar o modo de edição após salvar com sucesso
      setIsEditing(false);
      setRegistoToEdit(null);
      // Esconder o formulário após salvar
      setActiveTab(null);
      return true;
    } catch (error) {
      console.error("Erro ao processar detenção:", error);
      showErrorToast("Erro ao processar detenção");
      return false;
    }
  };

  // Função para lidar com a adição de um auto de notícia
  const handleAddAutoNoticia = async (autoNoticiaData: {
    id?: string;
    nppNuipc: string;
    responsavelId: string;
    responsavelNome: string;
    dataRegisto: string;
    horaRegisto: string;
    local: string;
    tipoCrime: string;
    tipoCrimeId?: string; // ID do tipo de crime selecionado
    createdBy: string;
    teamId?: string;
  }, isEdit: boolean) => {
    // Adicionar o ID da equipa se o usuário não for admin
    if (!isAdmin && userTeamId) {
      autoNoticiaData.teamId = userTeamId;
      console.log("Definindo teamId para auto de notícia:", userTeamId);
    } else {
      console.log("Não foi definido teamId para auto de notícia. isAdmin:", isAdmin, "userTeamId:", userTeamId);
    }

    // Garantir que o responsavelId está definido
    if (!autoNoticiaData.responsavelId) {
      autoNoticiaData.responsavelId = user?.uid || "";
    }
    try {
      const loadingToast = showLoadingToast(isEdit ? "A atualizar auto de notícia..." : "A guardar auto de notícia...");

      if (isEdit && autoNoticiaData.id) {
        // Atualizar registo existente
        const { id, ...dataToUpdate } = autoNoticiaData;

        // Obter o registro original para preservar o createdBy original
        const registoOriginal = registoToEdit;

        await updateRegisto(id, {
          ...dataToUpdate,
          tipoCrimeId: dataToUpdate.tipoCrimeId, // Garantir que o ID do tipo de crime seja incluído
          createdBy: registoOriginal?.createdBy || user?.uid, // Preservar o createdBy original
          // Não definir o teamId explicitamente, permitindo que a função updateRegisto
          // determine o teamId correto com base no novo responsável
          tipoRegisto: "autoNoticia"
        });
        dismissToast(loadingToast);
        showSuccessToast("Auto de Notícia atualizado");
      } else {
        // Adicionar novo registo
        await addAutoNoticia(autoNoticiaData);
        dismissToast(loadingToast);
        showSuccessToast("Auto de Notícia adicionado");
      }
      // Desativar o modo de edição após salvar com sucesso
      setIsEditing(false);
      setRegistoToEdit(null);
      // Esconder o formulário após salvar
      setActiveTab(null);
      return true;
    } catch (error) {
      console.error("Erro ao processar auto de notícia:", error);
      showErrorToast("Erro ao processar auto de notícia");
      return false;
    }
  };

  // Função para lidar com a exclusão de um registo
  const handleDelete = async (id: string): Promise<void> => {
    if (!user?.uid) return;

    try {
      await deleteRegisto(id, user.uid);
      // Não retorna nada (void)
    } catch (error) {
      console.error("Erro ao excluir registo:", error);
      throw error;
    }
  };

  // Função para lidar com a edição de um registo
  const handleEdit = (registo: Registo) => {
    setRegistoToEdit(registo);
    setIsEditing(true);
    setActiveTab(registo.tipoRegisto === "detencao" ? "detencao" : "autoNoticia");

    // Pequeno atraso para garantir que o formulário seja renderizado antes de rolar
    setTimeout(() => {
      // Rolar para o formulário
      const formElement = document.getElementById("registo-form");
      if (formElement) {
        formElement.scrollIntoView({ behavior: "smooth" });
      }
    }, 100);
  };

  // Estado para armazenar os filtros atuais
  const [currentFilters, setCurrentFilters] = useState<FilterOptions>({
    tipoRegisto: "all",
    nppNuipc: "",
    responsavelId: "all",
    dataInicio: null,
    dataFim: null,
    local: "",
    tipoCrime: "",
    teamId: "all",
    createdBy: "",
  });

  // Função para aplicar filtros
  const handleFilter = (options: FilterOptions) => {
    // Atualizar o estado dos filtros atuais
    setCurrentFilters(options);
    let filtered = [...registos];

    // Filtrar por tipo de registo
    if (options.tipoRegisto !== "all") {
      filtered = filtered.filter(registo => registo.tipoRegisto === options.tipoRegisto);
    }

    // Filtrar por NPP/NUIPC
    if (options.nppNuipc) {
      filtered = filtered.filter(registo =>
        registo.nppNuipc.toLowerCase().includes(options.nppNuipc.toLowerCase())
      );
    }

    // Filtrar por responsável
    if (options.responsavelId !== "all") {
      filtered = filtered.filter(registo => registo.responsavelId === options.responsavelId);
    }

    // Filtrar por data de início
    if (options.dataInicio) {
      const dataInicioStr = options.dataInicio.toISOString().split('T')[0];
      filtered = filtered.filter(registo => registo.dataRegisto >= dataInicioStr);
    }

    // Filtrar por data de fim
    if (options.dataFim) {
      const dataFimStr = options.dataFim.toISOString().split('T')[0];
      filtered = filtered.filter(registo => registo.dataRegisto <= dataFimStr);
    }

    // Filtrar por local
    if (options.local) {
      filtered = filtered.filter(registo =>
        registo.local.toLowerCase().includes(options.local.toLowerCase())
      );
    }

    // Filtrar por tipo de crime
    if (options.tipoCrime) {
      filtered = filtered.filter(registo =>
        registo.tipoCrime.toLowerCase().includes(options.tipoCrime.toLowerCase())
      );
    }

    // Filtros adicionais para administradores
    if (isAdmin) {
      // Filtrar por equipa
      if (options.teamId && options.teamId !== 'all') {
        filtered = filtered.filter(registo => {
          if (options.teamId === 'admin') {
            // Mostrar registos de administradores (sem equipa ou com teamId="admin")
            return !registo.teamId || registo.teamId === 'admin';
          }
          return registo.teamId === options.teamId;
        });
      }


    }

    setFilteredRegistos(filtered);
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 hidden sm:block">
            <h1 className="text-2xl font-bold tracking-tight">Registos</h1>
            <p className="text-muted-foreground mt-2">
              Gerencie os registos de detenções e autos de notícia
            </p>
          </div>

          {/* Cards para os formulários */}
          <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 mb-6">
            <Card
              className={`@container/card cursor-pointer hover:border-primary/50 transition-colors ${activeTab === "detencao" ? "border-primary" : ""}`}
              onClick={() => {
                setActiveTab("detencao");
                setIsEditing(false);
                setRegistoToEdit(null);
              }}
            >
              <CardHeader>
                <CardDescription>Formulário</CardDescription>
                <CardTitle className="text-lg font-semibold">
                  Detenções
                </CardTitle>
                <CardAction>
                  <IconUserShield className="size-5 text-primary" />
                </CardAction>
              </CardHeader>
              <CardFooter className="flex-col items-start gap-1.5 text-sm">
                <div className="line-clamp-1 flex gap-2 font-medium">
                  Registar nova detenção no sistema
                </div>
              </CardFooter>
            </Card>

            <Card
              className={`@container/card cursor-pointer hover:border-primary/50 transition-colors ${activeTab === "autoNoticia" ? "border-primary" : ""}`}
              onClick={() => {
                setActiveTab("autoNoticia");
                setIsEditing(false);
                setRegistoToEdit(null);
              }}
            >
              <CardHeader>
                <CardDescription>Formulário</CardDescription>
                <CardTitle className="text-lg font-semibold">
                  Auto de Notícia
                </CardTitle>
                <CardAction>
                  <IconFileText className="size-5 text-primary" />
                </CardAction>
              </CardHeader>
              <CardFooter className="flex-col items-start gap-1.5 text-sm">
                <div className="line-clamp-1 flex gap-2 font-medium">
                  Registar novo auto de notícia no sistema
                </div>
              </CardFooter>
            </Card>
          </div>

          {/* Formulários */}
          {activeTab && (
            <div className="mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="space-y-2">
                    <CardTitle>
                      {isEditing
                        ? (activeTab === "detencao" ? "Editar Detenção" : "Editar Auto de Notícia")
                        : (activeTab === "detencao" ? "Registar Nova Detenção" : "Registar Novo Auto de Notícia")
                      }
                    </CardTitle>
                    <CardDescription>
                      {activeTab === "detencao" ? "Preencha os campos para registar uma detenção" : "Preencha os campos para registar um auto de notícia"}
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setActiveTab(null);
                      setIsEditing(false);
                      setRegistoToEdit(null);
                    }}
                    className="h-8 w-8 rounded-full"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-4">
                      <path d="M18 6 6 18" />
                      <path d="m6 6 12 12" />
                    </svg>
                    <span className="sr-only">Fechar</span>
                  </Button>
                </CardHeader>
                <CardContent id="registo-form">
                  {activeTab === "detencao" ? (
                    <DetencaoForm
                      onSaveComplete={handleAddDetencao}
                      users={users}
                      initialData={isEditing && registoToEdit?.tipoRegisto === "detencao" ? {
                        nppNuipc: registoToEdit.nppNuipc,
                        responsavelId: registoToEdit.responsavelId,
                        dataRegisto: registoToEdit.dataRegisto,
                        horaRegisto: registoToEdit.horaRegisto,
                        local: registoToEdit.local,
                        tipoCrime: registoToEdit.tipoCrime
                      } : undefined}
                      editingId={isEditing && registoToEdit?.tipoRegisto === "detencao" ? registoToEdit.id : undefined}
                      isEditing={isEditing}
                      onCancelEdit={() => {
                        setIsEditing(false);
                        setRegistoToEdit(null);
                        setActiveTab(null);
                      }}
                    />
                  ) : (
                    <AutoNoticiaForm
                      onSaveComplete={handleAddAutoNoticia}
                      users={users}
                      initialData={isEditing && registoToEdit?.tipoRegisto === "autoNoticia" ? {
                        nppNuipc: registoToEdit.nppNuipc,
                        responsavelId: registoToEdit.responsavelId,
                        dataRegisto: registoToEdit.dataRegisto,
                        horaRegisto: registoToEdit.horaRegisto,
                        local: registoToEdit.local,
                        tipoCrime: registoToEdit.tipoCrime
                      } : undefined}
                      editingId={isEditing && registoToEdit?.tipoRegisto === "autoNoticia" ? registoToEdit.id : undefined}
                      isEditing={isEditing}
                      onCancelEdit={() => {
                        setIsEditing(false);
                        setRegistoToEdit(null);
                        setActiveTab(null);
                      }}
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Filtros */}
          <div className="mb-6">
            <RegistosFilter
              onFilter={handleFilter}
              users={users}
              initialFilters={currentFilters}
              isAdmin={isAdmin}
              teams={teams}
            />
          </div>

          {/* Seção de listagem */}
          <div className="mt-2">
            {!isLoading && filteredRegistos.length > 0 && (
              <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-muted-foreground">
                  {filteredRegistos.length} {filteredRegistos.length === 1 ? "registo encontrado" : "registos encontrados"}
                </div>
              </div>
            )}
            {isLoading ? (
              <div className="flex min-h-[200px] items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-muted-foreground">A carregar registos...</p>
                </div>
              </div>
            ) : (
              <RegistosTable
                registos={filteredRegistos}
                onDelete={handleDelete}
                onEdit={handleEdit}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
