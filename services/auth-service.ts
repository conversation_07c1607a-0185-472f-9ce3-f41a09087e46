import { auth, db } from "@/lib/firebase";
import { doc, getDoc } from "firebase/firestore";

/**
 * Interface para informações do usuário
 */
export interface UserInfo {
  uid: string;
  matricula?: string;
  fullName?: string;
  category?: string;
}

/**
 * Obtém as informações do usuário atualmente autenticado
 * @returns Informações do usuário ou null se não estiver autenticado
 */
export async function getUserInfo(): Promise<UserInfo | null> {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return null;
    }
    
    // Buscar informações adicionais do usuário no Firestore
    const userDocRef = doc(db, "users", currentUser.uid);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      
      return {
        uid: currentUser.uid,
        matricula: userData.registrationNumber,
        fullName: userData.fullName,
        category: userData.category
      };
    }
    
    // Se não houver documento do usuário, retornar apenas o UID
    return {
      uid: currentUser.uid
    };
  } catch (error) {
    return null;
  }
}

/**
 * Verifica se o usuário está autenticado
 * @returns true se o usuário estiver autenticado, false caso contrário
 */
export function isAuthenticated(): boolean {
  return !!auth.currentUser;
}

/**
 * Obtém o ID do usuário atualmente autenticado
 * @returns ID do usuário ou null se não estiver autenticado
 */
export function getCurrentUserId(): string | null {
  return auth.currentUser?.uid || null;
}
