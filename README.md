This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

### Environment Variables

Before running the application, you need to set up the environment variables:

1. Copy the `.env.local.example` file to `.env.local`:

```bash
cp .env.local.example .env.local
```

2. Update the values in `.env.local` with your own API keys and configuration.

### Running the Development Server

After setting up the environment variables, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## External APIs

This project uses the following external APIs:

### OpenCage Geocoding API

Used for location autocomplete and geocoding functionality. You need to obtain an API key from [OpenCage Data](https://opencagedata.com/).

- **API Documentation**: [OpenCage API Docs](https://opencagedata.com/api)
- **Free Tier**: 2,500 requests per day
- **Environment Variable**: `NEXT_PUBLIC_OPENCAGE_API_KEY`

## Project Utilities

### Toast Notifications

This project uses a custom toast notification system built on top of [Sonner](https://sonner.emilkowal.ski/) to display notifications to users. The toast utility includes features to prevent duplicate notifications and ensure proper styling in both light and dark modes.

#### Usage

Instead of using the `toast` function directly from Sonner, use the utility functions from `lib/toast-utils.ts`:

```typescript
import {
  showSuccessToast,
  showErrorToast,
  showInfoToast,
  showWarningToast,
  showLoadingToast,
  dismissToast,
} from "@/lib/toast-utils";

// Show a success toast
showSuccessToast("Operation successful", {
  description: "Your changes have been saved.",
});

// Show an error toast
showErrorToast("Error occurred", {
  description: "Could not save your changes.",
});

// Show a loading toast and dismiss it later
const loadingToast = showLoadingToast("Processing...");
// ... perform operation
dismissToast(loadingToast);
```

These utility functions prevent duplicate notifications from appearing when the same message is shown multiple times in quick succession.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
