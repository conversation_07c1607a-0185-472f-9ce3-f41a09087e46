import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe,
  where,
  getDoc,
  setDoc
} from "firebase/firestore";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

export interface Abastecimento {
  quilometros: number;
  litros: number;
  precoLitro: number;
  precoTotal: number;
}

export interface Condutor {
  id: string;
  userId: string;
  userName: string;
  viaturaId: string;
  viaturaInfo: string; // Formato: "Matricula - Modelo"
  quilometrosIniciais: number;
  quilometrosFinais: number | null;
  dataHoraInicio: Date;
  dataHoraFim: Date | null;
  abastecimento: Abastecimento | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy?: string;
  teamId?: string; // ID da equipa a que pertence o registo
}

const COLLECTION_NAME = "condutores";

// Adicionar um novo registo de condutor
export async function addCondutor(condutorData: Omit<Condutor, "id" | "createdAt" | "updatedAt">): Promise<string> {
  try {
    // Log detalhado dos dados recebidos
    console.log("Adicionando condutor com dados:", {
      ...condutorData,
      createdBy: condutorData.createdBy ? "[definido]" : "[não definido]",
      userId: condutorData.userId ? "[definido]" : "[não definido]",
      teamId: condutorData.teamId || "[não definido]",
    });

    // Verificar se o teamId está definido - isso é crítico para a visibilidade bilateral
    if (!condutorData.teamId) {
      console.warn("ATENÇÃO: Adicionando condutor sem teamId definido. Isso pode causar problemas de visibilidade!");
    }

    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...condutorData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    console.log("Condutor adicionado com sucesso. ID:", docRef.id);
    console.log("TeamId do condutor adicionado:", condutorData.teamId || "[não definido]");

    // Registar atividade de criação
    if (condutorData.createdBy) {
      const userInfo = await getUser(condutorData.createdBy);
      await logCreationActivity(
        ActivityType.CONDUTOR_CREATED,
        "Novo Condutor de Serviço",
        `Iniciou serviço como condutor${condutorData.viaturaInfo ? ` na viatura ${condutorData.viaturaInfo}` : ''}`,
        condutorData.createdBy,
        condutorData.teamId || userInfo?.teamId,
        {
          userName: condutorData.userName,
          viaturaInfo: condutorData.viaturaInfo,
          dataHoraInicio: condutorData.dataHoraInicio
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar registo de condutor:", error);
    throw error;
  }
}

// Obter todos os registos de condutores
export async function getCondutores(teamId?: string, userId?: string): Promise<Condutor[]> {
  try {
    let q;

    // Prioridade 1: Se teamId for fornecido, filtrar por equipa
    // Isso garante que todos os membros da mesma equipa vejam os mesmos registos
    if (teamId) {
      console.log("Filtrando condutores por teamId:", teamId);

      // Se o teamId for "admin", mostrar todos os registos com teamId "admin" e registos sem teamId
      if (teamId === "admin") {
        console.log("Admin: mostrando todos os registos");
        q = query(
          collection(db, COLLECTION_NAME),
          orderBy("dataHoraInicio", "desc")
        );
      } else {
        // Para usuários normais, mostrar apenas registos da sua equipa
        q = query(
          collection(db, COLLECTION_NAME),
          where("teamId", "==", teamId),
          orderBy("dataHoraInicio", "desc")
        );
      }
    }
    // Prioridade 2: Se não houver teamId mas houver userId, filtrar por usuário
    // Isso é para usuários que não pertencem a nenhuma equipa
    else if (userId) {
      console.log("Filtrando condutores por userId (responsável):", userId);
      // Alterado para filtrar por userId (responsável) em vez de userId (condutor)
      // Isso garante que o comportamento seja consistente com o serviço de registos
      q = query(
        collection(db, COLLECTION_NAME),
        where("userId", "==", userId),
        orderBy("dataHoraInicio", "desc")
      );
    } else {
      // Caso contrário, obter todos os registos (apenas para admin)
      console.log("Obtendo todos os condutores (admin)");
      q = query(collection(db, COLLECTION_NAME), orderBy("dataHoraInicio", "desc"));
    }

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.userId || "",
        userName: data.userName || "",
        viaturaId: data.viaturaId || "",
        viaturaInfo: data.viaturaInfo || "",
        quilometrosIniciais: data.quilometrosIniciais || 0,
        quilometrosFinais: data.quilometrosFinais || null,
        dataHoraInicio: data.dataHoraInicio ? new Date(data.dataHoraInicio.toDate()) : new Date(),
        dataHoraFim: data.dataHoraFim ? new Date(data.dataHoraFim.toDate()) : null,
        abastecimento: data.abastecimento || null,
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()) : new Date(),
        updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()) : new Date(),
        createdBy: data.createdBy || "",
      } as Condutor;
    });
  } catch (error) {
    console.error("Erro ao obter registos de condutores:", error);
    throw error;
  }
}

// Obter um registo de condutor específico
export async function getCondutor(id: string): Promise<Condutor | null> {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        userId: data.userId || "",
        userName: data.userName || "",
        viaturaId: data.viaturaId || "",
        viaturaInfo: data.viaturaInfo || "",
        quilometrosIniciais: data.quilometrosIniciais || 0,
        quilometrosFinais: data.quilometrosFinais || null,
        dataHoraInicio: data.dataHoraInicio ? new Date(data.dataHoraInicio.toDate()) : new Date(),
        dataHoraFim: data.dataHoraFim ? new Date(data.dataHoraFim.toDate()) : null,
        abastecimento: data.abastecimento || null,
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()) : new Date(),
        updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()) : new Date(),
        createdBy: data.createdBy || "",
      } as Condutor;
    }

    return null;
  } catch (error) {
    console.error("Erro ao obter registo de condutor:", error);
    throw error;
  }
}

// Atualizar um registo de condutor existente
export async function updateCondutor(id: string, condutorData: Partial<Omit<Condutor, "id" | "createdAt" | "updatedAt">>, userId?: string): Promise<void> {
  try {
    // Log detalhado dos dados recebidos
    console.log("Atualizando condutor ID:", id);
    console.log("Dados para atualização:", {
      ...condutorData,
      createdBy: condutorData.createdBy ? "[definido]" : "[não definido]",
      teamId: condutorData.teamId || "[não definido]",
    });

    // Primeiro, obter o documento atual para verificar os dados
    const condutorRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(condutorRef);

    // Verificar se o documento existe
    if (!docSnap.exists()) {
      // Se o ID for inválido, verificar se é uma tentativa de criar um novo documento
      if (condutorData.userId && condutorData.viaturaId) {
        console.log(`Condutor com ID ${id} não encontrado, mas temos dados suficientes para criar um novo.`);
        // Criar um novo documento com o ID fornecido
        await setDoc(condutorRef, {
          ...condutorData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
        console.log(`Novo condutor criado com ID: ${id}`);
        return;
      } else {
        throw new Error(`Condutor com ID ${id} não encontrado`);
      }
    }

    const dadosAtuais = docSnap.data();
    console.log("Dados atuais do condutor:", {
      ...dadosAtuais,
      createdBy: dadosAtuais.createdBy ? "[definido]" : "[não definido]",
      teamId: dadosAtuais.teamId || "[não definido]",
    });

    // Verificar se o usuário foi alterado
    let newTeamId = condutorData.teamId || dadosAtuais.teamId;
    if (condutorData.userId && condutorData.userId !== dadosAtuais.userId) {
      console.log("Usuário alterado. Obtendo teamId do novo usuário...");
      try {
        // Obter o teamId do novo usuário
        const userDoc = await getDoc(doc(db, "users", condutorData.userId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.teamId) {
            console.log(`Novo teamId encontrado para o usuário: ${userData.teamId}`);
            newTeamId = userData.teamId;
          } else {
            console.log("Novo usuário não tem teamId definido.");
          }
        } else {
          console.log("Usuário não encontrado.");
        }
      } catch (userError) {
        console.error("Erro ao obter informações do novo usuário:", userError);
        // Continuar mesmo se houver erro ao obter o teamId
      }
    }

    // Criar um objeto limpo sem campos undefined
    const cleanData: Record<string, any> = {};

    // Copiar apenas campos definidos
    Object.entries(condutorData).forEach(([key, value]) => {
      if (value !== undefined) {
        cleanData[key] = value;
      }
    });

    // Garantir que o teamId seja atualizado corretamente e nunca seja undefined
    const updateData = {
      ...cleanData,
      teamId: newTeamId || "", // Usar o teamId do novo usuário ou uma string vazia para evitar undefined
      updatedAt: serverTimestamp(),
    };

    console.log("Dados finais para atualização:", updateData);

    await updateDoc(condutorRef, updateData);
    console.log("Condutor atualizado com sucesso!");

    // Registar atividade de atualização
    if (userId && dadosAtuais) {
      const userInfo = await getUser(userId);
      await logUpdateActivity(
        ActivityType.CONDUTOR_UPDATED,
        "Condutor Atualizado",
        `Atualizou o registo de condutor: ${dadosAtuais.userName}`,
        userId,
        updateData.teamId || userInfo?.teamId,
        {
          userName: dadosAtuais.userName,
          viaturaInfo: dadosAtuais.viaturaInfo
        }
      );
    }
  } catch (error) {
    console.error("Erro ao atualizar registo de condutor:", error);
    throw error;
  }
}

// Finalizar um serviço de condutor (adicionar quilômetros finais e data/hora de fim)
export async function finalizarServico(
  id: string,
  quilometrosFinais: number,
  dataHoraFim: Date,
  abastecimento: Abastecimento | null = null,
  atualizadoPor: string = "N/A"
): Promise<void> {
  try {
    const updateData: any = {
      quilometrosFinais,
      dataHoraFim,
      atualizadoPor,
      updatedAt: serverTimestamp(),
    };

    if (abastecimento) {
      updateData.abastecimento = abastecimento;
    }

    await updateDoc(doc(db, COLLECTION_NAME, id), updateData);
  } catch (error) {
    console.error("Erro ao finalizar serviço de condutor:", error);
    throw error;
  }
}

// Excluir um registo de condutor
export async function deleteCondutor(id: string, userId: string): Promise<void> {
  try {
    // Obter dados do condutor antes de excluir para registar a atividade
    const condutorRef = doc(db, COLLECTION_NAME, id);
    const condutorSnap = await getDoc(condutorRef);
    const condutorData = condutorSnap.exists() ? condutorSnap.data() : null;

    await deleteDoc(doc(db, COLLECTION_NAME, id));

    // Registar atividade de eliminação
    if (condutorData) {
      const userInfo = await getUser(userId);
      await logDeletionActivity(
        ActivityType.CONDUTOR_DELETED,
        "Condutor Eliminado",
        `Eliminou o registo de condutor: ${condutorData.userName}`,
        userId,
        userInfo?.teamId,
        {
          userName: condutorData.userName,
          viaturaInfo: condutorData.viaturaInfo
        }
      );
    }
  } catch (error) {
    console.error("Erro ao excluir registo de condutor:", error);
    throw error;
  }
}

// Configurar um listener para mudanças nos registos de condutores
export function listenToCondutores(callback: (condutores: Condutor[]) => void, teamId?: string, userId?: string): Unsubscribe {
  let q;

  // Prioridade 1: Se teamId for fornecido, filtrar por equipa
  // Isso garante que todos os membros da mesma equipa vejam os mesmos registos
  if (teamId) {
    console.log("Filtrando condutores por teamId:", teamId);

    // Se o teamId for "admin", mostrar todos os registos com teamId "admin" e registos sem teamId
    if (teamId === "admin") {
      console.log("Admin: mostrando todos os registos");
      q = query(
        collection(db, COLLECTION_NAME),
        orderBy("dataHoraInicio", "desc")
      );
    } else {
      // Para usuários normais, mostrar apenas registos da sua equipa
      q = query(
        collection(db, COLLECTION_NAME),
        where("teamId", "==", teamId),
        orderBy("dataHoraInicio", "desc")
      );
    }
  }
  // Prioridade 2: Se não houver teamId mas houver userId, filtrar por usuário
  // Isso é para usuários que não pertencem a nenhuma equipa
  else if (userId) {
    console.log("Filtrando condutores por userId (responsável):", userId);
    // Alterado para filtrar por userId (responsável) em vez de userId (condutor)
    // Isso garante que o comportamento seja consistente com o serviço de registos
    q = query(
      collection(db, COLLECTION_NAME),
      where("userId", "==", userId),
      orderBy("dataHoraInicio", "desc")
    );
  } else {
    // Caso contrário, obter todos os registos (apenas para admin)
    console.log("Obtendo todos os condutores (admin)");
    q = query(collection(db, COLLECTION_NAME), orderBy("dataHoraInicio", "desc"));
  }

  return onSnapshot(q, (querySnapshot) => {
    const condutores = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.userId || "",
        userName: data.userName || "",
        viaturaId: data.viaturaId || "",
        viaturaInfo: data.viaturaInfo || "",
        quilometrosIniciais: data.quilometrosIniciais || 0,
        quilometrosFinais: data.quilometrosFinais || null,
        dataHoraInicio: data.dataHoraInicio ? new Date(data.dataHoraInicio.toDate()) : new Date(),
        dataHoraFim: data.dataHoraFim ? new Date(data.dataHoraFim.toDate()) : null,
        abastecimento: data.abastecimento || null,
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()) : new Date(),
        updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()) : new Date(),
        createdBy: data.createdBy || "",
      } as Condutor;
    });

    callback(condutores);
  }, (error) => {
    console.error("Erro ao escutar registos de condutores:", error);
  });
}
