// Script para gerenciar a cor do tema da PWA de acordo com o modo claro/escuro
(function() {
  // Cores do tema para cada modo
  const THEME_COLORS = {
    light: '#3B82F6', // Azul para o modo claro
    dark: '#1E40AF'   // Azul mais escuro para o modo escuro
  };

  // Função para atualizar a cor do tema
  function updateThemeColor(isDark) {
    const color = isDark ? THEME_COLORS.dark : THEME_COLORS.light;
    
    // Atualizar a meta tag theme-color
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', color);
    }
    
    // Se o navegador suporta a API de manifesto, tentar atualizar o manifesto também
    // (Nota: isso tem suporte limitado, mas incluímos para futuras implementações)
    if (navigator.serviceWorker && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'UPDATE_THEME_COLOR',
        color: color
      });
    }
  }

  // Verificar o modo inicial
  function checkInitialMode() {
    const isDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    updateThemeColor(isDark);
  }

  // Observar mudanças no modo de cor
  function watchColorSchemeChanges() {
    if (window.matchMedia) {
      const colorSchemeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      // Verificar se o navegador suporta o evento de mudança
      if (colorSchemeQuery.addEventListener) {
        colorSchemeQuery.addEventListener('change', (e) => {
          updateThemeColor(e.matches);
        });
      } else if (colorSchemeQuery.addListener) {
        // Fallback para navegadores mais antigos
        colorSchemeQuery.addListener((e) => {
          updateThemeColor(e.matches);
        });
      }
    }
  }

  // Também observar mudanças no tema da aplicação (se estiver usando um toggle de tema)
  function watchAppThemeChanges() {
    // Observar mudanças na classe 'dark' no elemento html
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          const isDark = document.documentElement.classList.contains('dark');
          updateThemeColor(isDark);
        }
      });
    });

    observer.observe(document.documentElement, { attributes: true });
  }

  // Inicializar
  document.addEventListener('DOMContentLoaded', function() {
    checkInitialMode();
    watchColorSchemeChanges();
    watchAppThemeChanges();
  });

  // Para garantir que funcione mesmo se o DOMContentLoaded já tiver disparado
  if (document.readyState === 'interactive' || document.readyState === 'complete') {
    checkInitialMode();
    watchColorSchemeChanges();
    watchAppThemeChanges();
  }
})();
