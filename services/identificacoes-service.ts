import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe,
  where
} from "firebase/firestore";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

export interface Identificacao {
  id: string;
  // Dados Pessoais
  nomeCompleto: string;
  documentoIdentificacao: string;
  numeroCC: string;
  validadeCC: string;
  nif: string;
  nacionalidade: string;
  dataNascimento: string;

  // Contato e Endereço
  telefone: string;
  morada: string;
  codigoPostal: string;
  freguesia: string;

  // Informações Complementares
  estadoCivil: string;
  profissao: string;
  escolaridade: string;

  // Filiação
  nomeMae: string;
  nomePai: string;

  // Dados da Ocorrência
  dataOcorrencia: string;
  horaOcorrencia: string;
  localOcorrencia: string;

  // Informações Adicionais
  observacoes: string;

  // Metadados
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  teamId?: string; // ID da equipa a que pertence o registo
  responsavelId?: string; // ID do responsável pela identificação
}

const COLLECTION_NAME = "identificacoes";

// Adicionar uma nova identificação
export async function addIdentificacao(identificacaoData: Omit<Identificacao, "id" | "createdAt" | "updatedAt">): Promise<string> {
  try {
    // Log detalhado dos dados recebidos
    console.log("Adicionando identificação com dados:", {
      ...identificacaoData,
      createdBy: identificacaoData.createdBy ? "[definido]" : "[não definido]",
      responsavelId: identificacaoData.responsavelId ? "[definido]" : "[não definido]",
      teamId: identificacaoData.teamId || "[não definido]",
    });

    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...identificacaoData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    console.log("Identificação adicionada com sucesso. ID:", docRef.id);

    // Registar atividade de criação
    if (identificacaoData.createdBy) {
      const userInfo = await getUser(identificacaoData.createdBy);
      await logCreationActivity(
        ActivityType.IDENTIFICACAO_CREATED,
        "Nova Identificação",
        `Criou uma nova identificação: ${identificacaoData.nomeCompleto}`,
        identificacaoData.createdBy,
        identificacaoData.teamId || userInfo?.teamId,
        {
          nomeCompleto: identificacaoData.nomeCompleto,
          numeroCC: identificacaoData.numeroCC,
          localOcorrencia: identificacaoData.localOcorrencia
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar identificação:", error);
    throw error;
  }
}

// Obter todas as identificações
export async function getIdentificacoes(teamId?: string, responsavelId?: string): Promise<Identificacao[]> {
  try {
    let q;

    // Prioridade 1: Se teamId for fornecido, filtrar APENAS por equipa
    // Isso garante que todos os membros da mesma equipa vejam as mesmas identificações
    if (teamId) {
      console.log("Filtrando identificações por teamId:", teamId);
      q = query(
        collection(db, COLLECTION_NAME),
        where("teamId", "==", teamId),
        orderBy("createdAt", "desc")
      );
    }
    // Prioridade 2: Se não houver teamId mas houver responsavelId, filtrar por responsável
    // Isso é para usuários que não pertencem a nenhuma equipa
    else if (responsavelId) {
      console.log("Filtrando identificações por responsavelId:", responsavelId);
      q = query(
        collection(db, COLLECTION_NAME),
        where("responsavelId", "==", responsavelId),
        orderBy("createdAt", "desc")
      );
    } else {
      // Caso contrário, obter todas as identificações (apenas para admin)
      console.log("Obtendo todas as identificações (admin)");
      q = query(
        collection(db, COLLECTION_NAME),
        orderBy("createdAt", "desc")
      );
    }

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as Identificacao));
  } catch (error) {
    console.error("Erro ao obter identificações:", error);
    throw error;
  }
}

// Atualizar uma identificação existente
export async function updateIdentificacao(id: string, data: Partial<Identificacao>, userId?: string): Promise<boolean> {
  try {
    // Log detalhado dos dados recebidos
    console.log("Atualizando identificação ID:", id);
    console.log("Dados para atualização:", {
      ...data,
      createdBy: data.createdBy ? "[definido]" : "[não definido]",
      responsavelId: data.responsavelId ? "[definido]" : "[não definido]",
      teamId: data.teamId || "[não definido]",
    });

    // Primeiro, obter o documento atual para verificar os dados
    const identificacaoRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(identificacaoRef);

    if (!docSnap.exists()) {
      throw new Error(`Identificação com ID ${id} não encontrada`);
    }

    const dadosAtuais = docSnap.data() as Identificacao;
    console.log("Dados atuais da identificação:", {
      ...dadosAtuais,
      createdBy: dadosAtuais.createdBy ? "[definido]" : "[não definido]",
      responsavelId: dadosAtuais.responsavelId ? "[definido]" : "[não definido]",
      teamId: dadosAtuais.teamId || "[não definido]",
    });

    // Verificar se o responsável foi alterado
    let newTeamId = data.teamId || dadosAtuais.teamId;
    if (data.responsavelId && data.responsavelId !== dadosAtuais.responsavelId) {
      console.log("Responsável alterado. Obtendo teamId do novo responsável...");
      try {
        // Obter o teamId do novo responsável
        const userDoc = await getDoc(doc(db, "users", data.responsavelId));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.teamId) {
            console.log(`Novo teamId encontrado para o responsável: ${userData.teamId}`);
            newTeamId = userData.teamId;
          } else {
            console.log("Novo responsável não tem teamId definido.");
          }
        } else {
          console.log("Usuário responsável não encontrado.");
        }
      } catch (userError) {
        console.error("Erro ao obter informações do novo responsável:", userError);
        // Continuar mesmo se houver erro ao obter o teamId
      }
    }

    // Garantir que o teamId seja atualizado corretamente
    const updateData = {
      ...data,
      teamId: newTeamId, // Usar o teamId do novo responsável ou manter o original
      updatedAt: serverTimestamp(),
    };

    console.log("Dados finais para atualização:", updateData);

    await updateDoc(identificacaoRef, updateData);
    console.log("Identificação atualizada com sucesso!");

    // Registar atividade de atualização
    if (userId && dadosAtuais) {
      const userInfo = await getUser(userId);
      await logUpdateActivity(
        ActivityType.IDENTIFICACAO_UPDATED,
        "Identificação Atualizada",
        `Atualizou a identificação: ${dadosAtuais.nomeCompleto}`,
        userId,
        updateData.teamId || userInfo?.teamId,
        {
          nomeCompleto: dadosAtuais.nomeCompleto,
          numeroCC: dadosAtuais.numeroCC,
          localOcorrencia: dadosAtuais.localOcorrencia
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao atualizar identificação:", error);
    throw error;
  }
}

// Excluir uma identificação
export async function deleteIdentificacao(id: string, userId: string): Promise<boolean> {
  try {
    // Obter dados da identificação antes de excluir para registar a atividade
    const identificacaoRef = doc(db, COLLECTION_NAME, id);
    const identificacaoSnap = await getDoc(identificacaoRef);
    const identificacaoData = identificacaoSnap.exists() ? identificacaoSnap.data() : null;

    await deleteDoc(doc(db, COLLECTION_NAME, id));

    // Registar atividade de eliminação
    if (identificacaoData) {
      const userInfo = await getUser(userId);
      await logDeletionActivity(
        ActivityType.IDENTIFICACAO_DELETED,
        "Identificação Eliminada",
        `Eliminou a identificação: ${identificacaoData.nomeCompleto}`,
        userId,
        userInfo?.teamId,
        {
          nomeCompleto: identificacaoData.nomeCompleto,
          numeroCC: identificacaoData.numeroCC,
          localOcorrencia: identificacaoData.localOcorrencia
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao excluir identificação:", error);
    throw error;
  }
}

// Escutar mudanças em identificações em tempo real
export function listenToIdentificacoes(callback: (identificacoes: Identificacao[]) => void, teamId?: string, responsavelId?: string): Unsubscribe {
  try {
    let q;

    // Prioridade 1: Se teamId for fornecido, filtrar APENAS por equipa
    // Isso garante que todos os membros da mesma equipa vejam as mesmas identificações
    if (teamId) {
      console.log("Filtrando identificações por teamId:", teamId);
      q = query(
        collection(db, COLLECTION_NAME),
        where("teamId", "==", teamId),
        orderBy("createdAt", "desc")
      );
    }
    // Prioridade 2: Se não houver teamId mas houver responsavelId, filtrar por responsável
    // Isso é para usuários que não pertencem a nenhuma equipa
    else if (responsavelId) {
      console.log("Filtrando identificações por responsavelId:", responsavelId);
      q = query(
        collection(db, COLLECTION_NAME),
        where("responsavelId", "==", responsavelId),
        orderBy("createdAt", "desc")
      );
    } else {
      // Caso contrário, obter todas as identificações (apenas para admin)
      console.log("Obtendo todas as identificações (admin)");
      q = query(
        collection(db, COLLECTION_NAME),
        orderBy("createdAt", "desc")
      );
    }

    return onSnapshot(q, (querySnapshot) => {
      const identificacoes = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as Identificacao));

      callback(identificacoes);
    }, (error) => {
      console.error("Erro ao escutar identificações:", error);
    });
  } catch (error) {
    console.error("Erro ao configurar listener de identificações:", error);
    throw error;
  }
}
