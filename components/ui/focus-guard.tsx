"use client"

import * as React from "react"

/**
 * FocusGuard é um componente utilitário que ajuda a gerenciar o foco
 * e evitar problemas de acessibilidade relacionados ao foco retido.
 * 
 * Ele é usado para limpar o foco quando componentes são desmontados
 * e garantir que o foco seja restaurado corretamente.
 */
export function FocusGuard() {
  // Referência para o elemento anterior que tinha foco
  const previousFocusRef = React.useRef<HTMLElement | null>(null)

  // Efeito para capturar o elemento com foco quando o componente é montado
  React.useEffect(() => {
    // Salvar o elemento que tinha foco antes do componente ser montado
    previousFocusRef.current = document.activeElement as HTMLElement

    // Função de limpeza para restaurar o foco quando o componente é desmontado
    return () => {
      // Verificar se há um elemento anterior para restaurar o foco
      if (previousFocusRef.current) {
        // Usar setTimeout para garantir que o foco seja restaurado após as animações
        setTimeout(() => {
          if (previousFocusRef.current) {
            try {
              previousFocusRef.current.focus({ preventScroll: true })
            } catch (e) {
              console.warn("Não foi possível restaurar o foco:", e)
            }
          }
        }, 0)
      }

      // Limpar quaisquer elementos com aria-hidden que possam estar causando problemas
      document.querySelectorAll('[aria-hidden="true"]').forEach((el) => {
        // Verificar se o elemento contém o foco atual
        if (el.contains(document.activeElement)) {
          // Se contiver, remover o aria-hidden para evitar o erro
          el.removeAttribute('aria-hidden')
        }
      })
    }
  }, [])

  // Este componente não renderiza nada visível
  return null
}

/**
 * Hook para gerenciar o foco em componentes
 * 
 * Retorna uma função para limpar o foco e restaurá-lo para o elemento anterior
 */
export function useFocusManagement() {
  // Referência para o elemento anterior que tinha foco
  const previousFocusRef = React.useRef<HTMLElement | null>(null)

  // Função para capturar o foco atual
  const captureFocus = React.useCallback(() => {
    previousFocusRef.current = document.activeElement as HTMLElement
  }, [])

  // Função para restaurar o foco para o elemento anterior
  const restoreFocus = React.useCallback(() => {
    if (previousFocusRef.current) {
      try {
        previousFocusRef.current.focus({ preventScroll: true })
      } catch (e) {
        console.warn("Não foi possível restaurar o foco:", e)
      }
    }
  }, [])

  // Função para limpar o foco atual
  const clearFocus = React.useCallback(() => {
    // Verificar se há um elemento com foco
    if (document.activeElement instanceof HTMLElement) {
      // Remover o foco
      document.activeElement.blur()
    }

    // Limpar quaisquer elementos com aria-hidden que possam estar causando problemas
    document.querySelectorAll('[aria-hidden="true"]').forEach((el) => {
      // Verificar se o elemento contém o foco atual
      if (el.contains(document.activeElement)) {
        // Se contiver, remover o aria-hidden para evitar o erro
        el.removeAttribute('aria-hidden')
      }
    })
  }, [])

  return {
    captureFocus,
    restoreFocus,
    clearFocus
  }
}
