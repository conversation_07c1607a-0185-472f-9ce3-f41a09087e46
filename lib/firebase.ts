import { initializeApp, getApps, getApp, FirebaseApp } from "firebase/app";
import { getAuth, setPersistence, browserLocalPersistence, Auth } from "firebase/auth";
import { getFirestore, Firestore } from "firebase/firestore";
import { getStorage, FirebaseStorage } from "firebase/storage";

// Configuração do Firebase usando variáveis de ambiente
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
};

// Verificar se a configuração está completa
const isConfigValid =
  firebaseConfig.apiKey &&
  firebaseConfig.authDomain &&
  firebaseConfig.projectId &&
  firebaseConfig.storageBucket &&
  firebaseConfig.messagingSenderId &&
  firebaseConfig.appId;

if (!isConfigValid) {
  throw new Error("Configuração do Firebase incompleta");
}

// Inicializar Firebase com valores padrão
let app: FirebaseApp = {} as FirebaseApp;
let auth: Auth = {} as Auth;
let db: Firestore = {} as Firestore;
let storage: FirebaseStorage = {} as FirebaseStorage;

try {
  // Verificar se o Firebase já foi inicializado
  if (!getApps().length) {
    app = initializeApp(firebaseConfig);
  } else {
    app = getApp();
  }

  // Inicializar serviços
  auth = getAuth(app);
  db = getFirestore(app);
  storage = getStorage(app);

  // Configurar persistência no lado do cliente
  if (typeof window !== 'undefined') {
    setPersistence(auth, browserLocalPersistence).catch(() => {
      // Silently handle persistence errors
    });
  }
} catch (error) {
  // Em caso de erro, criar objetos vazios para evitar quebras na aplicação
  if (!app) app = {} as unknown as FirebaseApp;
  if (!auth) auth = {} as unknown as Auth;
  if (!db) db = {} as unknown as Firestore;
  if (!storage) storage = {} as unknown as FirebaseStorage;
}

// Exportar instâncias
export { app, auth, db, storage };
