"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  IconDotsVertical,
  IconEdit,
  IconTrash,
  IconCopy
} from "@tabler/icons-react";
import { toast } from "sonner";
import { copyToClipboard } from "@/lib/clipboard-utils";
import { formatDate } from "@/lib/utils";
import { NIP } from "@/services/nips-service";
import { LocationAutocomplete } from "@/components/location-autocomplete";

interface NIPsTableProps {
  nips: NIP[];
  onDelete: (id: string) => Promise<void>;
  onEdit: (id: string, newLocal: string, newNumero: string) => Promise<void>;
}

export function NIPsTable({ nips, onDelete, onEdit }: NIPsTableProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedNIP, setSelectedNIP] = useState<NIP | null>(null);
  const [newLocal, setNewLocal] = useState("");
  const [newNumero, setNewNumero] = useState("");

  const handleCopyClick = async (nip: NIP) => {
    await copyToClipboard(nip.numero, {
      successMessage: "Número de NIP copiado",
      errorMessage: "Erro ao copiar número"
    });
  };

  const handleDeleteClick = (nip: NIP) => {
    setSelectedNIP(nip);
    setIsDeleteDialogOpen(true);
  };

  const handleEditClick = (nip: NIP) => {
    setSelectedNIP(nip);
    setNewLocal(nip.local);
    setNewNumero(nip.numero);
    setIsEditDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedNIP) return;

    try {
      await onDelete(selectedNIP.id);
      toast.success("NIP eliminado com sucesso");
    } catch (error: any) {
      toast.error("Erro ao eliminar NIP");
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedNIP(null);
    }
  };

  const confirmEdit = async () => {
    if (!selectedNIP || !newLocal.trim() || !newNumero.trim()) return;

    try {
      await onEdit(selectedNIP.id, newLocal, newNumero);
      toast.success("NIP atualizado com sucesso");
    } catch (error: any) {
      toast.error("Erro ao atualizar NIP");
    } finally {
      setIsEditDialogOpen(false);
      setSelectedNIP(null);
      setNewLocal("");
      setNewNumero("");
    }
  };

  return (
    <>
      <div className="relative flex flex-col gap-4">
        <div className="overflow-hidden rounded-lg border">
          <Table className="w-full">
            <TableHeader className="bg-muted sticky top-0 z-10">
              <TableRow>
                <TableHead className="w-[50%]">Local</TableHead>
                <TableHead className="w-[40%]">Nº NIP</TableHead>
                <TableHead className="w-[10%] text-right"></TableHead>
              </TableRow>
            </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {nips.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center text-muted-foreground">
                  Nenhum NIP encontrado.
                </TableCell>
              </TableRow>
            ) : (
              nips.map((nip) => (
                <TableRow key={nip.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="truncate">{nip.local}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{nip.numero}</div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-32">
                        <DropdownMenuItem onClick={() => handleCopyClick(nip)}>
                          <IconCopy className="size-4 mr-2" />
                          Copiar Nº
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditClick(nip)}>
                          <IconEdit className="size-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(nip)}
                          className="text-destructive focus:text-destructive"
                        >
                          <IconTrash className="size-4 mr-2" />
                          Eliminar
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>

      {/* Diálogo de confirmação para eliminar */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-xl w-[95vw]">
          <DialogHeader>
            <DialogTitle>Eliminar NIP</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja eliminar o NIP "{selectedNIP?.numero}" de "{selectedNIP?.local}"? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Eliminar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar NIP */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-xl md:max-w-2xl w-[95vw]">
          <DialogHeader>
            <DialogTitle>Editar NIP</DialogTitle>
            <DialogDescription>
              Atualize o local e o número do NIP.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="edit-local">Local</Label>
              <LocationAutocomplete
                value={newLocal}
                onChange={(value) => {
                  console.log("NIP edit dialog: local value changed to", value);
                  // Usar setTimeout para garantir que o valor seja aplicado corretamente no Safari
                  setTimeout(() => {
                    setNewLocal(value);
                  }, 0);
                }}
                placeholder="Insira o local do NIP"
                useNormalizedAddresses={false} // Usar o endereço completo, não normalizado
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-numero">Nº NIP</Label>
              <Input
                id="edit-numero"
                value={newNumero}
                onChange={(e) => setNewNumero(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={confirmEdit} disabled={!newLocal.trim() || !newNumero.trim()}>
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
