"use client";

import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { IconSearch, IconX, IconBuildingStore, IconGlass, IconMusic } from "@tabler/icons-react";
import { cn } from "@/lib/utils";
import { searchLocations, LocationResult, getAddressWithHouseNumber } from "@/services/location-service";
import { locationConfig } from "@/lib/config";

interface LocationAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  /**
   * Se verdadeiro, mostra endereços normalizados sem números de porta e códigos postais
   * @default true
   */
  useNormalizedAddresses?: boolean;
  /**
   * Se verdadeiro, remove endereços duplicados (mesmo endereço normalizado)
   * @default true
   */
  removeDuplicates?: boolean;
  /**
   * Se verdadeiro, inclui estabelecimentos personalizados nos resultados
   * @default true
   */
  includeCustomPOIs?: boolean;
  /**
   * Se verdadeiro, inclui o número de porta no endereço quando disponível
   * @default true
   */
  includeHouseNumber?: boolean;
}

export function LocationAutocomplete({
  value,
  onChange,
  placeholder = "Introduza o local",
  disabled = false,
  className,
  useNormalizedAddresses = locationConfig.addressNormalization?.normalizeAddresses ?? true,
  removeDuplicates = locationConfig.addressNormalization?.removeDuplicates ?? true,
  includeCustomPOIs = true,
  includeHouseNumber = true,
}: LocationAutocompleteProps) {
  // Detectar se estamos no Safari/iOS de forma mais robusta
  const isSafari = typeof navigator !== 'undefined' && (
    (/^((?!chrome|android).)*safari/i.test(navigator.userAgent) ||
    /iPad|iPhone|iPod/.test(navigator.userAgent)) &&
    !/CriOS/.test(navigator.userAgent) && // Excluir Chrome no iOS
    !/FxiOS/.test(navigator.userAgent) && // Excluir Firefox no iOS
    !/EdgiOS/.test(navigator.userAgent)   // Excluir Edge no iOS
  );

  // Log para debug
  useEffect(() => {
    if (isSafari) {
      console.log("Safari/iOS detectado. Ativando modo de compatibilidade.");
    }
  }, [isSafari]);
  // Usar o valor externo como fonte da verdade
  const [inputValue, setInputValue] = useState(value);
  const [results, setResults] = useState<LocationResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [debouncedValue, setDebouncedValue] = useState("");
  // Estado para controlar se o utilizador acabou de selecionar um item
  const [justSelected, setJustSelected] = useState(false);
  // Estado para controlar se o input foi modificado pelo utilizador
  const [userModified, setUserModified] = useState(false);
  const resultsRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Sincronizar o inputValue com o valor externo quando ele mudar
  useEffect(() => {
    if (value !== inputValue && !userModified) {
      console.log("Sincronizando inputValue com valor externo:", value);
      setInputValue(value);

      // Se o valor externo for definido e não vazio, forçar o fechamento dos resultados
      if (value) {
        setShowResults(false);
        setResults([]);
      }
    }


  }, [value, inputValue, userModified, isSafari, onChange]);
  // Debounce input value apenas quando modificado pelo utilizador
  useEffect(() => {
    if (!userModified) return;

    const timer = setTimeout(() => {
      setDebouncedValue(inputValue);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [inputValue, userModified]);

  // Fetch results when debounced value changes
  useEffect(() => {
    if (!debouncedValue || debouncedValue.length < 3) {
      setResults([]);
      setShowResults(false);
      return;
    }

    const fetchLocations = async () => {
      setIsLoading(true);
      try {
        // Usar o serviço de localização para buscar resultados com as opções configuradas
        const locationResults = await searchLocations(debouncedValue, {
          normalizeAddresses: useNormalizedAddresses,
          removeDuplicates: removeDuplicates,
          includeCustomPOIs: includeCustomPOIs
        });

        if (locationResults.length > 0) {
          setResults(locationResults);
          setShowResults(true);
        } else {
          setResults([]);
          setShowResults(false);
        }
      } catch (error) {
        console.error("Erro ao buscar localizações:", error);
        setResults([]);
        setShowResults(false);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLocations();
  }, [debouncedValue, useNormalizedAddresses, removeDuplicates, includeCustomPOIs]);

  // Verificar se o valor foi aplicado corretamente após a renderização (especialmente para Safari)
  useEffect(() => {
    if (isSafari && inputRef.current) {
      // Se temos um valor externo, garantir que ele seja aplicado corretamente
      if (value && inputRef.current.value !== value) {
        console.log("Corrigindo valor do input no Safari após renderização:", inputRef.current.value, "->>", value);

        // Atualizar o valor diretamente no DOM
        inputRef.current.value = value;

        // Disparar um evento de input para garantir que o navegador reconheça a mudança
        const inputEvent = new Event('input', { bubbles: true });
        inputRef.current.dispatchEvent(inputEvent);

        // Atualizar o estado interno
        setInputValue(value);
        setUserModified(false);
        setJustSelected(false);
      }
    }
  }, [isSafari, value]);

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (
        resultsRef.current &&
        !resultsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowResults(false);
        setResults([]);
      }
    };

    // Adicionar um listener para quando o input perder o foco
    const handleBlur = (event: FocusEvent) => {
      // Em dispositivos móveis, adicionamos um pequeno atraso para permitir que o toque seja processado
      setTimeout(() => {
        // Verificar se o elemento que está recebendo o foco está dentro dos resultados
        const relatedTarget = event.relatedTarget as Node;
        if (resultsRef.current && resultsRef.current.contains(relatedTarget)) {
          // Se o foco está indo para um elemento dentro dos resultados, não fechar
          return;
        }

        // Fechar os resultados imediatamente
        setShowResults(false);
        setResults([]);
      }, 100); // Pequeno atraso para dispositivos móveis
    };

    // Guardar uma referência ao input atual para usar na função de limpeza
    const currentInput = inputRef.current;

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("touchstart", handleClickOutside);
    currentInput?.addEventListener("blur", handleBlur);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchstart", handleClickOutside);
      currentInput?.removeEventListener("blur", handleBlur);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setUserModified(true);
    setJustSelected(false);

    // Sempre notificar o componente pai sobre a mudança
    onChange(newValue);

    if (newValue === "") {
      setResults([]);
      setShowResults(false);
      setDebouncedValue("");
    }
  };

  // Comentário para indicar o início da função de seleção de localização

  const handleSelectLocation = (location: LocationResult) => {
    // Verificar se temos um número de porta
    const hasHouseNumber = location.components?.house_number;

    // Determinar o valor a ser usado
    let newValue;

    if (includeHouseNumber && hasHouseNumber) {
      // Se devemos incluir o número de porta e ele está disponível, usar a função especial
      newValue = getAddressWithHouseNumber(location);
    } else if (useNormalizedAddresses && location.normalizedAddress) {
      // Usar o endereço normalizado se disponível e configurado
      newValue = location.normalizedAddress;
    } else {
      // Caso contrário, usar o endereço formatado original
      newValue = location.formatted;
    }

    console.log("Selecionando localização:", location);
    console.log("Valor a ser definido:", newValue);
    console.log("Tem número de porta:", hasHouseNumber || "não");
    console.log("Navegador Safari?", isSafari ? "Sim" : "Não");

    // Forçar o fechamento dos resultados e limpar os resultados
    setShowResults(false);
    setResults([]);

    // Limpar o valor debounced para evitar nova pesquisa
    setDebouncedValue("");

    // Tratamento especial para Safari
    if (isSafari) {
      console.log("Aplicando tratamento especial para Safari");

      // No Safari, precisamos atualizar o DOM diretamente primeiro
      if (inputRef.current) {
        inputRef.current.value = newValue;
      }

      // Atualizar o estado interno
      setInputValue(newValue);

      // Usar um atraso maior para Safari
      setTimeout(() => {
        // Notificar o componente pai sobre a mudança
        onChange(newValue);
        console.log("Safari: onChange chamado com atraso para:", newValue);

        // Marcar que acabou de selecionar um item e resetar o estado de modificação
        setJustSelected(true);
        setUserModified(false);

        // Remover o foco do input
        if (inputRef.current) {
          inputRef.current.blur();
        }

        // Adicionar um pequeno atraso antes de permitir que o input mostre resultados novamente
        setTimeout(() => {
          setJustSelected(false);
        }, 300);
      }, 100); // Atraso maior para Safari

      return;
    }

    // Para outros navegadores, o fluxo normal
    setTimeout(() => {
      // Atualizar o valor do input imediatamente
      setInputValue(newValue);

      // Notificar o componente pai sobre a mudança
      onChange(newValue);
      console.log("onChange chamado com:", newValue);

      // Marcar que acabou de selecionar um item e resetar o estado de modificação
      setJustSelected(true);
      setUserModified(false);

      // Remover o foco do input
      if (inputRef.current) {
        inputRef.current.blur();
      }

      // Adicionar um pequeno atraso antes de permitir que o input mostre resultados novamente
      setTimeout(() => {
        setJustSelected(false);
      }, 300);
    }, 10); // Pequeno atraso para outros navegadores
  };

  const handleClear = () => {
    setInputValue("");
    onChange("");
    setResults([]);
    setShowResults(false);
    inputRef.current?.focus();
  };

  return (
    <div className="relative">

      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          className={cn("pr-8", className)}
          onFocus={() => {
            // Se acabou de selecionar um item, não mostrar os resultados novamente
            if (justSelected) {
              return;
            }

            // Resetar o estado de modificação quando o input recebe foco
            // para permitir que o usuário digite algo novo
            setUserModified(true);
          }}
        />
        {inputValue && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="absolute right-0 top-0 h-full px-3 py-2"
            onClick={handleClear}
            disabled={disabled}
          >
            <IconX className="size-4" />
            <span className="sr-only">Limpar</span>
          </Button>
        )}
      </div>

      {showResults && (
        <div
          ref={resultsRef}
          className="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-popover p-1 shadow-md max-w-[95vw] sm:max-w-full"
        >
          <div className="text-xs text-muted-foreground px-2 py-1.5">
            Resultados encontrados
          </div>
          {/* Área de resultados com suporte a scroll em dispositivos móveis */}
          <div className="touch-pan-y">
            {results.map((result, index) => (
              <div
                key={index}
                className="flex w-full items-center px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground rounded-sm text-left overflow-hidden"
              >
                <button
                  className="flex w-full items-center text-left"
                  onClick={() => handleSelectLocation(result)}
                  type="button"
                >
              {result.isCustomPOI ? (
                result.poiType === 'bar' ? (
                  <IconGlass className="mr-2 size-3.5 text-blue-500 flex-shrink-0" />
                ) : result.poiType === 'discoteca' ? (
                  <IconMusic className="mr-2 size-3.5 text-purple-500 flex-shrink-0" />
                ) : (
                  <IconBuildingStore className="mr-2 size-3.5 text-green-500 flex-shrink-0" />
                )
              ) : (
                <IconSearch className="mr-2 size-3.5 text-muted-foreground flex-shrink-0" />
              )}
              <div className="flex flex-col truncate">
                <span className="truncate">
                  {useNormalizedAddresses && result.normalizedAddress
                    ? result.normalizedAddress
                    : result.formatted}
                  {!useNormalizedAddresses && result.components?.house_number && (
                    <span className="font-semibold"> {result.components.house_number}</span>
                  )}
                </span>
                {result.components?.house_number && (
                  <span className="text-xs text-muted-foreground">
                    Número: {result.components.house_number}
                  </span>
                )}
                {result.isCustomPOI && (
                  <span className="text-xs text-muted-foreground capitalize">
                    {result.poiType}
                  </span>
                )}
              </div>
              </button>
            </div>
          ))}
          </div>
        </div>
      )}

      {isLoading && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      )}
    </div>
  );
}
