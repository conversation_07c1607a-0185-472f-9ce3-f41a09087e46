"use client";

import { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/auth-context";
import { LocationAutocomplete } from "@/components/location-autocomplete";

interface NIPFormProps {
  onSaveComplete: (nipData: {
    local: string;
    numero: string;
    createdBy: string;
  }) => void;
}

export function NIPForm({ onSaveComplete }: NIPFormProps) {
  const [local, setLocal] = useState("");
  const [numero, setNumero] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();

  const handleLocalChange = (value: string) => {
    console.log("NIP form: local value changed to", value);
    // Usar setTimeout para garantir que o valor seja aplicado corretamente no Safari
    setTimeout(() => {
      setLocal(value);
    }, 0);
  };

  const handleNumeroChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNumero(e.target.value);
  };

  const resetForm = () => {
    setLocal("");
    setNumero("");
  };

  const handleSave = async () => {
    if (!local.trim()) {
      toast.error("Local obrigatório");
      return;
    }

    if (!numero.trim()) {
      toast.error("Número de NIP obrigatório");
      return;
    }

    if (!user) {
      toast.error("Utilizador não autenticado");
      return;
    }

    setIsSubmitting(true);
    const loadingToast = toast.loading("A guardar NIP...");

    try {
      // Preparar os dados do NIP
      const nipData = {
        local: local,
        numero: numero,
        createdBy: user.uid,
      };

      // Notificar o componente pai sobre o NIP guardado
      onSaveComplete(nipData);

      toast.dismiss(loadingToast);
      toast.success("NIP guardado com sucesso");

      resetForm();
    } catch (error: any) {
      toast.dismiss(loadingToast);
      toast.error("Erro ao guardar NIP");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4 py-2">
      <div className="space-y-2">
        <Label htmlFor="local">Local</Label>
        <LocationAutocomplete
          value={local}
          onChange={handleLocalChange}
          placeholder="Insira o local do NIP"
          disabled={isSubmitting}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="numero">Nº NIP</Label>
        <Input
          id="numero"
          placeholder="Insira o número do NIP"
          value={numero}
          onChange={handleNumeroChange}
          disabled={isSubmitting}
        />
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-end gap-2 mt-6">
        <Button
          variant="outline"
          type="button"
          onClick={resetForm}
          disabled={isSubmitting || (!local.trim() && !numero.trim())}
          className="sm:order-first"
        >
          Limpar Campos
        </Button>
        <Button
          onClick={handleSave}
          disabled={isSubmitting || !local.trim() || !numero.trim()}
        >
          {isSubmitting ? "A guardar..." : "Guardar NIP"}
        </Button>
      </div>
    </div>
  );
}
