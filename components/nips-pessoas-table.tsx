"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  IconDotsVertical,
  IconCopy,
  IconEdit,
  IconTrash,
  IconEye,
} from "@tabler/icons-react";
import { toast } from "sonner";
import { copyToClipboard } from "@/lib/clipboard-utils";
import { formatDate } from "@/lib/utils";
import { NIPPessoa } from "@/services/nips-pessoas-service";

interface NIPsPessoasTableProps {
  nipsPessoas: NIPPessoa[];
  onDelete: (id: string) => Promise<void>;
  onEdit: (id: string, newNomeCompleto: string, newNumero: string, newObservacoes?: string) => Promise<void>;
}

export function NIPsPessoasTable({ nipsPessoas, onDelete, onEdit }: NIPsPessoasTableProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedNIPPessoa, setSelectedNIPPessoa] = useState<NIPPessoa | null>(null);
  const [newNomeCompleto, setNewNomeCompleto] = useState("");
  const [newNumero, setNewNumero] = useState("");
  const [newObservacoes, setNewObservacoes] = useState("");

  const handleCopyClick = async (nipPessoa: NIPPessoa) => {
    await copyToClipboard(nipPessoa.numero, {
      successMessage: "Número de NIP copiado",
      errorMessage: "Erro ao copiar número"
    });
  };

  const handleDeleteClick = (nipPessoa: NIPPessoa) => {
    setSelectedNIPPessoa(nipPessoa);
    setIsDeleteDialogOpen(true);
  };

  const handleViewClick = (nipPessoa: NIPPessoa) => {
    setSelectedNIPPessoa(nipPessoa);
    setIsViewDialogOpen(true);
  };

  const handleEditClick = (nipPessoa: NIPPessoa) => {
    setSelectedNIPPessoa(nipPessoa);
    setNewNomeCompleto(nipPessoa.nomeCompleto);
    setNewNumero(nipPessoa.numero);
    setNewObservacoes(nipPessoa.observacoes || "");
    setIsEditDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedNIPPessoa) return;

    try {
      await onDelete(selectedNIPPessoa.id);
      toast.success("NIP Pessoa excluído com sucesso");
    } catch (error) {
      toast.error("Erro ao excluir NIP Pessoa");
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedNIPPessoa(null);
    }
  };

  const handleConfirmEdit = async () => {
    if (!selectedNIPPessoa) return;

    if (!newNomeCompleto.trim()) {
      toast.error("Nome completo obrigatório");
      return;
    }

    if (!newNumero.trim()) {
      toast.error("Número de NIP obrigatório");
      return;
    }

    try {
      await onEdit(selectedNIPPessoa.id, newNomeCompleto.trim(), newNumero.trim(), newObservacoes.trim() || undefined);
      toast.success("NIP Pessoa atualizado com sucesso");
    } catch (error) {
      toast.error("Erro ao atualizar NIP Pessoa");
    } finally {
      setIsEditDialogOpen(false);
      setSelectedNIPPessoa(null);
      setNewNomeCompleto("");
      setNewNumero("");
      setNewObservacoes("");
    }
  };

  return (
    <>
      <div className="relative flex flex-col gap-4">
        <div className="overflow-hidden rounded-lg border">
          <Table className="w-full">
            <TableHeader className="bg-muted sticky top-0 z-10">
              <TableRow>
                <TableHead className="w-[40%]">Nome Completo</TableHead>
                <TableHead className="w-[25%]">Nº NIP</TableHead>
                <TableHead className="w-[25%]">Observações</TableHead>
                <TableHead className="w-[10%] text-right"></TableHead>
              </TableRow>
            </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {nipsPessoas.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                  Nenhum NIP Pessoa encontrado.
                </TableCell>
              </TableRow>
            ) : (
              nipsPessoas.map((nipPessoa) => (
                <TableRow key={nipPessoa.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="truncate">{nipPessoa.nomeCompleto}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{nipPessoa.numero}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate text-muted-foreground text-sm">
                      {nipPessoa.observacoes
                        ? nipPessoa.observacoes.length > 50
                          ? `${nipPessoa.observacoes.substring(0, 50)}...`
                          : nipPessoa.observacoes
                        : "—"
                      }
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-32">
                        <DropdownMenuItem onClick={() => handleViewClick(nipPessoa)}>
                          <IconEye className="size-4 mr-2" />
                          Visualizar
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyClick(nipPessoa)}>
                          <IconCopy className="size-4 mr-2" />
                          Copiar Nº
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditClick(nipPessoa)}>
                          <IconEdit className="size-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(nipPessoa)}
                          className="text-destructive focus:text-destructive"
                        >
                          <IconTrash className="size-4 mr-2" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
          </Table>
        </div>
      </div>

      {/* Diálogo de confirmação de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza de que deseja excluir o NIP Pessoa de{" "}
              <strong>{selectedNIPPessoa?.nomeCompleto}</strong>?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar NIP Pessoa */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-xl md:max-w-2xl w-[95vw]">
          <DialogHeader>
            <DialogTitle>Editar NIP Pessoa</DialogTitle>
            <DialogDescription>
              Atualize o nome completo, número do NIP e observações.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="edit-nomeCompleto">Nome Completo</Label>
              <Input
                id="edit-nomeCompleto"
                value={newNomeCompleto}
                onChange={(e) => setNewNomeCompleto(e.target.value)}
                placeholder="Insira o nome completo da pessoa"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-numero">Nº NIP</Label>
              <Input
                id="edit-numero"
                value={newNumero}
                onChange={(e) => setNewNumero(e.target.value)}
                placeholder="Insira o número do NIP"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-observacoes">Observações</Label>
              <Textarea
                id="edit-observacoes"
                value={newObservacoes}
                onChange={(e) => setNewObservacoes(e.target.value)}
                placeholder="Insira observações sobre a pessoa (opcional)"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button onClick={handleConfirmEdit}>
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para visualizar NIP Pessoa */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-xl md:max-w-2xl w-[95vw]">
          <DialogHeader>
            <DialogTitle>Visualizar NIP Pessoa</DialogTitle>
            <DialogDescription>
              Informações completas do NIP Pessoa.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Nome Completo</Label>
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm">{selectedNIPPessoa?.nomeCompleto}</p>
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Nº NIP</Label>
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm font-mono">{selectedNIPPessoa?.numero}</p>
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Observações</Label>
              <div className="p-3 bg-muted rounded-md min-h-[80px]">
                <p className="text-sm whitespace-pre-wrap">
                  {selectedNIPPessoa?.observacoes || "Nenhuma observação registrada."}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Data de Criação</Label>
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm">
                  {selectedNIPPessoa?.createdAt ? formatDate(selectedNIPPessoa.createdAt) : "Não disponível"}
                </p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
