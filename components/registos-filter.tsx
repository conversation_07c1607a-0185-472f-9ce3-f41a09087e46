"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { IconFilter, IconSearch, IconX } from "@tabler/icons-react";

export interface FilterOptions {
  tipoRegisto: string;
  nppNuipc: string;
  responsavelId: string;
  dataInicio: Date | null;
  dataFim: Date | null;
  local: string;
  tipoCrime: string;
  teamId?: string;
  createdBy?: string;
}

interface RegistosFilterProps {
  onFilter: (options: FilterOptions) => void;
  users: { id: string; name: string; registrationNumber?: string }[];
  initialFilters?: FilterOptions;
  isAdmin?: boolean;
  teams?: { id: string; name: string }[];
}

export function RegistosFilter({ onFilter, users, initialFilters, isAdmin = false, teams = [] }: RegistosFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isFiltering, setIsFiltering] = useState(false);

  // Estado único para todos os filtros
  const [filters, setFilters] = useState<FilterOptions>({
    tipoRegisto: "all",
    nppNuipc: "",
    responsavelId: "all",
    dataInicio: null,
    dataFim: null,
    local: "",
    tipoCrime: "",
    teamId: "all",
    createdBy: "",
  });

  // Efeito para atualizar os filtros quando o componente pai envia novos filtros
  useEffect(() => {
    if (initialFilters) {
      setFilters({
        tipoRegisto: initialFilters.tipoRegisto || "all",
        nppNuipc: initialFilters.nppNuipc || "",
        responsavelId: initialFilters.responsavelId || "all",
        dataInicio: initialFilters.dataInicio,
        dataFim: initialFilters.dataFim,
        local: initialFilters.local || "",
        tipoCrime: initialFilters.tipoCrime || "",
        teamId: initialFilters.teamId || "all",
        createdBy: initialFilters.createdBy || "",
      });

      // Verificar se algum filtro está ativo
      const isActive: boolean = Boolean(
        initialFilters.tipoRegisto !== "all" ||
        initialFilters.nppNuipc !== "" ||
        initialFilters.responsavelId !== "all" ||
        initialFilters.dataInicio !== null ||
        initialFilters.dataFim !== null ||
        initialFilters.local !== "" ||
        initialFilters.tipoCrime !== "" ||
        (initialFilters.teamId && initialFilters.teamId !== "all") ||
        (initialFilters.createdBy && initialFilters.createdBy !== "")
      );

      setIsFiltering(isActive);
    }
  }, [initialFilters]);

  // Função para aplicar os filtros
  const aplicarFiltros = () => {
    // Verificar se algum filtro está ativo
    const isActive: boolean = Boolean(
      filters.tipoRegisto !== "all" ||
      filters.nppNuipc !== "" ||
      filters.responsavelId !== "all" ||
      filters.dataInicio !== null ||
      filters.dataFim !== null ||
      filters.local !== "" ||
      filters.tipoCrime !== "" ||
      filters.teamId !== "all" ||
      filters.createdBy !== ""
    );

    setIsFiltering(isActive);

    onFilter(filters);
  };

  // Função para atualizar um campo específico do filtro sem aplicar
  const updateFilterValue = (field: keyof FilterOptions, value: any) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);

    // Verificar se algum filtro está ativo
    const isActive: boolean = Boolean(
      newFilters.tipoRegisto !== "all" ||
      newFilters.nppNuipc !== "" ||
      newFilters.responsavelId !== "all" ||
      newFilters.dataInicio !== null ||
      newFilters.dataFim !== null ||
      newFilters.local !== "" ||
      newFilters.tipoCrime !== "" ||
      newFilters.teamId !== "all" ||
      newFilters.createdBy !== ""
    );

    setIsFiltering(isActive);
  };

  // Função para atualizar um campo específico do filtro e aplicar imediatamente
  const updateFilterAndApply = (field: keyof FilterOptions, value: any) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);

    // Verificar se algum filtro está ativo
    const isActive: boolean = Boolean(
      newFilters.tipoRegisto !== "all" ||
      newFilters.nppNuipc !== "" ||
      newFilters.responsavelId !== "all" ||
      newFilters.dataInicio !== null ||
      newFilters.dataFim !== null ||
      newFilters.local !== "" ||
      newFilters.tipoCrime !== "" ||
      newFilters.teamId !== "all" ||
      newFilters.createdBy !== ""
    );

    setIsFiltering(isActive);

    onFilter(newFilters);
  };

  // Função para limpar os filtros
  const limparFiltros = () => {
    const resetFilters = {
      tipoRegisto: "all",
      nppNuipc: "",
      responsavelId: "all",
      dataInicio: null,
      dataFim: null,
      local: "",
      tipoCrime: "",
      teamId: "all",
      createdBy: "",
    };

    setFilters(resetFilters);
    setIsFiltering(false);
    onFilter(resetFilters);
  };

  return (
    <div className="space-y-4">
      {/* Botão de filtro (sempre visível) */}
      <div className="flex justify-end">
        <Button
          variant={isExpanded ? "default" : "outline"}
          onClick={() => setIsExpanded(!isExpanded)}
          className="shrink-0"
        >
          <IconFilter className="size-4 mr-2" />
          Filtros
          {isFiltering && (
            <Badge variant="secondary" className="ml-2 px-1 py-0 text-xs">
              Ativos
            </Badge>
          )}
        </Button>
      </div>

      {/* Filtros (expansíveis) */}
      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 border rounded-md w-full">
          {/* Barra de pesquisa NPP/NUIPC */}
          <div className="col-span-full">
            <label className="text-sm font-medium mb-1.5 block">NPP/NUIPC</label>
            <div className="relative">
              <Input
                placeholder="Pesquisar por NPP/NUIPC..."
                value={filters.nppNuipc}
                onChange={(e) => {
                  const newValue = e.target.value;
                  updateFilterValue("nppNuipc", newValue);
                }}
                className="pl-9"
              />
              <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
            </div>
          </div>
          <div>
            <label className="text-sm font-medium mb-1.5 block">Tipo de Registo</label>
            <Select
              value={filters.tipoRegisto}
              onValueChange={(value) => {
                updateFilterValue("tipoRegisto", value);
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Todos os tipos" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os tipos</SelectItem>
                <SelectItem value="detencao">Detenção</SelectItem>
                <SelectItem value="autoNoticia">Auto de Notícia</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-1.5 block">Responsável</label>
            <Select
              value={filters.responsavelId}
              onValueChange={(value) => {
                updateFilterValue("responsavelId", value);
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Todos os responsáveis" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os responsáveis</SelectItem>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    <div className="flex justify-between w-full">
                      <span>{user.name}</span>
                      {user.registrationNumber && <span className="text-muted-foreground ml-2">{user.registrationNumber}</span>}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-1.5 block">Data Inicial</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left">
                  {filters.dataInicio ? (
                    format(filters.dataInicio, "dd/MM/yyyy", { locale: ptBR })
                  ) : (
                    <span className="text-muted-foreground">Selecionar data</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dataInicio || undefined}
                  onSelect={(date) => {
                    updateFilterValue("dataInicio", date);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div>
            <label className="text-sm font-medium mb-1.5 block">Data Final</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left">
                  {filters.dataFim ? (
                    format(filters.dataFim, "dd/MM/yyyy", { locale: ptBR })
                  ) : (
                    <span className="text-muted-foreground">Selecionar data</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dataFim || undefined}
                  onSelect={(date) => {
                    updateFilterValue("dataFim", date);
                  }}
                  initialFocus
                  disabled={(date) =>
                    filters.dataInicio ? date < filters.dataInicio : false
                  }
                />
              </PopoverContent>
            </Popover>
          </div>

          <div>
            <label className="text-sm font-medium mb-1.5 block">Local</label>
            <Input
              placeholder="Filtrar por local"
              value={filters.local}
              onChange={(e) => {
                const newValue = e.target.value;
                updateFilterValue("local", newValue);
              }}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-1.5 block">Tipo de Crime</label>
            <Input
              placeholder="Filtrar por tipo de crime"
              value={filters.tipoCrime}
              onChange={(e) => {
                const newValue = e.target.value;
                updateFilterValue("tipoCrime", newValue);
              }}
            />
          </div>

          {/* Filtros adicionais para administradores */}
          {isAdmin && (
            <>
              {/* Filtro por Equipa */}
              <div>
                <label className="text-sm font-medium mb-1.5 block">Equipa</label>
                <Select
                  value={filters.teamId}
                  onValueChange={(value) => {
                    updateFilterValue("teamId", value);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Todas as equipas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas as equipas</SelectItem>
                    <SelectItem value="admin">Administradores</SelectItem>
                    {teams.map((team) => (
                      <SelectItem key={team.id} value={team.id}>
                        {team.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>


            </>
          )}

          <div className="col-span-full flex justify-end gap-2 mt-2">
            <Button
              variant="outline"
              onClick={limparFiltros}
              className="shrink-0"
            >
              <IconX className="size-4 mr-2" />
              Limpar Filtros
            </Button>
            <Button
              onClick={aplicarFiltros}
              className="shrink-0"
            >
              <IconFilter className="size-4 mr-2" />
              Aplicar Filtros
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
