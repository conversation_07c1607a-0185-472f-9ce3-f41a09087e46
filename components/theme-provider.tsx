"use client"

import { ThemeProvider as NextThemesProvider, type ThemeProviderProps } from "next-themes"
import { useEffect, useState } from "react"

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  // Verificar se estamos no lado do cliente
  const isClient = typeof window !== 'undefined'
  const [mounted, setMounted] = useState(isClient ? false : true)

  // Evitar problemas de hidratação renderizando apenas no cliente
  useEffect(() => {
    setMounted(true)
  }, [])

  // No servidor ou durante a hidratação, renderizar apenas os filhos
  if (!isClient || !mounted) {
    return <>{children}</>
  }

  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}
