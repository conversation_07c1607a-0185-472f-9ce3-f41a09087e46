/**
 * Interface para representar estabelecimentos como bares, discotecas, etc.
 */
export interface Estabelecimento {
  id: string;
  nome: string;
  tipo: 'bar' | 'discoteca' | 'restaurante' | 'clube' | 'café' | 'outro';
  endereco: string;
  coordenadas: {
    lat: number;
    lng: number;
  };
  // Campos opcionais que não são mais usados no formulário, mas mantidos para compatibilidade
  descricao?: string;
  horarioFuncionamento?: string;
  telefone?: string;
  website?: string;
  // Campos de metadados
  criadoPor?: string;
  criadoEm?: Date;
  atualizadoPor?: string;
  atualizadoEm?: Date;
}

/**
 * Tipos de estabelecimentos disponíveis
 */
export const tiposEstabelecimento = [
  { id: 'bar', nome: 'Bar' },
  { id: 'discoteca', nome: 'Discoteca' },
  { id: 'restaurante', nome: 'Restaurante' },
  { id: 'clube', nome: 'Clube' },
  { id: 'café', nome: 'Café' },
  { id: 'outro', nome: 'Outro' }
];
