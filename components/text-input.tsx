"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/contexts/auth-context";
import { showErrorToast, showSuccessToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";

interface TextInputProps {
  onSaveComplete: (textData: {
    title: string;
    content: string;
    createdBy: string;
  }) => void;
}

export function TextInput({ onSaveComplete }: TextInputProps) {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
  };

  const resetForm = () => {
    setTitle("");
    setContent("");
  };

  const handleSave = async () => {
    if (!title.trim()) {
      showErrorToast("Por favor, insira um título para o texto");
      return;
    }

    if (!content.trim()) {
      showErrorToast("Por favor, insira o conteúdo do texto");
      return;
    }

    if (!user) {
      showErrorToast("É necessário estar autenticado para guardar textos");
      return;
    }

    setIsSubmitting(true);
    const loadingToast = showLoadingToast("A guardar texto...");

    try {
      // Preparar os dados do texto
      const textData = {
        title: title,
        content: content,
        createdBy: user.uid,
      };

      // Notificar o componente pai sobre o texto guardado
      onSaveComplete(textData);

      dismissToast(loadingToast);
      showSuccessToast("Texto guardado com sucesso");

      resetForm();
    } catch (error: any) {
      dismissToast(loadingToast);
      showErrorToast("Erro ao guardar texto");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4 py-2">
      <div className="space-y-2">
        <Label htmlFor="title">Título</Label>
        <Input
          id="title"
          placeholder="Insira um título para o texto"
          value={title}
          onChange={handleTitleChange}
          disabled={isSubmitting}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="content">Conteúdo</Label>
        <Textarea
          id="content"
          placeholder="Insira o conteúdo do texto"
          value={content}
          onChange={handleContentChange}
          disabled={isSubmitting}
          className="min-h-[250px] resize-y"
        />
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-end gap-2 mt-6">
        <Button
          variant="outline"
          type="button"
          onClick={resetForm}
          disabled={isSubmitting || (!title.trim() && !content.trim())}
          className="sm:order-first"
        >
          Limpar Campos
        </Button>
        <Button
          onClick={handleSave}
          disabled={isSubmitting || !title.trim() || !content.trim()}
        >
          {isSubmitting ? "A guardar..." : "Guardar Texto"}
        </Button>
      </div>
    </div>
  );
}
