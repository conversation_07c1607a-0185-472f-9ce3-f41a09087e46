import { locationConfig } from "@/lib/config";
import { Estabelecimento } from "@/types/estabelecimento";
import { searchEstabelecimentos } from "./estabelecimentos-service";

export interface LocationResult {
  formatted: string;
  geometry: {
    lat: number;
    lng: number;
  };
  components: {
    road?: string;
    house_number?: string;
    city?: string;
    town?: string;
    village?: string;
    state?: string;
    country?: string;
    postcode?: string;
    [key: string]: string | undefined;
  };
  // Campo adicional para armazenar o endereço normalizado (sem número de porta/código postal)
  normalizedAddress?: string;
  // Campo para identificar se é um estabelecimento personalizado
  isCustomPOI?: boolean;
  // Tipo de estabelecimento (se for um estabelecimento personalizado)
  poiType?: string;
}

/**
 * Normaliza um endereço removendo números de porta e códigos postais
 * @param result Resultado da localização
 * @returns Endereço normalizado
 */
function normalizeAddress(result: LocationResult): string {
  const components = result.components;

  // Extrair os componentes relevantes para construir o endereço normalizado
  const road = components.road || '';
  const locality = components.city || components.town || components.village || '';
  const state = components.state || '';
  const country = components.country || '';

  // Construir o endereço sem número de porta e código postal
  let normalizedParts = [];

  if (road) normalizedParts.push(road);
  if (locality) normalizedParts.push(locality);
  if (state) normalizedParts.push(state);
  if (country) normalizedParts.push(country);

  return normalizedParts.join(', ');
}

/**
 * Processa os resultados da API para adicionar endereços normalizados
 * @param results Resultados da API
 * @returns Resultados processados
 */
function processResults(results: LocationResult[]): LocationResult[] {
  // Criar um Map para rastrear endereços únicos (sem número/código postal)
  const uniqueAddresses = new Map<string, LocationResult>();

  // Processar cada resultado
  results.forEach(result => {
    // Adicionar o endereço normalizado ao resultado
    result.normalizedAddress = normalizeAddress(result);

    // Se já temos um resultado com este endereço normalizado, não adicionar duplicado
    if (!uniqueAddresses.has(result.normalizedAddress)) {
      uniqueAddresses.set(result.normalizedAddress, result);
    }
  });

  // Converter o Map de volta para um array
  return Array.from(uniqueAddresses.values());
}

/**
 * Converte um estabelecimento para o formato LocationResult
 */
function convertEstabelecimentoToLocationResult(estabelecimento: Estabelecimento): LocationResult {
  return {
    formatted: `${estabelecimento.nome}, ${estabelecimento.endereco}`,
    geometry: estabelecimento.coordenadas,
    components: {
      // Extrair componentes do endereço se possível
      // Como não temos os componentes separados, usamos o endereço completo
      road: estabelecimento.endereco
    },
    normalizedAddress: `${estabelecimento.nome}, ${estabelecimento.endereco}`,
    isCustomPOI: true,
    poiType: estabelecimento.tipo
  };
}

/**
 * Busca localizações com base em um termo de pesquisa
 * @param query Termo de pesquisa para buscar localizações
 * @param options Opções adicionais para a busca
 * @returns Array de resultados de localização
 */
export async function searchLocations(
  query: string,
  options: {
    removeDuplicates?: boolean;
    normalizeAddresses?: boolean;
    includeCustomPOIs?: boolean;
    searchByHouseNumber?: boolean;
  } = {}
): Promise<LocationResult[]> {
  // Obter configurações padrão do arquivo de configuração
  const defaultNormalizeAddresses = locationConfig.addressNormalization?.normalizeAddresses ?? true;
  const defaultRemoveDuplicates = locationConfig.addressNormalization?.removeDuplicates ?? true;
  const defaultSearchByHouseNumber = locationConfig.addressNormalization?.searchByHouseNumber ?? true;

  // Mesclar opções fornecidas com as configurações padrão
  const {
    removeDuplicates = defaultRemoveDuplicates,
    normalizeAddresses = defaultNormalizeAddresses,
    includeCustomPOIs = true,
    searchByHouseNumber = defaultSearchByHouseNumber
  } = options;

  if (!query || query.length < 3) {
    return [];
  }

  try {
    // Buscar resultados da API OpenCage
    const opencageResults = await searchOpenCageLocations(query, {
      removeDuplicates,
      normalizeAddresses,
      searchByHouseNumber
    });

    // Se não quisermos incluir POIs personalizados, retornar apenas os resultados da API
    if (!includeCustomPOIs) {
      return opencageResults;
    }

    // Buscar estabelecimentos personalizados
    const estabelecimentos = await searchEstabelecimentos(query);
    const estabelecimentosResults = estabelecimentos.map(convertEstabelecimentoToLocationResult);

    // Combinar os resultados
    const combinedResults = [...opencageResults, ...estabelecimentosResults];

    // Se não houver resultados, retornar array vazio
    if (combinedResults.length === 0) {
      return [];
    }

    return combinedResults;
  } catch (error) {
    console.error("Erro ao buscar localizações:", error);
    return [];
  }
}

/**
 * Busca localizações usando a API OpenCage
 * @param query Termo de pesquisa
 * @param options Opções adicionais
 * @returns Array de resultados de localização
 */
async function searchOpenCageLocations(
  query: string,
  options: { removeDuplicates?: boolean; normalizeAddresses?: boolean; searchByHouseNumber?: boolean }
): Promise<LocationResult[]> {
  try {
    // Construir a URL usando as configurações do arquivo config
    const { apiUrl, apiKey, defaultParams } = locationConfig;

    // Verificar se a consulta contém um número de porta
    // Exemplo: "Rua da Trindade 46" ou "Avenida da Liberdade, 10"
    const hasHouseNumber = options.searchByHouseNumber && /\b\d+\b/.test(query);

    // Se a consulta contém um número de porta, adicionar parâmetros para melhorar a precisão
    const params = new URLSearchParams({
      q: query,
      key: apiKey,
      language: defaultParams.language,
      countrycode: defaultParams.countrycode,
      limit: defaultParams.limit.toString(),
      // Adicionar parâmetros adicionais para melhorar a precisão quando há número de porta
      ...(hasHouseNumber ? { addressdetails: '1' } : {})
    });

    console.log("Consulta contém número de porta:", hasHouseNumber);

    console.log("Buscando localizações para:", query);
    const response = await fetch(`${apiUrl}?${params}`);
    const data = await response.json();

    if (data.results && data.results.length > 0) {
      console.log(`Encontrados ${data.results.length} resultados para a consulta:`, query);
      let results = data.results;

      // Processar os resultados se necessário
      if (options.normalizeAddresses || options.removeDuplicates) {
        results = processResults(results);
        console.log(`Após processamento, ${results.length} resultados únicos`);
      }

      return results;
    } else {
      console.warn("Nenhum resultado encontrado para a consulta:", query);
      return [];
    }
  } catch (error) {
    console.error("Erro ao buscar localizações da API OpenCage:", error);
    return [];
  }
}

/**
 * Obtém as coordenadas de uma localização específica
 * @param address Endereço para obter as coordenadas
 * @returns Coordenadas da localização (latitude e longitude)
 */
export async function getCoordinates(address: string): Promise<{ lat: number; lng: number } | null> {
  try {
    console.log("Buscando coordenadas para endereço:", address);

    if (!address || address.trim().length < 3) {
      console.warn("Endereço muito curto ou vazio:", address);
      return null;
    }

    // Usar a opção para não normalizar endereços para obter resultados mais precisos
    const results = await searchLocations(address, {
      normalizeAddresses: false,
      includeCustomPOIs: true
    });

    if (results.length > 0) {
      console.log("Coordenadas encontradas:", results[0].geometry);
      return results[0].geometry;
    }

    // Se não encontrou resultados, tentar uma busca mais ampla
    // Remover partes do endereço que podem estar causando problemas
    const simplifiedAddress = address.split(',')[0].trim();
    if (simplifiedAddress !== address) {
      console.log("Tentando busca com endereço simplificado:", simplifiedAddress);
      const simplifiedResults = await searchLocations(simplifiedAddress, {
        normalizeAddresses: false,
        includeCustomPOIs: true
      });

      if (simplifiedResults.length > 0) {
        console.log("Coordenadas encontradas com endereço simplificado:", simplifiedResults[0].geometry);
        return simplifiedResults[0].geometry;
      }
    }

    console.warn("Nenhuma coordenada encontrada para o endereço:", address);
    return null;
  } catch (error) {
    console.error("Erro ao obter coordenadas:", error);
    return null;
  }
}

/**
 * Obtém o endereço normalizado (sem número de porta e código postal) de um resultado
 * @param result Resultado da localização
 * @returns Endereço normalizado
 */
export function getFormattedAddress(result: LocationResult): string {
  // Se temos um endereço normalizado, usá-lo
  if (result.normalizedAddress) {
    return result.normalizedAddress;
  }

  // Caso contrário, normalizar o endereço
  return normalizeAddress(result);
}

/**
 * Formata um endereço incluindo o número de porta, se disponível
 * @param result Resultado da localização
 * @returns Endereço formatado com número de porta
 */
export function getAddressWithHouseNumber(result: LocationResult): string {
  // Se não temos número de porta, retornar o endereço formatado normal
  if (!result.components?.house_number) {
    return result.formatted;
  }

  // Se temos um endereço normalizado, adicionar o número de porta
  if (result.normalizedAddress) {
    const road = result.components.road || "";
    const houseNumber = result.components.house_number;

    // Se o endereço normalizado já contém a rua, substituir a rua pela rua com número
    if (road && result.normalizedAddress.includes(road)) {
      return result.normalizedAddress.replace(road, `${road} ${houseNumber}`);
    }

    // Caso contrário, adicionar o número de porta ao início
    return `${road} ${houseNumber}, ${result.normalizedAddress}`;
  }

  // Se não temos endereço normalizado, retornar o endereço formatado normal
  return result.formatted;
}
