"use client";

import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import { categories } from "@/lib/categories";
import { UserRole } from "@/types/team";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Team } from "@/types/team";

// Definir o esquema de validação
const registerSchema = z.object({
  // Dados de autenticação
  email: z.string().min(1, "O email é obrigatório").email("Email inválido"),
  password: z.string().min(6, "A password deve ter pelo menos 6 caracteres"),

  // Dados de perfil
  fullName: z.string().min(3, "O nome completo deve ter pelo menos 3 caracteres"),
  registrationNumber: z.string().min(1, "O número de matrícula é obrigatório"),
  category: z.string().min(1, "A categoria é obrigatória"),

  // Dados de acesso
  role: z.enum([UserRole.ADMIN, UserRole.TEAM_LEADER, UserRole.TEAM_MEMBER], {
    required_error: "O papel é obrigatório",
    invalid_type_error: "Selecione um papel válido",
  }),
  teamId: z.string().optional().refine(
    (val) => {
      // A validação real é feita no formulário
      return true;
    },
    {
      message: "A equipa é obrigatória para líderes e membros de equipa",
    }
  ),
  canEditProfile: z.boolean().default(true).optional(),
});

type RegisterFormValues = z.infer<typeof registerSchema>;

interface UserRegisterFormProps {
  teams: Team[];
  onRegister: (data: RegisterFormValues) => Promise<void>;
  onCancel: () => void;
}

export function UserRegisterForm({ teams, onRegister, onCancel }: UserRegisterFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("auth");
  const [showTeamSelect, setShowTeamSelect] = useState(false);

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: "",
      password: "",
      fullName: "",
      registrationNumber: "",
      category: "",
      role: undefined,
      teamId: "",
      canEditProfile: true,
    },
    mode: "onChange", // Validar ao alterar os campos
  });

  // Observar mudanças no campo de papel para mostrar/esconder o seletor de equipa
  const selectedRole = form.watch("role");

  // Atualizar visibilidade do seletor de equipa com base no papel selecionado
  useEffect(() => {
    setShowTeamSelect(selectedRole === UserRole.TEAM_LEADER || selectedRole === UserRole.TEAM_MEMBER);

    // Se o papel não requer equipa, limpar a seleção de equipa
    if (selectedRole === UserRole.ADMIN) {
      form.setValue("teamId", "");
    }
  }, [selectedRole, form]);

  async function onSubmit(data: RegisterFormValues) {
    console.log("Submetendo formulário de registo:", data);
    setIsSubmitting(true);
    const loadingToast = showLoadingToast("A registar utilizador...");

    try {
      try {
        await onRegister(data);

        dismissToast(loadingToast);
        showSuccessToast("Utilizador registado com sucesso");

        // Limpar o formulário
        form.reset();
      } catch (registerError: any) {
        console.error("Erro ao registar utilizador:", registerError);
        dismissToast(loadingToast);

        // Tratar erros específicos
        if (registerError.message?.includes("email já está em uso")) {
          showErrorToast("Este email já está registado no sistema");
        } else if (registerError.message?.includes("password é muito fraca")) {
          showErrorToast("A password fornecida é muito fraca");
        } else if (registerError.message?.includes("Permissão negada")) {
          showErrorToast("Não tem permissão para registar novos utilizadores");
        } else {
          showErrorToast(registerError.message || "Não foi possível registar o utilizador");
        }
      }
    } catch (error: any) {
      console.error("Erro no processamento do formulário:", error);
      dismissToast(loadingToast);
      showErrorToast("Ocorreu um erro ao processar o formulário");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="auth">Autenticação</TabsTrigger>
            <TabsTrigger value="profile">Perfil</TabsTrigger>
            <TabsTrigger value="access">Acesso</TabsTrigger>
          </TabsList>

          {/* Separador de Autenticação */}
          <TabsContent value="auth" className="space-y-4 mt-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>
                    O email será usado para login e para enviar o convite.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">Password Inicial</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="******" {...field} />
                  </FormControl>
                  <FormDescription>
                    Uma password temporária que o utilizador poderá alterar após o primeiro login.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-between mt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancelar
              </Button>
              <Button
                type="button"
                onClick={() => {
                  // Validar os campos da aba atual antes de avançar
                  const emailValid = form.trigger("email");
                  const passwordValid = form.trigger("password");

                  Promise.all([emailValid, passwordValid]).then(results => {
                    if (results.every(Boolean)) {
                      setActiveTab("profile");
                    }
                  });
                }}
              >
                Próximo
              </Button>
            </div>
          </TabsContent>

          {/* Separador de Perfil */}
          <TabsContent value="profile" className="space-y-4 mt-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">Nome Completo</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome completo" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="registrationNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">Número de Matrícula</FormLabel>
                  <FormControl>
                    <Input placeholder="Número de matrícula" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">Categoria</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione uma categoria" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(categories).map(([group, items]) => (
                        <SelectGroup key={group}>
                          <SelectLabel>{group}</SelectLabel>
                          {items.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-between mt-4">
              <Button type="button" variant="outline" onClick={() => setActiveTab("auth")}>
                Anterior
              </Button>
              <Button
                type="button"
                onClick={() => {
                  // Validar os campos da aba atual antes de avançar
                  const fullNameValid = form.trigger("fullName");
                  const registrationNumberValid = form.trigger("registrationNumber");
                  const categoryValid = form.trigger("category");

                  Promise.all([fullNameValid, registrationNumberValid, categoryValid]).then(results => {
                    if (results.every(Boolean)) {
                      setActiveTab("access");
                    }
                  });
                }}
              >
                Próximo
              </Button>
            </div>
          </TabsContent>

          {/* Separador de Acesso */}
          <TabsContent value="access" className="space-y-4 mt-4">
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">Papel</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setShowTeamSelect(value === UserRole.TEAM_LEADER || value === UserRole.TEAM_MEMBER);
                      if (value === UserRole.ADMIN) {
                        form.setValue("teamId", "");
                      }
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um papel" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={UserRole.ADMIN}>Administrador</SelectItem>
                      <SelectItem value={UserRole.TEAM_LEADER}>Líder de Equipa</SelectItem>
                      <SelectItem value={UserRole.TEAM_MEMBER}>Membro de Equipa</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    O papel define as permissões do utilizador no sistema.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {showTeamSelect && (
              <FormField
                control={form.control}
                name="teamId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="after:content-['*'] after:ml-0.5 after:text-red-500">Equipa</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma equipa" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {teams.map((team) => (
                          <SelectItem key={team.id} value={team.id}>
                            {team.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      A equipa à qual o utilizador pertence.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="canEditProfile"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Permitir edição de perfil</FormLabel>
                    <FormDescription>
                      Se ativado, o utilizador poderá editar o seu próprio perfil.
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <div className="flex justify-between mt-4">
              <Button type="button" variant="outline" onClick={() => setActiveTab("profile")}>
                Anterior
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "A registar..." : "Registar Utilizador"}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </form>
    </Form>
  );
}
