import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth, getAdminFirestore } from '@/lib/firebase-admin';
import admin from '@/lib/firebase-admin';

export async function POST(request: NextRequest) {
  try {
    console.log("API de exclusão de utilizador chamada");

    // Verificar se o utilizador está autenticado e é admin
    // Nota: Em produção, deve-se implementar uma verificação de autenticação adequada
    // Esta é uma implementação simplificada

    const body = await request.json();
    console.log("Dados recebidos:", body);
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'ID do utilizador é obrigatório' },
        { status: 400 }
      );
    }

    // Obter instâncias do Firebase Admin
    const adminAuth = getAdminAuth();
    const adminFirestore = getAdminFirestore();

    // Verificar se o utilizador existe no Firebase Authentication
    let userExists = true;
    let userAuthData;
    try {
      userAuthData = await adminAuth.getUser(userId);
      console.log("Utilizador encontrado no Firebase Authentication:", userAuthData.uid);
    } catch (error: unknown) {
      const firebaseError = error as { code?: string; message: string; };
      userExists = false;
      console.log("Utilizador não encontrado no Firebase Authentication:", firebaseError.message);

      // Se o erro não for 'user-not-found', é um erro mais grave
      if (firebaseError.code !== 'auth/user-not-found') {
        return NextResponse.json(
          { error: 'Erro ao verificar utilizador', details: firebaseError.message },
          { status: 500 }
        );
      }
    }

    // Verificar se o utilizador existe no Firestore
    const userDoc = await adminFirestore.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      console.log("Dados do utilizador não encontrados no Firestore");

      // Se o utilizador existe no Auth mas não no Firestore, ainda podemos excluí-lo do Auth
      if (!userExists) {
        return NextResponse.json(
          { error: 'Utilizador não encontrado em nenhum sistema' },
          { status: 404 }
        );
      }
    } else {
      const userData = userDoc.data();
      console.log("Dados do utilizador a ser excluído:", userData);
    }

    // Verificar se o utilizador pertence a alguma equipa e removê-lo
    try {
      if (userDoc.exists && userDoc.data()?.teamId) {
        const teamId = userDoc.data()?.teamId;
        const userRole = userDoc.data()?.role;

        console.log(`Verificando equipa ${teamId} do utilizador ${userId} com papel ${userRole}`);

        const teamRef = adminFirestore.collection('teams').doc(teamId);
        const teamDoc = await teamRef.get();

        if (teamDoc.exists) {
          // Remover o utilizador da equipa com base no seu papel
          if (userRole === 'team_leader') {
            console.log(`Removendo utilizador ${userId} como líder da equipa ${teamId}`);
            await teamRef.update({
              leaders: admin.firestore.FieldValue.arrayRemove(userId),
              updatedAt: new Date()
            });
          } else if (userRole === 'team_member') {
            console.log(`Removendo utilizador ${userId} como membro da equipa ${teamId}`);
            await teamRef.update({
              members: admin.firestore.FieldValue.arrayRemove(userId),
              updatedAt: new Date()
            });
          }
          console.log(`Utilizador removido da equipa ${teamId} com sucesso`);
        } else {
          console.log(`Equipa ${teamId} não encontrada, nenhuma atualização necessária`);
        }
      }
    } catch (teamError: unknown) {
      // Apenas registrar o erro, mas continuar com a exclusão do utilizador
      console.error('Erro ao atualizar equipa:', teamError);
    }

    // Excluir o utilizador do Firebase Authentication (se existir)
    if (userExists) {
      try {
        console.log("Excluindo utilizador do Firebase Authentication...");
        await adminAuth.deleteUser(userId);
        console.log("Utilizador excluído do Firebase Authentication com sucesso");
      } catch (authError: unknown) {
        const firebaseAuthError = authError as { code?: string; message: string; };
        console.error('Erro ao excluir utilizador do Firebase Authentication:', firebaseAuthError);

        // Se o erro não for 'user-not-found', é um erro mais grave
        if (firebaseAuthError.code !== 'auth/user-not-found') {
          return NextResponse.json(
            { error: 'Erro ao excluir utilizador do Firebase Authentication', details: firebaseAuthError.message },
            { status: 500 }
          );
        }
      }
    }

    // Excluir os dados do utilizador do Firestore (se existir)
    if (userDoc.exists) {
      try {
        console.log("Excluindo dados do utilizador do Firestore...");
        await adminFirestore.collection('users').doc(userId).delete();
        console.log("Dados do utilizador excluídos do Firestore com sucesso");
      } catch (firestoreError: unknown) {
        const firebaseFirestoreError = firestoreError as { message: string; };
        console.error('Erro ao excluir dados do utilizador do Firestore:', firebaseFirestoreError);
        return NextResponse.json(
          { error: 'Erro ao excluir dados do utilizador do Firestore', details: firebaseFirestoreError.message },
          { status: 500 }
        );
      }
    }

    // Importante: Não excluímos os registos criados pelo utilizador
    // Os registos permanecem no sistema com as referências ao utilizador que os criou

    return NextResponse.json({
      success: true,
      message: 'Utilizador excluído com sucesso',
      userId: userId,
      authDeleted: userExists,
      firestoreDeleted: userDoc.exists
    });
  } catch (error: unknown) {
    const firebaseError = error as { code?: string; message: string; stack?: string; };
    console.error('Erro ao excluir utilizador:', firebaseError);
    console.error('Stack trace:', firebaseError.stack);

    // Tratar erros específicos do Firebase
    if (firebaseError.code === 'auth/user-not-found') {
      return NextResponse.json(
        { error: 'Utilizador não encontrado' },
        { status: 404 }
      );
    }

    if (firebaseError.code === 'auth/invalid-uid') {
      return NextResponse.json(
        { error: 'ID de utilizador inválido' },
        { status: 400 }
      );
    }

    if (firebaseError.code === 'auth/insufficient-permission') {
      return NextResponse.json(
        { error: 'Permissão insuficiente para excluir o utilizador' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: 'Erro ao excluir utilizador', details: firebaseError.message, code: firebaseError.code },
      { status: 500 }
    );
  }
}
