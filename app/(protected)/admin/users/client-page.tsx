"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { SearchBar } from "@/components/search-bar";
import { UsersTable } from "@/components/users/users-table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { UserRoleForm } from "@/components/users/user-role-form";
import { UserProfileForm } from "@/components/users/user-profile-form";
import { UserRegisterForm } from "@/components/users/user-register-form";
import { getSystemUsers } from "@/services/users-service";
import { getTeams } from "@/services/teams-service";
import { updateUserRole, updateUserProfile, registerUser, deleteUser, sendPasswordResetEmail, UserRegisterData } from "@/services/users-service";
import { UserR<PERSON>, Team } from "@/types/team";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import { IconPlus, IconTrash, IconAlertTriangle } from "@tabler/icons-react";

interface User {
  id: string;
  name: string;
  email?: string;
  registrationNumber?: string;
  category?: string;
  role?: UserRole;
  teamId?: string;
  canEditProfile?: boolean;
}

export function ClientPage() {
  const { user, loading, profileLoading, isAdmin } = useAuth();
  const router = useRouter();

  // Estados
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [isEditRoleModalOpen, setIsEditRoleModalOpen] = useState(false);
  const [isEditProfileModalOpen, setIsEditProfileModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [isResetLinkModalOpen, setIsResetLinkModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [resetLink, setResetLink] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Efeito para verificar se o usuário é admin
  useEffect(() => {
    if (!loading && !profileLoading && !isAdmin) {
      showErrorToast("Acesso negado");
      router.push("/dashboard");
    }
  }, [loading, profileLoading, isAdmin, router]);

  // Efeito para carregar usuários
  useEffect(() => {
    const loadUsers = async () => {
      try {
        setIsLoading(true);
        const usersData = await getSystemUsers();
        setUsers(usersData);
        setFilteredUsers(usersData);
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
        showErrorToast("Erro ao carregar utilizadores");
      } finally {
        setIsLoading(false);
      }
    };

    if (user && isAdmin) {
      loadUsers();
    }
  }, [user, isAdmin]);

  // Efeito para carregar equipas
  useEffect(() => {
    const loadTeams = async () => {
      try {
        const teamsData = await getTeams();
        setTeams(teamsData);
      } catch (error) {
        console.error("Erro ao carregar equipas:", error);
        showErrorToast("Erro ao carregar equipas");
      }
    };

    if (user && isAdmin) {
      loadTeams();
    }
  }, [user, isAdmin]);

  // Função para pesquisar usuários (memoizada para evitar loops infinitos)
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);

    if (!term.trim()) {
      setFilteredUsers(users);
      return;
    }

    const lowerCaseSearchTerm = term.toLowerCase();
    const filtered = users.filter(user =>
      user.name.toLowerCase().includes(lowerCaseSearchTerm) ||
      (user.email && user.email.toLowerCase().includes(lowerCaseSearchTerm)) ||
      (user.registrationNumber && user.registrationNumber.toLowerCase().includes(lowerCaseSearchTerm)) ||
      (user.category && user.category.toLowerCase().includes(lowerCaseSearchTerm))
    );

    setFilteredUsers(filtered);
  }, [users]);

  // Função para abrir o modal de edição de papel
  const handleEditRole = (user: User) => {
    console.log("ClientPage - Abrindo modal de edição de papel para:", {
      userId: user.id,
      name: user.name,
      role: user.role || "(sem papel)",
      teamId: user.teamId || "(sem equipa)"
    });

    // Fechar o modal primeiro para garantir que o componente seja recriado
    setIsEditRoleModalOpen(false);

    // Usar setTimeout para garantir que o modal seja fechado antes de abrir novamente
    setTimeout(() => {
      setSelectedUser(user);
      setIsEditRoleModalOpen(true);
    }, 50);
  };

  // Função para abrir o modal de edição de perfil
  const handleEditProfile = (user: User) => {
    setSelectedUser(user);
    setIsEditProfileModalOpen(true);
  };

  // Função para enviar email de redefinição de password
  const handleSendPasswordResetEmail = async (email: string) => {
    try {
      const loadingToast = showLoadingToast("A enviar email...");

      try {
        // Enviar o email de redefinição de password usando o Firebase Client SDK
        await sendPasswordResetEmail(email);
        dismissToast(loadingToast);
        showSuccessToast("Email enviado com sucesso", {
          description: "O utilizador receberá um email com instruções para redefinir a password."
        });

        // Se estamos no modal de redefinição de senha após registo, fechar o modal
        if (isResetLinkModalOpen) {
          setIsResetLinkModalOpen(false);
        }

        // Como estamos usando o Firebase Client SDK, não temos acesso ao link gerado
        // Portanto, não podemos mais mostrar o modal com o link
      } catch (error: unknown) {
        dismissToast(loadingToast);
        console.error("Erro ao enviar email de redefinição de password:", error);
        showErrorToast("Erro ao enviar email de redefinição de password", {
          description: error instanceof Error ? error.message : "Ocorreu um erro ao processar o pedido."
        });
      }
    } catch (error: unknown) {
      console.error("Erro inesperado ao enviar email de redefinição de password:", error);
      showErrorToast("Erro inesperado", {
        description: "Ocorreu um erro inesperado ao processar o pedido."
      });
    }
  };

  // Função para iniciar o processo de exclusão
  const handleInitiateDelete = (userToDelete: User) => {
    console.log("Tentando excluir utilizador:", userToDelete.name, "(ID:", userToDelete.id, ")");
    console.log("Utilizador logado:", user?.uid);

    // Não permitir excluir o próprio usuário
    if (userToDelete && user && userToDelete.id === user.uid) {
      console.log("Tentativa de excluir o próprio utilizador bloqueada");
      showErrorToast("Não é possível excluir o seu próprio utilizador");
      return;
    }

    console.log("Iniciando exclusão do utilizador:", userToDelete.name, "(ID:", userToDelete.id, ")");

    setSelectedUser(userToDelete);
    setIsDeleteModalOpen(true);
  };

  // Função para avançar para a segunda etapa de confirmação
  const handleContinueDelete = () => {
    setIsDeleteModalOpen(false);
    // Limpar o texto de confirmação antes de abrir o segundo modal
    setDeleteConfirmText("");
    setTimeout(() => {
      setIsDeleteConfirmModalOpen(true);
    }, 100);
  };

  // Função para executar a exclusão após a dupla confirmação
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    // Verificar se o texto de confirmação corresponde ao email do utilizador
    if (deleteConfirmText !== selectedUser.email) {
      showErrorToast("O email digitado não corresponde ao email do utilizador");
      return;
    }

    try {
      const loadingToast = showLoadingToast("A excluir utilizador...");

      await deleteUser(selectedUser.id);

      dismissToast(loadingToast);
      showSuccessToast("Utilizador excluído com sucesso");

      // Fechar o modal de confirmação
      setIsDeleteConfirmModalOpen(false);
      setSelectedUser(null);

      // Recarregar a lista de utilizadores
      const updatedUsers = await getSystemUsers();
      setUsers(updatedUsers);
      setFilteredUsers(!searchTerm.trim() ? updatedUsers : updatedUsers.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.registrationNumber && user.registrationNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.category && user.category.toLowerCase().includes(searchTerm.toLowerCase()))
      ));
    } catch (error: unknown) {
      console.error("Erro ao excluir utilizador:", error);
      showErrorToast("Erro ao excluir utilizador");
    }
  };

  // Função para atualizar o perfil do usuário
  const handleUpdateProfile = async (userId: string, profileData: {
    fullName: string;
    registrationNumber: string;
    category: string;
    email?: string;
    canEditProfile?: boolean;
  }) => {
    // Verificar se o utilizador está tentando bloquear a edição do próprio perfil
    if (user && userId === user.uid && profileData.canEditProfile === false) {
      // Nota: user.uid é correto aqui porque estamos usando o objeto User do Firebase
      showErrorToast("Não é possível bloquear a edição do seu próprio perfil");
      return;
    }
    try {
      await updateUserProfile(userId, profileData);

      // Recarregar todos os usuários para garantir que temos os dados mais atualizados
      const updatedUsers = await getSystemUsers();
      setUsers(updatedUsers);

      // Atualizar a lista filtrada
      if (!searchTerm.trim()) {
        setFilteredUsers(updatedUsers);
      } else {
        const lowerCaseSearchTerm = searchTerm.toLowerCase();
        const filtered = updatedUsers.filter(user =>
          user.name.toLowerCase().includes(lowerCaseSearchTerm) ||
          (user.email && user.email.toLowerCase().includes(lowerCaseSearchTerm)) ||
          (user.registrationNumber && user.registrationNumber.toLowerCase().includes(lowerCaseSearchTerm)) ||
          (user.category && user.category.toLowerCase().includes(lowerCaseSearchTerm))
        );
        setFilteredUsers(filtered);
      }

      showSuccessToast("Perfil do utilizador atualizado com sucesso");
      setIsEditProfileModalOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error("Erro ao atualizar perfil do usuário:", error);
      showErrorToast("Erro ao atualizar perfil do utilizador");
    }
  };

  // Função para registar um novo utilizador
  const handleRegisterUser = async (userData: UserRegisterData): Promise<void> => {
    try {
      console.log("Iniciando registo de utilizador:", userData);

      // Verificar se os dados estão completos
      if (!userData.email || !userData.password || !userData.fullName || !userData.registrationNumber || !userData.category) {
        throw new Error("Todos os campos obrigatórios devem ser preenchidos");
      }

      // Verificar se o papel requer equipa
      if ((userData.role === UserRole.TEAM_LEADER || userData.role === UserRole.TEAM_MEMBER) && !userData.teamId) {
        throw new Error("Deve selecionar uma equipa para líderes e membros de equipa");
      }

      const result = await registerUser(userData);
      console.log("Utilizador registado com sucesso:", result);

      // Recarregar a lista de utilizadores
      const updatedUsers = await getSystemUsers();
      setUsers(updatedUsers);
      setFilteredUsers(!searchTerm.trim() ? updatedUsers : updatedUsers.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.registrationNumber && user.registrationNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.category && user.category.toLowerCase().includes(searchTerm.toLowerCase()))
      ));

      // Fechar o modal de registo
      setIsRegisterModalOpen(false);

      // Mostrar o link de redefinição de senha
      setResetLink(result.resetLink);
      setIsResetLinkModalOpen(true);

      // Não retornamos o resultado para corresponder ao tipo Promise<void>
    } catch (error: unknown) {
      console.error("Erro ao registar utilizador:", error);

      // Em produção, não mostrar o stack trace
      if (process.env.NODE_ENV === 'development' && error instanceof Error) {
        console.error("Stack trace:", error.stack);
      }

      // Tratar erros específicos
      let errorMessage = "Não foi possível registar o utilizador. Verifique os dados e tente novamente.";

      if (error instanceof Error) {
        if (error.message.includes("email já está em uso")) {
          errorMessage = "Este email já está registado no sistema. Tente outro email.";
        } else if (error.message.includes("password é muito fraca")) {
          errorMessage = "A password fornecida é muito fraca. Escolha uma password mais segura.";
        } else if (error.message.includes("Permissão negada")) {
          errorMessage = "Você não tem permissão para registar novos utilizadores.";
        } else {
          errorMessage = error.message;
        }
      }

      showErrorToast(errorMessage);

      throw error;
    }
  };

  // Função para atualizar o papel do usuário
  const handleUpdateRole = async (userId: string, role: UserRole, teamId?: string) => {
    // Verificar se o utilizador está tentando alterar seu próprio papel
    if (user && userId === user.uid && role !== UserRole.ADMIN) {
      // Nota: user.uid é correto aqui porque estamos usando o objeto User do Firebase
      // Verificar se existem outros administradores no sistema
      const adminCount = users.filter(u => u.role === UserRole.ADMIN && u.id !== userId).length;

      if (adminCount === 0) {
        showErrorToast("Não é possível rebaixar o seu próprio papel quando é o único administrador");
        return;
      }
    }
    try {
      await updateUserRole(userId, role, teamId);

      // Recarregar todos os usuários para garantir que temos os dados mais atualizados
      const updatedUsers = await getSystemUsers();
      setUsers(updatedUsers);

      // Atualizar a lista filtrada
      if (!searchTerm.trim()) {
        setFilteredUsers(updatedUsers);
      } else {
        const lowerCaseSearchTerm = searchTerm.toLowerCase();
        const filtered = updatedUsers.filter(user =>
          user.name.toLowerCase().includes(lowerCaseSearchTerm) ||
          (user.email && user.email.toLowerCase().includes(lowerCaseSearchTerm)) ||
          (user.registrationNumber && user.registrationNumber.toLowerCase().includes(lowerCaseSearchTerm)) ||
          (user.category && user.category.toLowerCase().includes(lowerCaseSearchTerm))
        );
        setFilteredUsers(filtered);
      }

      showSuccessToast("Papel do utilizador atualizado com sucesso");
      setIsEditRoleModalOpen(false);
      setSelectedUser(null);

      // Recarregar as equipas também para garantir que estão atualizadas
      const updatedTeams = await getTeams();
      setTeams(updatedTeams);
    } catch (error) {
      console.error("Erro ao atualizar papel do utilizador:", error);
      showErrorToast("Erro ao atualizar papel do utilizador");
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Utilizadores</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie os utilizadores do sistema
              </p>
            </div>
            <Button onClick={() => setIsRegisterModalOpen(true)} className="shrink-0">
              <IconPlus className="size-4 mr-2" />
              Adicionar Utilizador
            </Button>
          </div>

          {/* Barra de pesquisa */}
          <div className="mb-6">
            <SearchBar
              onSearch={handleSearch}
              placeholder="Pesquisar por nome, email, matrícula ou categoria..."
              className="max-w-full sm:max-w-md"
            />
          </div>

          {/* Seção de listagem */}
          <div className="mt-2">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              </div>
            ) : (
              <UsersTable
                users={filteredUsers}
                onEditRole={handleEditRole}
                onEditProfile={handleEditProfile}
                onDelete={handleInitiateDelete}
                onSendPasswordReset={handleSendPasswordResetEmail}
                teams={teams}
              />
            )}
          </div>

          {/* Modal de Edição de Papel */}
          <Dialog
            open={isEditRoleModalOpen}
            onOpenChange={(open) => {
              setIsEditRoleModalOpen(open);
              if (!open) {
                // Limpar o usuário selecionado quando o modal é fechado
                setTimeout(() => setSelectedUser(null), 100);
              }
            }}
          >
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Editar Papel do Utilizador</DialogTitle>
              </DialogHeader>
              {selectedUser && (
                <>
                  {user && selectedUser.id === user.uid && ( // user.uid é correto aqui
                    <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                      <p className="text-sm text-yellow-800 dark:text-yellow-400">
                        <strong>Atenção:</strong> Está a alterar o seu próprio papel. Se é o único administrador, não será possível rebaixar o seu papel.
                      </p>
                    </div>
                  )}
                  <UserRoleForm
                    user={selectedUser}
                    teams={teams}
                    onSave={handleUpdateRole}
                    isSelfEdit={user ? selectedUser.id === user.uid : false} // user.uid é correto aqui
                    key={selectedUser.id} // Adicionar key para forçar recriação do componente
                  />
                </>
              )}
            </DialogContent>
          </Dialog>

          {/* Modal de Edição de Perfil */}
          <Dialog open={isEditProfileModalOpen} onOpenChange={setIsEditProfileModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Editar Perfil do Utilizador</DialogTitle>
              </DialogHeader>
              {selectedUser && (
                <>
                  {user && selectedUser.id === user.uid && ( // user.uid é correto aqui
                    <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                      <p className="text-sm text-yellow-800 dark:text-yellow-400">
                        <strong>Atenção:</strong> Está a editar o seu próprio perfil. Algumas opções podem estar limitadas por razões de segurança.
                      </p>
                    </div>
                  )}
                  <UserProfileForm
                    userId={selectedUser.id}
                    initialData={{
                      fullName: selectedUser.name,
                      registrationNumber: selectedUser.registrationNumber || "",
                      category: selectedUser.category || "",
                      email: selectedUser.email || "",
                      canEditProfile: selectedUser.canEditProfile !== false, // Usar o valor atual do banco de dados
                    }}
                    onSave={handleUpdateProfile}
                    onCancel={() => setIsEditProfileModalOpen(false)}
                    isSelfEdit={user ? selectedUser.id === user.uid : false} // user.uid é correto aqui
                  />
                </>
              )}
            </DialogContent>
          </Dialog>

          {/* Modal de Registo de Utilizador */}
          <Dialog open={isRegisterModalOpen} onOpenChange={setIsRegisterModalOpen}>
            <DialogContent className="sm:max-w-xl">
              <DialogHeader>
                <DialogTitle>Adicionar Novo Utilizador</DialogTitle>
                <DialogDescription>
                  Preencha os dados para registar um novo utilizador no sistema.
                </DialogDescription>
              </DialogHeader>
              <UserRegisterForm
                teams={teams}
                onRegister={handleRegisterUser}
                onCancel={() => setIsRegisterModalOpen(false)}
              />
            </DialogContent>
          </Dialog>

          {/* Modal de Link de Redefinição de Senha - Apenas para novos registros */}
          <Dialog open={isResetLinkModalOpen} onOpenChange={setIsResetLinkModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>
                  Utilizador Registado com Sucesso
                </DialogTitle>
                <DialogDescription>
                  O utilizador foi registado com sucesso. Um email com instruções para definir a password foi enviado.
                </DialogDescription>
              </DialogHeader>
              {resetLink && (
                <>
                  <div className="my-4">
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                      <p className="text-sm text-blue-800 dark:text-blue-400 break-all">
                        {resetLink}
                      </p>
                    </div>
                  </div>
                  <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md mb-4">
                    <p className="text-sm text-yellow-800 dark:text-yellow-400">
                      <strong>Nota:</strong> Este link foi gerado durante o registo do utilizador.
                      Para fins de demonstração, você pode copiar o link e enviá-lo manualmente.
                    </p>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => {
                        navigator.clipboard.writeText(resetLink);
                        showSuccessToast("Link copiado para a área de transferência!");
                      }}
                    >
                      Copiar Link
                    </Button>
                  </DialogFooter>
                </>
              )}
              {!resetLink && (
                <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md my-4">
                  <p className="text-sm text-green-800 dark:text-green-400">
                    <strong>Sucesso:</strong> Um email com instruções para definir a password foi enviado para o utilizador.
                  </p>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Primeiro Modal de Exclusão - Aviso Inicial */}
          <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Excluir Utilizador</DialogTitle>
                <DialogDescription>
                  Tem certeza que deseja excluir o utilizador <strong>{selectedUser?.name}</strong>?
                </DialogDescription>
              </DialogHeader>
              <div className="p-4 my-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                <p className="text-sm text-yellow-800 dark:text-yellow-400">
                  <strong>Atenção:</strong> A exclusão de um utilizador removerá permanentemente a conta e os dados de perfil associados.
                </p>
              </div>
              <div className="p-4 my-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                <p className="text-sm text-green-800 dark:text-green-400">
                  <strong>Nota:</strong> Os registos efetuados por este utilizador não serão afetados e permanecerão no sistema.
                </p>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
                  Cancelar
                </Button>
                <Button variant="destructive" onClick={handleContinueDelete}>
                  <IconTrash className="mr-2 size-4" />
                  Continuar com a Exclusão
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Segundo Modal de Exclusão - Confirmação Final */}
          <Dialog open={isDeleteConfirmModalOpen} onOpenChange={setIsDeleteConfirmModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Confirmação Final de Exclusão</DialogTitle>
                <DialogDescription>
                  Para confirmar a exclusão, digite o email exato do utilizador: <strong>{selectedUser?.email}</strong>
                </DialogDescription>
              </DialogHeader>
              <div className="p-4 my-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <div className="flex items-start gap-2">
                  <IconAlertTriangle className="size-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-800 dark:text-red-400">
                    <strong>Aviso:</strong> Esta ação é irreversível! A conta do utilizador será permanentemente excluída do sistema.
                  </p>
                </div>
              </div>
              <div className="my-4">
                <label htmlFor="confirm-text" className="text-sm font-medium">
                  Email do utilizador:
                </label>
                <input
                  id="confirm-text"
                  type="text"
                  value={deleteConfirmText}
                  onChange={(e) => setDeleteConfirmText(e.target.value)}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder={`Digite "${selectedUser?.email}" para confirmar`}
                />
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteConfirmModalOpen(false)}>
                  Cancelar
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteUser}
                  disabled={deleteConfirmText !== selectedUser?.email}
                >
                  <IconTrash className="mr-2 size-4" />
                  Excluir Permanentemente
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
