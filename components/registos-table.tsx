"use client";

import { useState } from "react";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  IconEdit,
  IconTrash,
  IconFileText,
  IconUserShield,
} from "@tabler/icons-react";
import { Badge } from "@/components/ui/badge";
import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";
import { Registo } from "@/services/registos-service";

interface RegistosTableProps {
  registos: Registo[];
  onDelete: (id: string) => Promise<void>;
  onEdit: (registo: Registo) => void;
}

export function RegistosTable({ registos, onDelete, onEdit }: RegistosTableProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedRegisto, setSelectedRegisto] = useState<Registo | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Função para confirmar exclusão
  const confirmDelete = (registo: Registo) => {
    setSelectedRegisto(registo);
    setIsDeleteDialogOpen(true);
  };

  // Função para executar a exclusão
  const handleDelete = async () => {
    if (!selectedRegisto) return;

    setIsDeleting(true);
    try {
      await onDelete(selectedRegisto.id);
      showSuccessToast("Registo eliminado com sucesso");
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      showErrorToast("Erro ao eliminar registo");
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // Função para obter o tipo de registo formatado
  const getTipoRegistoDisplay = (tipoRegisto: string) => {
    switch (tipoRegisto) {
      case "detencao":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800">
            <IconUserShield className="size-3.5 mr-1" />
            Detenção
          </Badge>
        );
      case "autoNoticia":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
            <IconFileText className="size-3.5 mr-1" />
            Auto de Notícia
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            Desconhecido
          </Badge>
        );
    }
  };

  return (
    <>
      {/* Cards para todos os tamanhos de tela */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {registos.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            Nenhum registo encontrado.
          </div>
        ) : (
          registos.map((registo) => (
            <Card key={registo.id} className="@container/card dark:*:data-[slot=card]:bg-card *:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs flex flex-col">
              <CardHeader className="px-6 pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex flex-col gap-1.5">
                    <CardTitle className="leading-none font-semibold">
                      {registo.nppNuipc}
                    </CardTitle>
                    <CardDescription className="text-sm">
                      {registo.dataRegisto} {registo.horaRegisto}
                    </CardDescription>
                  </div>
                  <div>
                    {getTipoRegistoDisplay(registo.tipoRegisto)}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="px-6 pt-0 flex-1">
                <div className="grid grid-cols-1 gap-3">
                  <div className="flex flex-col min-w-0">
                    <span className="text-xs text-muted-foreground">Responsável</span>
                    <div className="flex items-center gap-1">
                      <span className="text-sm font-medium truncate max-w-full">{registo.responsavelNome}</span>
                      {registo.responsavelMatricula && (
                        <span className="text-xs text-muted-foreground whitespace-nowrap">[{registo.responsavelMatricula}]</span>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col w-full">
                    <span className="text-xs text-muted-foreground">Local</span>
                    <span className="text-sm break-words hyphens-auto">{registo.local}</span>
                  </div>

                  <div className="flex flex-col min-w-0">
                    <span className="text-xs text-muted-foreground">Tipo de Crime</span>
                    <span className="text-sm truncate max-w-full">{registo.tipoCrime}</span>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="px-6 pt-3 border-t mt-auto">
                <div className="flex gap-2 w-full">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => onEdit(registo)}
                  >
                    <IconEdit className="size-4 mr-2" />
                    Editar
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 text-destructive"
                    onClick={() => confirmDelete(registo)}
                  >
                    <IconTrash className="size-4 mr-2" />
                    Eliminar
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))
        )}
      </div>

      {/* Dialog de confirmação de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmar eliminação</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja eliminar este registo? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "A eliminar..." : "Eliminar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
