"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout
import { ContactosTable } from "@/components/contactos-table";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { IconPlus } from "@tabler/icons-react";
import { SearchBar } from "@/components/search-bar";
import { ContactoForm } from "@/components/contacto-form";
import {
  addContacto,
  updateContacto,
  deleteContacto,
  listenToContactos,
  Contacto
} from "@/services/contactos-service";

export default function ClientPage() {
  const { user, loading, profileLoading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true); // Estado para controlar o carregamento dos dados
  // Usamos setContactos no listener, mas a variável contactos não é usada diretamente
  const [, setContactos] = useState<Contacto[]>([]);
  const [filteredContactos, setFilteredContactos] = useState<Contacto[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Nota: Usamos o listener em tempo real em vez de carregar os contactos diretamente

  // Configurar listener em tempo real para contactos quando o usuário estiver autenticado
  useEffect(() => {
    // Se o usuário não estiver autenticado e não estiver carregando, redirecionar para o login
    if (!loading && !user) {
      router.push("/login");
    } else if (!loading && !profileLoading && user) {
      // Configurar listener em tempo real para atualizações
      const unsubscribe = listenToContactos((updatedContactos) => {
        setContactos(updatedContactos);

        // Aplicar filtro atual aos contactos atualizados
        if (searchQuery.trim()) {
          const query = searchQuery.toLowerCase().trim();
          const filtered = updatedContactos.filter(contacto =>
            contacto.nome.toLowerCase().includes(query) ||
            contacto.telefones.some(tel => tel.value.toLowerCase().includes(query)) ||
            contacto.emails.some(email => email.value.toLowerCase().includes(query))
          );
          setFilteredContactos(filtered);
        } else {
          setFilteredContactos(updatedContactos);
        }

        setIsLoading(false);
      });

      // Limpar listener quando o componente for desmontado
      return () => {
        unsubscribe();
      };
    }
  }, [user, loading, profileLoading, router, searchQuery]);



  // Função para lidar com a pesquisa
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Função para lidar com a adição de um novo contacto
  const handleSaveComplete = async (contactoData: {
    nome: string;
    telefones: { value: string; primary?: boolean }[];
    emails: { value: string; primary?: boolean }[];
    createdBy: string;
  }) => {
    try {
      // Adicionar o contacto ao Firestore
      await addContacto(contactoData);
      // O listener em tempo real já vai atualizar os dados automaticamente
    } catch (error) {
      console.error("Erro ao adicionar contacto:", error);
      throw error;
    }
  };

  // Manipular a exclusão de um contacto
  const handleDelete = async (id: string) => {
    if (!user?.uid) {
      console.error("Usuário não autenticado");
      throw new Error("Usuário não autenticado");
    }

    try {
      await deleteContacto(id, user.uid);
      // O listener em tempo real já vai atualizar os dados automaticamente
    } catch (error) {
      console.error("Erro ao excluir contacto:", error);
      throw error;
    }
  };

  // Manipular a edição de um contacto
  const handleEdit = async (id: string, updatedContacto: Contacto) => {
    try {
      const { nome, telefones, emails } = updatedContacto;
      await updateContacto(id, { nome, telefones, emails }, user?.uid);
      // O listener em tempo real já vai atualizar os dados automaticamente
    } catch (error) {
      console.error("Erro ao atualizar contacto:", error);
      throw error;
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Contactos</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie os contactos disponíveis no sistema
              </p>
            </div>
            <Button onClick={() => setIsAddModalOpen(true)} className="shrink-0">
              <IconPlus className="size-4 mr-2" />
              Adicionar Contacto
            </Button>
          </div>

          {/* Barra de pesquisa */}
          <div className="mb-6">
            <SearchBar
              onSearch={handleSearch}
              placeholder="Pesquisar por nome, telefone ou email..."
              className="max-w-full sm:max-w-md"
            />
          </div>

          {/* Seção de listagem */}
          <div className="mt-2">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              </div>
            ) : (
              <ContactosTable
                contactos={filteredContactos}
                onDelete={handleDelete}
                onEdit={handleEdit}
              />
            )}
          </div>

          {/* Modal de Adição */}
          <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Adicionar Novo Contacto</DialogTitle>
              </DialogHeader>
              <ContactoForm onSaveComplete={(contactoData) => {
                handleSaveComplete(contactoData);
                setIsAddModalOpen(false);
              }} />
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
