"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { IconDotsVertical, IconEdit, IconTrash } from "@tabler/icons-react";
import { Estabelecimento } from "@/types/estabelecimento";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth-context";

interface EstabelecimentosTableProps {
  estabelecimentos: Estabelecimento[];
  onEdit: (estabelecimento: Estabelecimento) => void;
  onDelete: (id: string) => Promise<void>;
}

export function EstabelecimentosTable({
  estabelecimentos,
  onEdit,
  onDelete,
}: EstabelecimentosTableProps) {
  const { isAdmin, isTeamLeader } = useAuth();
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [estabelecimentoToDelete, setEstabelecimentoToDelete] = useState<Estabelecimento | null>(null);

  // Verificar se o usuário tem permissão para editar/excluir
  const canEdit = isAdmin || isTeamLeader;

  // Função para abrir o diálogo de confirmação de exclusão
  const handleDeleteClick = (estabelecimento: Estabelecimento) => {
    setEstabelecimentoToDelete(estabelecimento);
    setIsDeleteDialogOpen(true);
  };

  // Função para lidar com a exclusão
  const handleDelete = async () => {
    if (!estabelecimentoToDelete) return;

    setIsDeleting(estabelecimentoToDelete.id);
    try {
      await onDelete(estabelecimentoToDelete.id);
      toast.success("Estabelecimento excluído com sucesso");
    } catch (error) {
      console.error("Erro ao excluir estabelecimento:", error);
      toast.error("Erro ao excluir estabelecimento");
    } finally {
      setIsDeleting(null);
      setIsDeleteDialogOpen(false);
      setEstabelecimentoToDelete(null);
    }
  };

  // Função para obter o nome do tipo de estabelecimento
  const getTipoNome = (tipo: string): string => {
    switch (tipo) {
      case "bar":
        return "Bar";
      case "discoteca":
        return "Discoteca";
      case "restaurante":
        return "Restaurante";
      case "clube":
        return "Clube";
      case "café":
        return "Café";
      default:
        return "Outro";
    }
  };

  return (
    <>
      <div className="relative flex flex-col gap-4">
        <div className="overflow-hidden rounded-lg border">
          <Table className="w-full">
            <TableHeader className="bg-muted sticky top-0 z-10">
              <TableRow>
                <TableHead className="w-[30%]">Nome</TableHead>
                <TableHead className="w-[20%]">Tipo</TableHead>
                <TableHead className="w-[40%]">Endereço</TableHead>
                <TableHead className="w-[10%] text-right"></TableHead>
              </TableRow>
            </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {estabelecimentos.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                  Nenhum estabelecimento encontrado.
                </TableCell>
              </TableRow>
            ) : (
              estabelecimentos.map((estabelecimento) => (
                <TableRow key={estabelecimento.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="truncate">{estabelecimento.nome}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{getTipoNome(estabelecimento.tipo)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{estabelecimento.endereco}</div>
                  </TableCell>
                  <TableCell className="text-right">
                    {canEdit ? (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                            size="icon"
                          >
                            <IconDotsVertical className="size-4" />
                            <span className="sr-only">Abrir menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-32">
                          <DropdownMenuItem onClick={() => onEdit(estabelecimento)}>
                            <IconEdit className="size-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteClick(estabelecimento)}
                            className="text-destructive focus:text-destructive"
                            disabled={isDeleting === estabelecimento.id}
                          >
                            <IconTrash className="size-4 mr-2" />
                            Eliminar
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    ) : null}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>

      {/* Diálogo de confirmação de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Eliminar Estabelecimento</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Tem certeza que deseja eliminar o estabelecimento "
              {estabelecimentoToDelete?.nome}"? Esta ação não pode ser desfeita.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting !== null}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting !== null}
            >
              {isDeleting !== null ? "A eliminar..." : "Eliminar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
