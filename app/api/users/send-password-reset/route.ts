import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/firebase-admin';

export async function POST(request: NextRequest) {
  try {
    console.log("📧 API de envio de email de redefinição de password chamada");

    // Obter instância do Firebase Auth
    const auth = getAdminAuth();

    const body = await request.json();
    console.log("📋 Dados recebidos:", body);
    const { email } = body;

    if (!email) {
      return NextResponse.json(
        { error: 'Email é obrigatório' },
        { status: 400 }
      );
    }

    // Configurar a URL de redirecionamento após a redefinição da password
    const actionCodeSettings = {
      url: `${process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/login`,
      handleCodeInApp: false,
    };

    console.log("🔗 URL de redirecionamento configurada:", actionCodeSettings.url);

    // Verificar se o utilizador existe
    console.log("🔍 Verificando se o utilizador existe:", email);
    const userRecord = await auth.getUserByEmail(email);
    console.log("✅ Utilizador encontrado:", userRecord.uid);

    try {
      // Gerar e enviar o link de redefinição de password
      console.log("📧 Gerando e enviando link de redefinição de password...");
      const resetLink = await auth.generatePasswordResetLink(email, actionCodeSettings);
      console.log("✅ Link de redefinição de password gerado e enviado com sucesso");

      // Retornar o link apenas para fins de demonstração
      // Em um ambiente de produção real, você pode querer remover esta linha
      return NextResponse.json({
        success: true,
        message: 'Email de redefinição de password enviado com sucesso',
        link: resetLink
      });
    } catch (emailError: any) {
      console.error("Erro ao enviar email de redefinição de password:", emailError);
      return NextResponse.json(
        { error: 'Erro ao enviar email de redefinição de password', details: emailError.message || 'Erro desconhecido' },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Erro ao enviar email de redefinição de password:', error);
    console.error('Stack trace:', error.stack);

    // Tratar erros específicos do Firebase
    if (error.code === 'auth/user-not-found') {
      return NextResponse.json(
        { error: 'Utilizador não encontrado' },
        { status: 404 }
      );
    }

    if (error.code === 'auth/invalid-email') {
      return NextResponse.json(
        { error: 'O email fornecido é inválido' },
        { status: 400 }
      );
    }

    if (error.code === 'auth/invalid-continue-uri') {
      return NextResponse.json(
        { error: 'URL de redirecionamento inválido', details: 'Verifique a configuração NEXTAUTH_URL ou NEXT_PUBLIC_APP_URL' },
        { status: 400 }
      );
    }

    if (error.code === 'auth/unauthorized-continue-uri') {
      return NextResponse.json(
        { error: 'URL de redirecionamento não autorizado', details: 'O domínio não está na lista de domínios autorizados no Firebase Console' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Erro ao enviar email de redefinição de password', details: error.message },
      { status: 500 }
    );
  }
}
