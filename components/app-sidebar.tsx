"use client"

import * as React from "react"
import { useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"



import { NavMain } from "@/components/nav-main"
// import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
} from "@/components/ui/sidebar"

const getNavCategories = (isAdmin: boolean, isTeamLeader: boolean, isTeamMember: boolean, isPageHidden: (pageId: string) => boolean) => {
  const categories = [
    {
      category: "Principal",
      items: [
        {
          title: "Dashboard",
          url: "/dashboard",
        },
      ],
    }
  ];

  // Adicionar seção Operacional - acessível por todos os papéis, mas com restrições de dados
  const operacionalItems = [];

  // Todos os papéis podem acessar estas páginas (com restrições de dados)
  if (isAdmin || isTeamLeader || isTeamMember) {
    // Verificar permissões para cada página operacional
    if (!isPageHidden("condutores")) {
      operacionalItems.push({
        title: "Condutor de Serviço",
        url: "/condutores",
      });
    }

    if (!isPageHidden("registos")) {
      operacionalItems.push({
        title: "Registo Expediente",
        url: "/registos",
      });
    }

    if (!isPageHidden("identificacoes")) {
      operacionalItems.push({
        title: "Identificações",
        url: "/identificacoes",
      });
    }

    if (!isPageHidden("relatorios")) {
      operacionalItems.push({
        title: "Relatório Operacional",
        url: "/relatorios",
      });
    }
  }

  // Adicionar seção Operacional apenas se houver itens
  if (operacionalItems.length > 0) {
    categories.push({
      category: "Operacional",
      items: operacionalItems,
    });
  }

  // Adicionar seção Documentação - acessível por todos os usuários autenticados
  const documentacaoItems = [];

  if (!isPageHidden("formularios")) {
    documentacaoItems.push({
      title: "Formulários",
      url: "/formularios",
    });
  }

  if (!isPageHidden("textos")) {
    documentacaoItems.push({
      title: "Textos",
      url: "/textos",
    });
  }

  if (!isPageHidden("nips")) {
    documentacaoItems.push({
      title: "Nips Locais",
      url: "/nips",
    });
  }

  if (!isPageHidden("nips-pessoas")) {
    documentacaoItems.push({
      title: "NIPs Pessoas",
      url: "/nips-pessoas",
    });
  }

  if (!isPageHidden("contactos")) {
    documentacaoItems.push({
      title: "Contactos",
      url: "/contactos",
    });
  }

  // Adicionar seção Documentação apenas se houver itens
  if (documentacaoItems.length > 0) {
    categories.push({
      category: "Documentação",
      items: documentacaoItems,
    });
  }

  // Adicionar seção Base de Dados - acessível por todos os usuários autenticados
  const baseDadosItems = [];

  if (!isPageHidden("estabelecimentos")) {
    baseDadosItems.push({
      title: "Estabelecimentos",
      url: "/estabelecimentos",
    });
  }

  if (!isPageHidden("viaturas")) {
    baseDadosItems.push({
      title: "Viaturas",
      url: "/viaturas",
    });
  }

  if (!isPageHidden("tipos-crimes")) {
    baseDadosItems.push({
      title: "Tipos de Crimes",
      url: "/tipos-crimes",
    });
  }

  // Adicionar seção Base de Dados apenas se houver itens
  if (baseDadosItems.length > 0) {
    categories.push({
      category: "Base de Dados",
      items: baseDadosItems,
    });
  }

  // Adicionar seção de administração apenas para admins
  if (isAdmin) {
    const adminItems = [];

    adminItems.push({
      title: "Equipas",
      url: "/admin/teams",
    });

    adminItems.push({
      title: "Utilizadores",
      url: "/admin/users",
    });

    // Adicionar página de permissões
    adminItems.push({
      title: "Permissões",
      url: "/admin/permissions",
    });

    categories.push({
      category: "Administração",
      items: adminItems,
    });
  }

  return categories;
};



export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, isAdmin, isTeamLeader, isTeamMember, isPageHidden } = useAuth();

  // Adicionar um efeito para limpar o estado do sidebar quando o componente for desmontado
  useEffect(() => {
    return () => {
      // Limpar ao desmontar
      if (typeof window !== 'undefined') {
        // Verificar se estamos no processo de logout
        if ((window as unknown as Record<string, unknown>).__LOGGING_OUT) {
          // Se estamos fazendo logout, não fazer nada para evitar erros de DOM
          console.log('AppSidebar desmontado durante logout, evitando manipulações do DOM');
          return;
        }

        // Remover classes do body que podem interferir com a interatividade
        document.body.classList.remove('overflow-hidden');
      }
    };
  }, []);

  // Criar um objeto de usuário com os dados do Firebase Auth
  const userData = {
    name: user?.displayName || "Utilizador",
    email: user?.email || "",
    avatar: user?.photoURL || "/avatars/shadcn.jpg",
  };

  // Obter categorias de navegação com base no papel do usuário e permissões
  const navCategories = getNavCategories(isAdmin, isTeamLeader, isTeamMember, isPageHidden);

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarContent className="pt-4">
        <NavMain categories={navCategories} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
    </Sidebar>
  )
}
