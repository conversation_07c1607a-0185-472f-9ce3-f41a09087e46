"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout
import { FormulariosTable, Formulario } from "@/components/formularios-table";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { IconPlus } from "@tabler/icons-react";
import { SearchBar } from "@/components/search-bar";
import { FileUpload } from "@/components/file-upload";
import {
  addFormulario,
  getFormularios,
  updateFormulario,
  deleteFormulario,
  listenToFormularios
} from "@/services/formularios-service";

export default function ClientPage() {
  const { user, loading, profileLoading } = useAuth(); // userProfile não é usado neste componente
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [formularios, setFormularios] = useState<Formulario[]>([]);
  const [filteredFormularios, setFilteredFormularios] = useState<Formulario[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

  // Carregar formulários do Firestore
  const loadFormularios = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      // Carregar todos os formulários
      const data = await getFormularios();
      setFormularios(data);
      setFilteredFormularios(data);
    } catch (error) {
      console.error("Erro ao carregar formulários:", error);
    } finally {
      setIsLoading(false);
    }
  }, [user, setFormularios, setFilteredFormularios, setIsLoading]);

  // Configurar listener em tempo real para formulários
  useEffect(() => {
    // Se o usuário não estiver autenticado e não estiver carregando, redirecionar para o login
    if (!loading && !user) {
      router.push("/login");
    } else if (!loading && !profileLoading && user) {
      // Configurar listener em tempo real para atualizações
      const unsubscribe = listenToFormularios((updatedFormularios) => {
        setFormularios(updatedFormularios);
        setFilteredFormularios(updatedFormularios);
        setIsLoading(false);
      });

      // Limpar listener quando o componente for desmontado
      return () => {
        unsubscribe();
      };
    }
  }, [user, loading, profileLoading, router]);

  // Efeito para filtrar formulários com base na pesquisa
  useEffect(() => {
    if (!formularios.length) return;

    if (!searchQuery.trim()) {
      setFilteredFormularios(formularios);
      return;
    }

    const query = searchQuery.toLowerCase().trim();
    const filtered = formularios.filter(formulario =>
      formulario.title.toLowerCase().includes(query)
    );

    setFilteredFormularios(filtered);
  }, [searchQuery, formularios]);

  // Função para lidar com a pesquisa
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Manipular o upload completo de um formulário
  const handleUploadComplete = async (fileData: Omit<Formulario, "id">) => {
    try {
      // Adicionar o formulário ao Firestore
      await addFormulario(fileData);

      // O listener em tempo real já vai atualizar os dados automaticamente
      // Não precisamos fazer nada aqui, pois o listener vai detectar a mudança
      // e atualizar os estados setFormularios e setFilteredFormularios
    } catch (error) {
      console.error("Erro ao salvar formulário:", error);
    }
  };

  // Manipular a exclusão de um formulário
  const handleDelete = async (id: string) => {
    if (!user?.uid) {
      console.error("Usuário não autenticado");
      throw new Error("Usuário não autenticado");
    }

    const formulario = formularios.find(f => f.id === id);
    if (!formulario) return;

    try {
      await deleteFormulario(id, formulario.url, user.uid);
      // O listener em tempo real já vai atualizar os dados automaticamente
    } catch (error) {
      console.error("Erro ao excluir formulário:", error);
      throw error;
    }
  };

  // Manipular a edição de um formulário
  const handleEdit = async (id: string, newTitle: string) => {
    try {
      await updateFormulario(id, { title: newTitle }, user?.uid);
      // O listener em tempo real já vai atualizar os dados automaticamente
    } catch (error) {
      console.error("Erro ao atualizar formulário:", error);
      throw error;
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Formulários</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie os formulários disponíveis no sistema
              </p>
            </div>
            <Button onClick={() => setIsUploadModalOpen(true)} className="shrink-0">
              <IconPlus className="size-4 mr-2" />
              Adicionar Formulário
            </Button>
          </div>

          {/* Barra de pesquisa */}
          <div className="mb-6">
            <SearchBar
              onSearch={handleSearch}
              placeholder="Pesquisar por título..."
              className="max-w-full sm:max-w-md"
            />
          </div>

          {/* Seção de listagem */}
          <div className="mt-2">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              </div>
            ) : (
              <FormulariosTable
                formularios={filteredFormularios}
                onDelete={handleDelete}
                onEdit={handleEdit}
              />
            )}
          </div>

          {/* Modal de Upload */}
          <Dialog open={isUploadModalOpen} onOpenChange={setIsUploadModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Adicionar Novo Formulário</DialogTitle>
              </DialogHeader>
              <FileUpload
                onUploadComplete={(fileData) => {
                  handleUploadComplete(fileData);
                  setIsUploadModalOpen(false);
                }}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
