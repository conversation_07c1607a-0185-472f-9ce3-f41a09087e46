"use client"

import * as React from "react"
import { useFocusManagement } from "./focus-guard"

/**
 * FocusCleaner é um componente utilitário que limpa o foco quando a rota muda
 * ou quando ocorrem outras ações que podem causar problemas de foco.
 */
export function FocusCleaner() {
  const { clearFocus } = useFocusManagement()
  
  // Limpar o foco quando a rota muda
  React.useEffect(() => {
    // Função para limpar o foco
    const handleRouteChange = () => {
      setTimeout(() => {
        clearFocus()
      }, 0)
    }
    
    // Adicionar event listener para mudanças de rota
    window.addEventListener('popstate', handleRouteChange)
    
    // Limpar event listener quando o componente for desmontado
    return () => {
      window.removeEventListener('popstate', handleRouteChange)
    }
  }, [clearFocus])
  
  // Limpar o foco periodicamente para evitar problemas persistentes
  React.useEffect(() => {
    // Verificar periodicamente se há elementos com aria-hidden que contêm foco
    const interval = setInterval(() => {
      document.querySelectorAll('[aria-hidden="true"]').forEach((el) => {
        // Verificar se o elemento contém o foco atual
        if (el.contains(document.activeElement)) {
          // Se contiver, limpar o foco
          clearFocus()
        }
      })
    }, 1000)
    
    // Limpar interval quando o componente for desmontado
    return () => {
      clearInterval(interval)
    }
  }, [clearFocus])
  
  // Este componente não renderiza nada visível
  return null
}
