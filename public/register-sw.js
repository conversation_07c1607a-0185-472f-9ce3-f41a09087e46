/**
 * Script para gerenciar Service Workers
 * Este script gerencia os Service Workers de forma seletiva para preservar a autenticação
 * enquanto evita problemas de cache em outras áreas da aplicação
 */

// Função para gerenciar Service Workers
const manageServiceWorkers = async () => {
  if (!('serviceWorker' in navigator)) {
    console.log('Service Worker não é suportado neste navegador');
    return;
  }

  try {
    // Verificar se o usuário está autenticado
    const isAuthenticated = localStorage.getItem('authUser') !== null;

    // Verificar se estamos na página de login ou logout
    const isLoginPage = window.location.pathname.includes('/login');
    const isLogoutProcess = sessionStorage.getItem('loggingOut') === 'true';

    // Obter registros de Service Workers
    const registrations = await navigator.serviceWorker.getRegistrations();

    // Decidir o que fazer com base no estado de autenticação e página atual
    if (isLogoutProcess || (isLoginPage && !isAuthenticated)) {
      // Se estamos fazendo logout ou na página de login sem autenticação,
      // desregistrar Service Workers e limpar caches
      if (registrations.length > 0) {
        console.log(`Desregistrando ${registrations.length} Service Workers`);

        for (let registration of registrations) {
          await registration.unregister();
          console.log('Service Worker desregistrado');
        }

        // Limpar caches específicos relacionados à navegação
        if ('caches' in window) {
          const cacheNames = await caches.keys();
          const navigationCaches = cacheNames.filter(name =>
            name.includes('next-data') ||
            name.includes('workbox') ||
            name.includes('webpack')
          );

          await Promise.all(
            navigationCaches.map(cacheName => {
              console.log('Limpando cache de navegação:', cacheName);
              return caches.delete(cacheName);
            })
          );
          console.log('Caches de navegação foram limpos');
        }

        // Registrar a limpeza
        sessionStorage.setItem('swCleared', 'true');
        sessionStorage.setItem('swClearedAt', new Date().toISOString());
      } else {
        console.log('Nenhum Service Worker para desregistrar');
      }
    } else {
      // Se estamos autenticados e não estamos na página de login,
      // manter os Service Workers para preservar a autenticação
      console.log('Mantendo Service Workers para preservar a autenticação');

      // Apenas registrar que verificamos
      sessionStorage.setItem('swChecked', 'true');
      sessionStorage.setItem('swCheckedAt', new Date().toISOString());
    }

    // Adicionar listener para navegação
    window.addEventListener('beforeunload', () => {
      // Armazenar a última rota para diagnóstico
      sessionStorage.setItem('lastNavigationFrom', window.location.pathname);
      sessionStorage.setItem('lastNavigationAt', new Date().toISOString());
    });

  } catch (error) {
    console.error('Erro ao gerenciar Service Workers:', error);
  }
};

// Executar quando a página carregar
window.addEventListener('load', manageServiceWorkers);
