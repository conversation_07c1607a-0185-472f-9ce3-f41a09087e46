const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Certifique-se de que o diretório de ícones existe
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Tamanhos de ícones necessários para PWA
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Função para gerar ícones
async function generateIcons() {
  const svgPath = path.join(__dirname, '../public/logo.svg');

  // Gerar ícones para cada tamanho
  for (const size of sizes) {
    await sharp(svgPath)
      .resize(size, size)
      .png()
      .toFile(path.join(iconsDir, `icon-${size}x${size}.png`));

    console.log(`Gerado ícone ${size}x${size}`);
  }

  // Gerar ícone para Apple (com fundo branco)
  await sharp(svgPath)
    .resize(180, 180)
    .png()
    .toFile(path.join(iconsDir, 'apple-touch-icon.png'));

  console.log('Gerado ícone para Apple');

  // Gerar favicon
  await sharp(svgPath)
    .resize(32, 32)
    .png()
    .toFile(path.join(__dirname, '../public/favicon.png'));

  console.log('Gerado favicon');

  // Criar um SVG monocromático para o Safari

  // Criando um SVG monocromático simplificado para o Safari
  const safariSvgContent = `<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
<g>
<path d="M108.98 79.0194H120.064L129.835 94.6978L147.01 79.0194H157.744L134.26 100.383C132.726 101.76 130.667 103.031 127.819 103.031C125.103 103.031 123.569 101.866 122.606 100.383L108.98 79.0194Z" fill="black"/>
<path d="M107.502 86.6114C107.502 92.4732 101.149 96.6046 90.9841 96.6046H75.6494L74.335 102.643H65.3095L67.9383 90.5663H93.1747C96.3293 90.5663 98.2571 89.2598 98.2571 87.4236C98.2571 86.0464 96.8989 85.1636 94.3577 85.1636H69.1213L76.4819 79.0194H94.8835C103.033 79.0194 107.502 82.0915 107.502 86.6114Z" fill="black"/>
<path d="M38.6861 96.5693H63.6596L56.4743 102.643H28.2585L33.4723 79.0194H67.3838L60.1546 85.1636H41.1834L40.57 87.9886H63.7473L57.4382 93.356H39.3871L38.6861 96.5693Z" fill="black"/>
</g>
</svg>`;

  // Salvando o SVG monocromático para o Safari
  fs.writeFileSync(path.join(iconsDir, 'safari-pinned-tab.svg'), safariSvgContent);
  console.log('Gerado safari-pinned-tab.svg monocromático');

  console.log('Todos os ícones foram gerados com sucesso!');
}

generateIcons().catch(err => {
  console.error('Erro ao gerar ícones:', err);
  process.exit(1);
});
