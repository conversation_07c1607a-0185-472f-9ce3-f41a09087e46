rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Regra temporária para testes: permitir acesso total a todos os usuários autenticados
    // ATENÇÃO: Remover esta regra após os testes!
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    // Funções auxiliares para verificação de papéis
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    function isTeamLeader() {
      return isAuthenticated() &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'team_leader';
    }

    function isTeamMember() {
      return isAuthenticated() &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'team_member';
    }

    function getUserTeamId() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.teamId;
    }

    // Nota: Removidas funções não utilizadas isInSameTeam e isTeamLeaderOf

    // Regra padrão: negar acesso a tudo por padrão
    match /{document=**} {
      allow read, write: if false;
    }

    // Regras para a coleção de usuários
    match /users/{userId} {
      // Permitir leitura e escrita apenas para o próprio usuário
      allow read, write: if isAuthenticated() && request.auth.uid == userId;

      // Permitir leitura para administradores
      allow read: if isAdmin();

      // Permitir leitura para líderes e membros de equipa
      allow read: if isTeamLeader() || isTeamMember();

      // Permitir atualização de papel apenas para administradores
      allow update: if isAdmin() &&
        (request.resource.data.diff(resource.data).affectedKeys()
          .hasAny(['role', 'teamId']));
    }

    // Regras para a coleção de equipas
    match /teams/{teamId} {
      // Administradores podem fazer tudo
      allow read, write: if isAdmin();

      // Líderes de equipa e membros podem ler qualquer equipa
      // Isso é necessário para que possam ver suas próprias equipas
      allow read: if isTeamLeader() || isTeamMember();
    }

    // Regras para coleções restritas por equipa
    match /registos/{registoId} {
      // Administradores podem fazer tudo
      allow read, write: if isAdmin();

      // Líderes e membros de equipa podem ler registos
      // Permitir leitura para consultas e documentos individuais
      allow read: if isTeamLeader() || isTeamMember();

      // Líderes de equipa podem criar, atualizar e excluir registos da sua equipa
      allow create: if isTeamLeader() && request.resource.data.teamId == getUserTeamId();
      allow update, delete: if isTeamLeader() &&
        (resource.data.teamId == null || resource.data.teamId == getUserTeamId());

      // Membros de equipa podem criar registos para sua equipa
      allow create: if isTeamMember() && request.resource.data.teamId == getUserTeamId();

      // Regra simplificada: Membros de equipa podem atualizar registos onde eles são os responsáveis
      allow update: if isTeamMember() && resource.data.responsavelId == request.auth.uid;

      // Regra alternativa: Membros de equipa podem atualizar registos que eles criaram
      allow update: if isTeamMember() && resource.data.createdBy == request.auth.uid;

      // Regra temporária para testes: Membros de equipa podem atualizar qualquer registo da sua equipa
      allow update: if isTeamMember() &&
        (resource.data.teamId == null || resource.data.teamId == getUserTeamId());
    }

    match /condutores/{condutorId} {
      // Administradores podem fazer tudo
      allow read, write: if isAdmin();

      // Líderes e membros de equipa podem ler registos
      // Permitir leitura para consultas e documentos individuais
      allow read: if isTeamLeader() || isTeamMember();

      // Líderes de equipa podem criar, atualizar e excluir registos da sua equipa
      allow create: if isTeamLeader() && request.resource.data.teamId == getUserTeamId();
      allow update, delete: if isTeamLeader() &&
        (resource.data.teamId == null || resource.data.teamId == getUserTeamId());

      // Membros de equipa podem criar registos para sua equipa
      allow create: if isTeamMember() && request.resource.data.teamId == getUserTeamId();

      // Regra simplificada: Membros de equipa podem atualizar registos onde eles são os responsáveis
      allow update: if isTeamMember() && resource.data.userId == request.auth.uid;

      // Regra alternativa: Membros de equipa podem atualizar registos que eles criaram
      allow update: if isTeamMember() && resource.data.createdBy == request.auth.uid;

      // Regra temporária para testes: Membros de equipa podem atualizar qualquer registo da sua equipa
      allow update: if isTeamMember() &&
        (resource.data.teamId == null || resource.data.teamId == getUserTeamId());
    }

    match /identificacoes/{identificacaoId} {
      // Administradores podem fazer tudo
      allow read, write: if isAdmin();

      // Líderes e membros de equipa podem ler registos
      // Permitir leitura para consultas e documentos individuais
      allow read: if isTeamLeader() || isTeamMember();

      // Líderes de equipa podem criar, atualizar e excluir registos da sua equipa
      allow create: if isTeamLeader() && request.resource.data.teamId == getUserTeamId();
      allow update, delete: if isTeamLeader() &&
        (resource.data.teamId == null || resource.data.teamId == getUserTeamId());

      // Membros de equipa podem criar registos para sua equipa
      allow create: if isTeamMember() && request.resource.data.teamId == getUserTeamId();

      // Regra simplificada: Membros de equipa podem atualizar registos onde eles são os responsáveis
      allow update: if isTeamMember() && resource.data.responsavelId == request.auth.uid;

      // Regra alternativa: Membros de equipa podem atualizar registos que eles criaram
      allow update: if isTeamMember() && resource.data.createdBy == request.auth.uid;

      // Regra temporária para testes: Membros de equipa podem atualizar qualquer registo da sua equipa
      allow update: if isTeamMember() &&
        (resource.data.teamId == null || resource.data.teamId == getUserTeamId());
    }

    // Coleção de permissões de página
    match /page_permissions/{permissionId} {
      // Apenas administradores podem criar, atualizar e excluir permissões
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin();
    }

    // Coleções compartilhadas (acessíveis por todos os usuários autenticados)
    match /formularios/{formularioId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeamLeader();
    }

    match /textos/{textoId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeamLeader();
    }

    match /nips/{nipId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeamLeader();
    }

    match /contactos/{contactoId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeamLeader();
    }

    match /estabelecimentos/{estabelecimentoId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeamLeader();
    }

    match /viaturas/{viaturaId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeamLeader();
    }

    match /crimeTypes/{crimeTypeId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTeamLeader();
    }
  }
}
