"use client";

import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { IconSearch, IconX } from "@tabler/icons-react";

interface SearchBarProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  className?: string;
  initialQuery?: string;
}

export function SearchBar({
  onSearch,
  placeholder = "Pesquisar...",
  className = "",
  initialQuery = "",
}: SearchBarProps) {
  const [query, setQuery] = useState(initialQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(initialQuery);

  // Atualizar a query quando o initialQuery mudar
  useEffect(() => {
    setQuery(initialQuery);
    setDebouncedQuery(initialQuery);
  }, [initialQuery]);

  // Debounce para evitar muitas chamadas de pesquisa durante a digitação
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  // Ref para rastrear se é a primeira renderização
  const isFirstRenderRef = useRef(true);

  // Chamar a função de pesquisa quando a query debounced mudar
  useEffect(() => {
    // Evitar chamadas desnecessárias apenas na primeira renderização se a query estiver vazia
    if (isFirstRenderRef.current && debouncedQuery === "" && initialQuery === "") {
      isFirstRenderRef.current = false;
      return;
    }

    // Para todas as outras mudanças, sempre chamar onSearch
    onSearch(debouncedQuery);
  }, [debouncedQuery, onSearch, initialQuery]);

  // Limpar a pesquisa
  const handleClear = () => {
    setQuery("");
    // Chamar diretamente onSearch com string vazia para garantir que a pesquisa seja resetada imediatamente
    onSearch("");
  };

  return (
    <div className={`relative flex items-center ${className}`}>
      <div className="relative w-full">
        <Input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pr-10 pl-10"
        />
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <IconSearch className="size-4 text-muted-foreground" />
        </div>
        {query && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute inset-y-0 right-0 flex items-center pr-3 h-full"
            onClick={handleClear}
          >
            <IconX className="size-4" />
            <span className="sr-only">Limpar pesquisa</span>
          </Button>
        )}
      </div>
    </div>
  );
}
