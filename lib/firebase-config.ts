/**
 * Configuração do Firebase para o lado do cliente
 *
 * Este arquivo contém funções para carregar as credenciais do Firebase de forma segura
 * no lado do cliente, evitando que as credenciais sejam expostas no código compilado.
 */

// Configuração do Firebase usando variáveis de ambiente
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
};

// Função para carregar as credenciais do Firebase de forma segura
export async function loadFirebaseConfig() {
  // Verificar se as variáveis de ambiente estão definidas
  if (!firebaseConfig.apiKey) {
    throw new Error('Variáveis de ambiente do Firebase não estão definidas');
  }

  // Retornar a configuração do Firebase
  return firebaseConfig;
}
