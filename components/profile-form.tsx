"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth-context";
import { categories } from "@/lib/categories";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

// Definir o esquema de validação
const profileSchema = z.object({
  fullName: z.string().min(3, "O nome completo deve ter pelo menos 3 caracteres"),
  registrationNumber: z.string().min(1, "O número de matrícula é obrigatório"),
  category: z.string().min(1, "A categoria é obrigatória"),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export function ProfileForm({
  onComplete,
  isAccountPage = false
}: {
  onComplete?: () => void;
  isAccountPage?: boolean;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const { updateUserProfile, userProfile, canEditProfile, isAdmin } = useAuth();

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      fullName: userProfile?.fullName || "",
      registrationNumber: userProfile?.registrationNumber || "",
      category: userProfile?.category || "",
    },
  });

  // Atualizar os valores do formulário quando o userProfile mudar
  useEffect(() => {
    if (userProfile) {
      console.log("Atualizando formulário com dados do perfil:", userProfile);
      form.reset({
        fullName: userProfile.fullName || "",
        registrationNumber: userProfile.registrationNumber || "",
        category: userProfile.category || "",
      });
    }
  }, [userProfile, form]);

  async function onSubmit(data: ProfileFormValues) {
    setIsSubmitting(true);
    const loadingToast = toast.loading("A guardar informações...", {
      description: "A processar os seus dados",
    });

    try {
      // Atualizar o perfil do usuário com os novos dados
      await updateUserProfile({
        fullName: data.fullName,
        registrationNumber: data.registrationNumber,
        category: data.category,
        profileCompleted: true,
      });

      toast.dismiss(loadingToast);
      toast.success("Perfil atualizado com sucesso!", {
        description: "As suas informações foram guardadas.",
      });

      if (isAccountPage) {
        // Se estiver na página de conta, não redireciona
      } else if (onComplete) {
        onComplete();
      } else {
        router.push("/dashboard");
      }
    } catch (error) {
      console.error("Erro ao salvar perfil:", error);
      toast.dismiss(loadingToast);
      toast.error("Erro ao guardar informações", {
        description: "Ocorreu um erro ao processar os seus dados. Tente novamente.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  // Verificar se o utilizador pode editar seu perfil
  // Administradores sempre podem editar, mesmo que o perfil esteja bloqueado
  const canEdit = isAdmin || canEditProfile || !isAccountPage;

  return (
    <Card className={`w-full ${isAccountPage ? '' : 'max-w-lg'}`}>
      <CardHeader>
        <CardTitle>{isAccountPage ? "Informações Pessoais" : "Informações Adicionais"}</CardTitle>
        <CardDescription>
          {isAccountPage
            ? canEdit
              ? "Atualize as suas informações pessoais abaixo."
              : "As suas informações pessoais estão bloqueadas para edição."
            : "Por favor, complete o seu perfil para continuar."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome Completo</FormLabel>
                  <FormControl>
                    <Input placeholder="Introduza o seu nome completo" {...field} disabled={!canEdit} />
                  </FormControl>
                  <FormDescription>
                    Este é o nome que será exibido no sistema.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="registrationNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Número de Matrícula</FormLabel>
                  <FormControl>
                    <Input placeholder="Introduza o seu número de matrícula" {...field} disabled={!canEdit} />
                  </FormControl>
                  <FormDescription>
                    O seu número de identificação único.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Categoria</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={!canEdit}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione a sua categoria" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(categories).map(([group, items]) => (
                        <SelectGroup key={group}>
                          <SelectLabel>{group}</SelectLabel>
                          {items.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    A sua categoria profissional.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <CardFooter className="flex justify-end px-0 pt-4">
              {canEdit ? (
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "A guardar..." : isAccountPage ? "Atualizar Perfil" : "Guardar Informações"}
                </Button>
              ) : (
                <Button type="button" disabled>
                  Edição bloqueada
                </Button>
              )}
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
