"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  IconEdit,
  IconTrash,
  IconPlus,
  IconCar,
  IconDotsVertical,
} from "@tabler/icons-react";
import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";
import { Viatura, addViatura, deleteViatura, updateViatura } from "@/services/viaturas-service";
import { ViaturaForm } from "@/components/viatura-form";
import { useAuth } from "@/contexts/auth-context";

interface ViaturasTableProps {
  viaturas: Viatura[];
  loading: boolean;
  onAddViatura: () => void;
}

export function ViaturasTable({ viaturas, loading, onAddViatura }: ViaturasTableProps) {
  const { user } = useAuth();

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedViatura, setSelectedViatura] = useState<Viatura | null>(null);



  // Função para editar uma viatura
  const handleEditViatura = async (viaturaData: Omit<Viatura, "id" | "createdAt" | "updatedAt">) => {
    try {
      if (!selectedViatura) return;

      await updateViatura(selectedViatura.id, viaturaData, user?.uid);
      setIsEditModalOpen(false);
      setSelectedViatura(null);
      showSuccessToast("Viatura atualizada com sucesso");
    } catch (error) {
      console.error("Erro ao editar viatura:", error);
      showErrorToast("Erro ao editar viatura");
    }
  };

  // Função para excluir uma viatura
  const handleDeleteViatura = async () => {
    if (!selectedViatura) return;

    try {
      await deleteViatura(selectedViatura.id, user?.uid);
      setIsDeleteDialogOpen(false);
      setSelectedViatura(null);
      showSuccessToast("Viatura excluída com sucesso");
    } catch (error) {
      console.error("Erro ao excluir viatura:", error);
      showErrorToast("Erro ao excluir viatura");
    }
  };

  // Função para abrir o modal de edição
  const handleOpenEditModal = (viatura: Viatura) => {
    setSelectedViatura(viatura);
    setIsEditModalOpen(true);
  };

  // Função para abrir o diálogo de confirmação de exclusão
  const handleOpenDeleteDialog = (viatura: Viatura) => {
    setSelectedViatura(viatura);
    setIsDeleteDialogOpen(true);
  };

  // Função para formatar a data
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("pt-PT", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date);
  };

  return (
    <>

      <div className="rounded-md border overflow-x-auto">
        <Table className="w-full table-fixed">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[45%]">Matrícula</TableHead>
              <TableHead className="w-[45%]">Modelo</TableHead>
              <TableHead className="w-[10%] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {loading ? (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center text-muted-foreground">
                  <div className="flex flex-col items-center gap-2">
                    <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                    <p>A carregar viaturas...</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : viaturas.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center text-muted-foreground">
                  Nenhuma viatura encontrada.
                </TableCell>
              </TableRow>
            ) : (
              viaturas.map((viatura) => (
                <TableRow key={viatura.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="truncate">{viatura.matricula}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{viatura.modelo}</div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-32">
                        <DropdownMenuItem onClick={() => handleOpenEditModal(viatura)}>
                          <IconEdit className="size-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleOpenDeleteDialog(viatura)}
                          className="text-destructive focus:text-destructive"
                        >
                          <IconTrash className="size-4 mr-2" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>





      {/* Modal para editar viatura */}
      {selectedViatura && (
        <ViaturaForm
          open={isEditModalOpen}
          onOpenChange={setIsEditModalOpen}
          onSaveComplete={handleEditViatura}
          initialData={selectedViatura}
        />
      )}

      {/* Diálogo de confirmação de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir a viatura {selectedViatura?.matricula}? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteViatura}
            >
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
