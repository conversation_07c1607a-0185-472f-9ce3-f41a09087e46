/**
 * Utilitário para copiar texto para a área de transferência
 * com suporte para dispositivos móveis e fallbacks
 */

import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";

interface CopyOptions {
  successMessage?: string;
  errorMessage?: string;
}

/**
 * Copia texto para a área de transferência usando várias estratégias
 * para garantir compatibilidade com diferentes dispositivos e navegadores
 */
export async function copyToClipboard(
  text: string,
  options: CopyOptions = {}
): Promise<boolean> {
  const {
    successMessage = "Texto copiado",
    errorMessage = "Erro ao copiar texto"
  } = options;

  // Lista de estratégias de cópia em ordem de preferência
  const strategies = [
    navigatorClipboardWriteText,
    documentExecCommand,
    inputSelectionCopy,
    mobilePromptCopy
  ];

  // Tenta cada estratégia até que uma funcione
  for (const strategy of strategies) {
    try {
      const success = await strategy(text);
      if (success) {
        showSuccessToast(successMessage);
        return true;
      }
    } catch (error) {
      console.error(`Estratégia de cópia falhou:`, error);
      // Continua para a próxima estratégia
    }
  }

  // Se todas as estratégias falharem
  showErrorToast(errorMessage);
  return false;
}

/**
 * Estratégia 1: Usar a API Clipboard moderna
 */
async function navigatorClipboardWriteText(text: string): Promise<boolean> {
  if (!navigator.clipboard || !navigator.clipboard.writeText) {
    return false;
  }

  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error("Erro ao usar navigator.clipboard.writeText:", error);
    return false;
  }
}

/**
 * Estratégia 2: Usar document.execCommand
 */
async function documentExecCommand(text: string): Promise<boolean> {
  try {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Configurar o textarea para ser o menos intrusivo possível
    textArea.style.position = "fixed";
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.width = "2em";
    textArea.style.height = "2em";
    textArea.style.padding = "0";
    textArea.style.border = "none";
    textArea.style.outline = "none";
    textArea.style.boxShadow = "none";
    textArea.style.background = "transparent";
    textArea.style.fontSize = "16px"; // Evita zoom em iOS

    // Adicionar ao DOM, selecionar e copiar
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    const successful = document.execCommand("copy");
    document.body.removeChild(textArea);
    return successful;
  } catch (error) {
    console.error("Erro ao usar document.execCommand:", error);
    return false;
  }
}

/**
 * Estratégia 3: Usar input e seleção para dispositivos que requerem interação do usuário
 * Esta estratégia é menos intrusiva que a anterior e pode funcionar em alguns dispositivos móveis
 */
async function inputSelectionCopy(text: string): Promise<boolean> {
  try {
    // Criar um input em vez de textarea (melhor para dispositivos móveis)
    const input = document.createElement("input");
    input.setAttribute("readonly", "");
    input.setAttribute("value", text);
    input.style.position = "fixed";
    input.style.opacity = "0";
    input.style.fontSize = "16px"; // Evita zoom em iOS

    document.body.appendChild(input);

    // Em iOS, precisamos de uma abordagem diferente
    if (navigator.userAgent.match(/ipad|iphone/i)) {
      // Criar um range e selecionar
      const range = document.createRange();
      range.selectNodeContents(input);

      const selection = window.getSelection();
      if (!selection) return false;

      selection.removeAllRanges();
      selection.addRange(range);
      input.setSelectionRange(0, text.length);
    } else {
      input.focus();
      input.select();
    }

    const successful = document.execCommand("copy");
    document.body.removeChild(input);
    return successful;
  } catch (error) {
    console.error("Erro ao usar input selection:", error);
    return false;
  }
}

/**
 * Estratégia 4: Última tentativa - mostrar o texto e pedir ao usuário para copiar manualmente
 * Esta é uma solução de último recurso
 */
async function mobilePromptCopy(text: string): Promise<boolean> {
  try {
    // Criar um elemento para mostrar o texto
    const container = document.createElement("div");
    container.style.position = "fixed";
    container.style.top = "50%";
    container.style.left = "50%";
    container.style.transform = "translate(-50%, -50%)";
    container.style.zIndex = "9999";
    container.style.backgroundColor = "white";
    container.style.color = "black";
    container.style.padding = "20px";
    container.style.borderRadius = "8px";
    container.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
    container.style.maxWidth = "90%";
    container.style.maxHeight = "70%";
    container.style.overflow = "auto";

    // Adicionar instruções e o texto a ser copiado
    container.innerHTML = `
      <div style="margin-bottom: 10px; font-weight: bold;">
        Não foi possível copiar automaticamente. Por favor, selecione e copie o texto abaixo:
      </div>
      <div style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; word-break: break-all;">
        ${text}
      </div>
      <div style="margin-top: 15px; text-align: center;">
        <button id="clipboard-close-btn" style="padding: 8px 16px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Fechar
        </button>
      </div>
    `;

    document.body.appendChild(container);

    // Adicionar evento para fechar
    return new Promise((resolve) => {
      document.getElementById("clipboard-close-btn")?.addEventListener("click", () => {
        document.body.removeChild(container);
        resolve(true); // Consideramos como sucesso, mesmo que o usuário tenha que copiar manualmente
      });
    });
  } catch (error) {
    console.error("Erro ao mostrar prompt manual:", error);
    return false;
  }
}
