"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  IconDotsVertical,
  IconEdit,
  IconTrash,
  IconEye,
  IconUser,
  IconHome,
  IconBriefcase,
  IconUsers,
  IconMapPin,
  IconNotes
} from "@tabler/icons-react";
import { toast } from "sonner";
import { Identificacao, deleteIdentificacao, updateIdentificacao } from "@/services/identificacoes-service";
import { IdentificacaoPDFDownloadButton } from "./identificacao-pdf";
import { auth } from "@/lib/firebase";

interface IdentificacoesTableProps {
  identificacoes: Identificacao[];
  onRefresh: () => void;
}

export function IdentificacoesTable({ identificacoes, onRefresh }: IdentificacoesTableProps) {
  const [selectedIdentificacao, setSelectedIdentificacao] = useState<Identificacao | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Estado para o formulário de edição
  const [editFormData, setEditFormData] = useState({
    nomeCompleto: "",
    documentoIdentificacao: "Cartão de Cidadão",
    numeroCC: "",
    validadeCC: "",
    nif: "",
    nacionalidade: "",
    dataNascimento: "",
    telefone: "",
    morada: "",
    codigoPostal: "",
    freguesia: "",
    estadoCivil: "",
    profissao: "",
    escolaridade: "",
    nomeMae: "",
    nomePai: "",
    dataOcorrencia: "",
    horaOcorrencia: "",
    localOcorrencia: "",
    observacoes: ""
  });

  // Função para visualizar uma identificação
  const handleView = (identificacao: Identificacao) => {
    setSelectedIdentificacao(identificacao);
    setIsViewModalOpen(true);
  };

  // Função para abrir o modal de edição
  const handleEdit = (identificacao: Identificacao) => {
    setSelectedIdentificacao(identificacao);

    // Preencher o formulário com os dados da identificação selecionada
    setEditFormData({
      nomeCompleto: identificacao.nomeCompleto || "",
      documentoIdentificacao: identificacao.documentoIdentificacao || "Cartão de Cidadão",
      numeroCC: identificacao.numeroCC || "",
      validadeCC: identificacao.validadeCC || "",
      nif: identificacao.nif || "",
      nacionalidade: identificacao.nacionalidade || "",
      dataNascimento: identificacao.dataNascimento || "",
      telefone: identificacao.telefone || "",
      morada: identificacao.morada || "",
      codigoPostal: identificacao.codigoPostal || "",
      freguesia: identificacao.freguesia || "",
      estadoCivil: identificacao.estadoCivil || "",
      profissao: identificacao.profissao || "",
      escolaridade: identificacao.escolaridade || "",
      nomeMae: identificacao.nomeMae || "",
      nomePai: identificacao.nomePai || "",
      dataOcorrencia: identificacao.dataOcorrencia || "",
      horaOcorrencia: identificacao.horaOcorrencia || "",
      localOcorrencia: identificacao.localOcorrencia || "",
      observacoes: identificacao.observacoes || ""
    });

    setIsEditModalOpen(true);
  };

  // Manipular mudanças nos campos do formulário de edição
  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({ ...prev, [name]: value }));
  };

  // Função para salvar as alterações
  const handleSaveEdit = async () => {
    if (!selectedIdentificacao) return;

    setIsSubmitting(true);
    const loadingToast = toast.loading("A salvar alterações...");

    try {
      // Atualizar a identificação no Firestore
      await updateIdentificacao(selectedIdentificacao.id, editFormData);

      toast.dismiss(loadingToast);
      toast.success("Identificação atualizada com sucesso");

      // Fechar o modal e atualizar a lista
      setIsEditModalOpen(false);
      onRefresh();
    } catch {
      toast.dismiss(loadingToast);
      toast.error("Erro ao atualizar identificação", {
        description: "Ocorreu um erro ao atualizar a identificação. Tente novamente.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para excluir uma identificação
  const handleDelete = async () => {
    if (!selectedIdentificacao) return;

    // Get current user from auth context
    const currentUser = auth.currentUser;
    if (!currentUser?.uid) {
      toast.error("Usuário não autenticado");
      return;
    }

    setIsDeleting(true);
    const loadingToast = toast.loading("A excluir identificação...");

    try {
      await deleteIdentificacao(selectedIdentificacao.id, currentUser.uid);
      toast.dismiss(loadingToast);
      toast.success("Identificação excluída com sucesso");
      setIsDeleteModalOpen(false);
      onRefresh();
    } catch {
      toast.dismiss(loadingToast);
      toast.error("Erro ao excluir identificação", {
        description: "Ocorreu um erro ao excluir a identificação. Tente novamente.",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Função para confirmar exclusão
  const confirmDelete = (identificacao: Identificacao) => {
    setSelectedIdentificacao(identificacao);
    setIsDeleteModalOpen(true);
  };

  return (
    <>
      <div className="rounded-md border overflow-x-auto">
        <Table className="w-full table-fixed">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[50%] sm:w-[30%]">Nome</TableHead>
              <TableHead className="hidden sm:table-cell w-[20%]">Documento</TableHead>
              <TableHead className="hidden md:table-cell w-[20%]">Data de Nascimento</TableHead>
              <TableHead className="hidden lg:table-cell w-[20%]">Data da Ocorrência</TableHead>
              <TableHead className="w-[60px] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {identificacoes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  Nenhuma identificação encontrada.
                </TableCell>
              </TableRow>
            ) : (
              identificacoes.map((identificacao) => (
                <TableRow key={identificacao.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium max-w-0 overflow-hidden">
                    <div className="truncate">{identificacao.nomeCompleto}</div>
                    <div className="sm:hidden text-xs text-muted-foreground mt-1 truncate">
                      {identificacao.documentoIdentificacao}: {identificacao.numeroCC}
                    </div>
                    <div className="md:hidden text-xs text-muted-foreground mt-1 truncate">
                      Nasc: {identificacao.dataNascimento || "-"}
                    </div>
                  </TableCell>
                  <TableCell className="hidden sm:table-cell max-w-0 overflow-hidden">
                    <div className="truncate">{identificacao.documentoIdentificacao}: {identificacao.numeroCC}</div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">{identificacao.dataNascimento || "-"}</TableCell>
                  <TableCell className="hidden lg:table-cell">{identificacao.dataOcorrencia || "-"}</TableCell>
                  <TableCell className="text-right p-2 whitespace-nowrap">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground flex size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem onClick={() => handleView(identificacao)}>
                          <IconEye className="size-4 mr-2" />
                          Visualizar
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(identificacao)}>
                          <IconEdit className="size-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <div className="px-2 py-1.5 text-sm">
                            <IdentificacaoPDFDownloadButton identificacao={identificacao} />
                          </div>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => confirmDelete(identificacao)}
                          className="text-destructive focus:text-destructive"
                        >
                          <IconTrash className="size-4 mr-2" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Modal de Visualização */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="sm:max-w-xl md:max-w-3xl lg:max-w-5xl xl:max-w-6xl h-auto max-h-[90vh] overflow-y-auto p-6">
          <DialogHeader>
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <DialogTitle>Detalhes da Identificação</DialogTitle>
              <div className="flex w-full sm:w-auto justify-start sm:justify-end gap-2 sm:mr-8">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 px-2 text-xs sm:text-sm"
                  onClick={() => {
                    setIsViewModalOpen(false);
                    handleEdit(selectedIdentificacao!);
                  }}
                >
                  <IconEdit className="size-4 mr-1" />
                  Editar
                </Button>
                <IdentificacaoPDFDownloadButton identificacao={selectedIdentificacao!} />
              </div>
            </div>
          </DialogHeader>
          {selectedIdentificacao && (
            <div className="space-y-6">
              {/* Dados Pessoais */}
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                  <IconUser className="size-4 text-primary" />
                  <h3 className="font-semibold text-sm">Dados Pessoais</h3>
                </div>
                <div className="p-4 sm:p-6">
                  <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Nome Completo</p>
                      <p>{selectedIdentificacao.nomeCompleto || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Documento</p>
                      <p>{selectedIdentificacao.documentoIdentificacao || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Número</p>
                      <p>{selectedIdentificacao.numeroCC || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Validade</p>
                      <p>{selectedIdentificacao.validadeCC || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">NIF</p>
                      <p>{selectedIdentificacao.nif || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Nacionalidade</p>
                      <p>{selectedIdentificacao.nacionalidade || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Data de Nascimento</p>
                      <p>{selectedIdentificacao.dataNascimento || "-"}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contato e Endereço */}
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                  <IconHome className="size-4 text-primary" />
                  <h3 className="font-semibold text-sm">Contato e Endereço</h3>
                </div>
                <div className="p-4 sm:p-6">
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Telefone</p>
                      <p>{selectedIdentificacao.telefone || "-"}</p>
                    </div>
                    <div className="sm:col-span-2">
                      <p className="text-sm font-medium text-muted-foreground mb-1">Morada</p>
                      <p>{selectedIdentificacao.morada || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Código Postal</p>
                      <p>{selectedIdentificacao.codigoPostal || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Freguesia</p>
                      <p>{selectedIdentificacao.freguesia || "-"}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Informações Complementares e Filiação */}
              <div className="grid gap-6 sm:grid-cols-2">
                {/* Informações Complementares */}
                <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                  <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                    <IconBriefcase className="size-4 text-primary" />
                    <h3 className="font-semibold text-sm">Informações Complementares</h3>
                  </div>
                  <div className="p-4 sm:p-6">
                    <div className="grid gap-4">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">Estado Civil</p>
                        <p>{selectedIdentificacao.estadoCivil || "-"}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">Profissão</p>
                        <p>{selectedIdentificacao.profissao || "-"}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">Escolaridade</p>
                        <p>{selectedIdentificacao.escolaridade || "-"}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Filiação */}
                <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                  <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                    <IconUsers className="size-4 text-primary" />
                    <h3 className="font-semibold text-sm">Filiação</h3>
                  </div>
                  <div className="p-4 sm:p-6">
                    <div className="grid gap-4">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">Nome da Mãe</p>
                        <p>{selectedIdentificacao.nomeMae || "-"}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">Nome do Pai</p>
                        <p>{selectedIdentificacao.nomePai || "-"}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Dados da Ocorrência */}
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                  <IconMapPin className="size-4 text-primary" />
                  <h3 className="font-semibold text-sm">Dados da Ocorrência</h3>
                </div>
                <div className="p-4 sm:p-6">
                  <div className="grid gap-4 sm:grid-cols-3">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Data da Ocorrência</p>
                      <p>{selectedIdentificacao.dataOcorrencia || "-"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Hora da Ocorrência</p>
                      <p>{selectedIdentificacao.horaOcorrencia || "-"}</p>
                    </div>
                    <div className="sm:col-span-3">
                      <p className="text-sm font-medium text-muted-foreground mb-1">Local da Ocorrência</p>
                      <p>{selectedIdentificacao.localOcorrencia || "-"}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Observações */}
              {selectedIdentificacao.observacoes && (
                <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                  <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                    <IconNotes className="size-4 text-primary" />
                    <h3 className="font-semibold text-sm">Observações</h3>
                  </div>
                  <div className="p-4 sm:p-6">
                    <p className="whitespace-pre-wrap">{selectedIdentificacao.observacoes}</p>
                  </div>
                </div>
              )}
            </div>
          )}

        </DialogContent>
      </Dialog>

      {/* Modal de Edição */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-2xl md:max-w-3xl lg:max-w-4xl h-auto max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Identificação</DialogTitle>
          </DialogHeader>

          {selectedIdentificacao && (
            <form onSubmit={(e) => { e.preventDefault(); handleSaveEdit(); }} className="space-y-6">
              {/* Dados Pessoais */}
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                  <IconUser className="size-4 text-primary" />
                  <h3 className="font-semibold text-sm">Dados Pessoais</h3>
                </div>
                <div className="p-4 sm:p-6">
                  <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    <div className="space-y-2">
                      <Label htmlFor="edit-nomeCompleto">Nome Completo</Label>
                      <Input
                        id="edit-nomeCompleto"
                        name="nomeCompleto"
                        value={editFormData.nomeCompleto}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-documentoIdentificacao">Documento de Identificação</Label>
                      <Select
                        value={editFormData.documentoIdentificacao}
                        onValueChange={(value) => setEditFormData(prev => ({ ...prev, documentoIdentificacao: value }))}
                        disabled={isSubmitting}
                      >
                        <SelectTrigger id="edit-documentoIdentificacao">
                          <SelectValue placeholder="Selecione o documento" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Cartão de Cidadão">Cartão de Cidadão</SelectItem>
                          <SelectItem value="Passaporte">Passaporte</SelectItem>
                          <SelectItem value="Título de Residência">Título de Residência</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-numeroCC">
                        {editFormData.documentoIdentificacao === "Cartão de Cidadão" ? "Nº do CC" :
                         editFormData.documentoIdentificacao === "Passaporte" ? "Nº do Passaporte" :
                         "Nº do Título de Residência"}
                      </Label>
                      <Input
                        id="edit-numeroCC"
                        name="numeroCC"
                        value={editFormData.numeroCC}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-validadeCC">
                        {editFormData.documentoIdentificacao === "Cartão de Cidadão" ? "Validade do CC" :
                         editFormData.documentoIdentificacao === "Passaporte" ? "Validade do Passaporte" :
                         "Validade do Título de Residência"}
                      </Label>
                      <Input
                        id="edit-validadeCC"
                        name="validadeCC"
                        value={editFormData.validadeCC}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-nif">NIF</Label>
                      <Input
                        id="edit-nif"
                        name="nif"
                        value={editFormData.nif}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-nacionalidade">Nacionalidade</Label>
                      <Input
                        id="edit-nacionalidade"
                        name="nacionalidade"
                        value={editFormData.nacionalidade}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-dataNascimento">Data de Nascimento</Label>
                      <Input
                        id="edit-dataNascimento"
                        name="dataNascimento"
                        value={editFormData.dataNascimento}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Contato e Endereço */}
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                  <IconHome className="size-4 text-primary" />
                  <h3 className="font-semibold text-sm">Contato e Endereço</h3>
                </div>
                <div className="p-4 sm:p-6">
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="edit-telefone">Telefone</Label>
                      <Input
                        id="edit-telefone"
                        name="telefone"
                        value={editFormData.telefone}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="sm:col-span-2 space-y-2">
                      <Label htmlFor="edit-morada">Morada</Label>
                      <Input
                        id="edit-morada"
                        name="morada"
                        value={editFormData.morada}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-codigoPostal">Código Postal</Label>
                      <Input
                        id="edit-codigoPostal"
                        name="codigoPostal"
                        value={editFormData.codigoPostal}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-freguesia">Freguesia</Label>
                      <Input
                        id="edit-freguesia"
                        name="freguesia"
                        value={editFormData.freguesia}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Informações Complementares e Filiação */}
              <div className="grid gap-6 sm:grid-cols-2">
                {/* Informações Complementares */}
                <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                  <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                    <IconBriefcase className="size-4 text-primary" />
                    <h3 className="font-semibold text-sm">Informações Complementares</h3>
                  </div>
                  <div className="p-4 sm:p-6">
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-estadoCivil">Estado Civil</Label>
                        <Select
                          value={editFormData.estadoCivil}
                          onValueChange={(value) => setEditFormData(prev => ({ ...prev, estadoCivil: value }))}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="edit-estadoCivil">
                            <SelectValue placeholder="Selecione o estado civil" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Solteiro(a)">Solteiro(a)</SelectItem>
                            <SelectItem value="Casado(a)">Casado(a)</SelectItem>
                            <SelectItem value="União de Facto">União de Facto</SelectItem>
                            <SelectItem value="Divorciado(a)">Divorciado(a)</SelectItem>
                            <SelectItem value="Viúvo(a)">Viúvo(a)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="edit-profissao">Profissão</Label>
                        <Input
                          id="edit-profissao"
                          name="profissao"
                          value={editFormData.profissao}
                          onChange={handleEditFormChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="edit-escolaridade">Escolaridade</Label>
                        <Select
                          value={editFormData.escolaridade}
                          onValueChange={(value) => setEditFormData(prev => ({ ...prev, escolaridade: value }))}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="edit-escolaridade">
                            <SelectValue placeholder="Selecione a escolaridade" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Ensino Básico">Ensino Básico</SelectItem>
                            <SelectItem value="Ensino Secundário">Ensino Secundário</SelectItem>
                            <SelectItem value="Licenciatura">Licenciatura</SelectItem>
                            <SelectItem value="Mestrado">Mestrado</SelectItem>
                            <SelectItem value="Doutoramento">Doutoramento</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Filiação */}
                <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                  <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                    <IconUsers className="size-4 text-primary" />
                    <h3 className="font-semibold text-sm">Filiação</h3>
                  </div>
                  <div className="p-4 sm:p-6">
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit-nomeMae">Nome da Mãe</Label>
                        <Input
                          id="edit-nomeMae"
                          name="nomeMae"
                          value={editFormData.nomeMae}
                          onChange={handleEditFormChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="edit-nomePai">Nome do Pai</Label>
                        <Input
                          id="edit-nomePai"
                          name="nomePai"
                          value={editFormData.nomePai}
                          onChange={handleEditFormChange}
                          disabled={isSubmitting}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Dados da Ocorrência */}
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                  <IconMapPin className="size-4 text-primary" />
                  <h3 className="font-semibold text-sm">Dados da Ocorrência</h3>
                </div>
                <div className="p-4 sm:p-6">
                  <div className="grid gap-4 sm:grid-cols-3">
                    <div className="space-y-2">
                      <Label htmlFor="edit-dataOcorrencia">Data da Ocorrência</Label>
                      <Input
                        id="edit-dataOcorrencia"
                        name="dataOcorrencia"
                        value={editFormData.dataOcorrencia}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="edit-horaOcorrencia">Hora da Ocorrência</Label>
                      <Input
                        id="edit-horaOcorrencia"
                        name="horaOcorrencia"
                        value={editFormData.horaOcorrencia}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>

                    <div className="sm:col-span-3 space-y-2">
                      <Label htmlFor="edit-localOcorrencia">Local da Ocorrência</Label>
                      <Input
                        id="edit-localOcorrencia"
                        name="localOcorrencia"
                        value={editFormData.localOcorrencia}
                        onChange={handleEditFormChange}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Observações */}
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                <div className="bg-muted/50 px-4 py-3 flex flex-row items-center gap-2 border-b">
                  <IconNotes className="size-4 text-primary" />
                  <h3 className="font-semibold text-sm">Observações</h3>
                </div>
                <div className="p-4 sm:p-6">
                  <div className="space-y-2">
                    <Label htmlFor="edit-observacoes">Observações</Label>
                    <Textarea
                      id="edit-observacoes"
                      name="observacoes"
                      value={editFormData.observacoes}
                      onChange={handleEditFormChange}
                      disabled={isSubmitting}
                      className="min-h-[100px]"
                    />
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditModalOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "A guardar..." : "Guardar Alterações"}
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* Modal de Confirmação de Exclusão */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Tem certeza que deseja excluir esta identificação?</p>
            <p className="text-muted-foreground text-sm mt-2">
              Esta ação não pode ser desfeita.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
              disabled={isDeleting}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "A excluir..." : "Excluir"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
