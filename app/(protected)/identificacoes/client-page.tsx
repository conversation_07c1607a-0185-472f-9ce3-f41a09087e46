"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout
import { Button } from "@/components/ui/button";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { IdentificacoesTable } from "@/components/identificacoes-table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import {
  IconUser,
  IconHome,
  IconBriefcase,
  IconUsers,
  IconMapPin,
  IconNotes
} from "@tabler/icons-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  addIdentificacao,
  getIdentificacoes,
  listenToIdentificacoes,
  Identificacao
} from "@/services/identificacoes-service";

export default function ClientPage() {
  const { user, loading, profileLoading, isAdmin, userTeamId } = useAuth();
  const router = useRouter();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [identificacoes, setIdentificacoes] = useState<Identificacao[]>([]);

  // Obter data e hora atual formatadas
  const getCurrentDateTime = () => {
    const now = new Date();

    // Formatar data como DD-MM-AAAA
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = now.getFullYear();
    const formattedDate = `${day}-${month}-${year}`;

    // Formatar hora como HH:MM
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const formattedTime = `${hours}:${minutes}`;

    return { date: formattedDate, time: formattedTime };
  };

  const { date: currentDate, time: currentTime } = getCurrentDateTime();

  // Formulário
  const [formData, setFormData] = useState({
    // Dados Pessoais
    nomeCompleto: "",
    documentoIdentificacao: "Cartão de Cidadão",
    numeroCC: "",
    validadeCC: "",
    nif: "",
    nacionalidade: "",
    dataNascimento: "",

    // Contato e Endereço
    telefone: "",
    morada: "",
    codigoPostal: "",
    freguesia: "",

    // Informações Complementares
    estadoCivil: "",
    profissao: "",
    escolaridade: "",

    // Filiação
    nomeMae: "",
    nomePai: "",

    // Dados da Ocorrência
    dataOcorrencia: currentDate,
    horaOcorrencia: currentTime,
    localOcorrencia: "",

    // Informações Adicionais
    observacoes: ""
  });

  // Carregar identificações do Firestore
  const loadIdentificacoes = useCallback(async () => {
    if (!user) return;

    try {
      console.log("Carregando identificações:");
      console.log("- isAdmin:", isAdmin);
      console.log("- userTeamId:", userTeamId);
      console.log("- userId:", user.uid);

      let data: Identificacao[] = [];
      if (!isAdmin && user) {
        if (userTeamId) {
          // Se não for admin mas tiver teamId, filtrar por equipa
          console.log("Carregando identificações para equipa:", userTeamId);
          data = await getIdentificacoes(userTeamId);
        } else {
          // Se não tiver teamId, filtrar por responsavelId
          console.log("Carregando identificações para responsável:", user.uid);
          data = await getIdentificacoes(undefined, user.uid);
        }
      } else if (isAdmin) {
        // Admin vê todas as identificações
        data = await getIdentificacoes();
      } else {
        // Fallback para caso o usuário não esteja definido
        data = [];
        console.warn("Usuário não está definido");
      }

      setIdentificacoes(data);
      setFilteredIdentificacoes(data);
    } catch (error) {
      console.error("Erro ao carregar identificações:", error);
    }
  }, [user, isAdmin]);

  // Estado para pesquisa
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredIdentificacoes, setFilteredIdentificacoes] = useState<Identificacao[]>([]);

  // Função para pesquisar identificações
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilteredIdentificacoes(identificacoes);
      return;
    }

    const filtered = identificacoes.filter((identificacao) =>
      identificacao.nomeCompleto.toLowerCase().includes(query.toLowerCase()) ||
      (identificacao.numeroCC && identificacao.numeroCC.toLowerCase().includes(query.toLowerCase()))
    );

    setFilteredIdentificacoes(filtered);
  }, [identificacoes]);

  // Configurar listener em tempo real para identificações
  useEffect(() => {
    // Se o usuário não estiver autenticado e não estiver carregando, redirecionar para o login
    if (!loading && !user) {
      router.push("/login");
    } else if (!loading && !profileLoading && user) {
      // Inicialmente, carregar identificações normalmente para mostrar algo rapidamente
      loadIdentificacoes();

      // Configurar listener em tempo real para atualizações
      let unsubscribe;

      // Log para debug
      console.log("Configuração de listener para identificações:");
      console.log("- isAdmin:", isAdmin);
      console.log("- userTeamId:", userTeamId);
      console.log("- userId:", user.uid);

      if (!isAdmin && user) {
        if (userTeamId) {
          // Se não for admin mas tiver teamId, filtrar por equipa
          console.log("Configurando listener de identificações para equipa:", userTeamId);
          unsubscribe = listenToIdentificacoes((updatedIdentificacoes) => {
            setIdentificacoes(updatedIdentificacoes);

            // Aplicar filtro de pesquisa se houver
            if (searchQuery) {
              const filtered = updatedIdentificacoes.filter((identificacao) =>
                identificacao.nomeCompleto.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (identificacao.numeroCC && identificacao.numeroCC.toLowerCase().includes(searchQuery.toLowerCase()))
              );
              setFilteredIdentificacoes(filtered);
            } else {
              setFilteredIdentificacoes(updatedIdentificacoes);
            }
          }, userTeamId);
        } else {
          // Se não tiver teamId, filtrar por responsavelId
          console.log("Configurando listener de identificações para usuário:", user.uid);
          unsubscribe = listenToIdentificacoes((updatedIdentificacoes) => {
            setIdentificacoes(updatedIdentificacoes);

            // Aplicar filtro de pesquisa se houver
            if (searchQuery) {
              const filtered = updatedIdentificacoes.filter((identificacao) =>
                identificacao.nomeCompleto.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (identificacao.numeroCC && identificacao.numeroCC.toLowerCase().includes(searchQuery.toLowerCase()))
              );
              setFilteredIdentificacoes(filtered);
            } else {
              setFilteredIdentificacoes(updatedIdentificacoes);
            }
          }, undefined, user.uid);
        }
      } else if (isAdmin) {
        // Se for admin, ver todas as identificações
        console.log("Configurando listener de identificações para admin");
        unsubscribe = listenToIdentificacoes((updatedIdentificacoes) => {
          setIdentificacoes(updatedIdentificacoes);

          // Aplicar filtro de pesquisa se houver
          if (searchQuery) {
            const filtered = updatedIdentificacoes.filter((identificacao) =>
              identificacao.nomeCompleto.toLowerCase().includes(searchQuery.toLowerCase()) ||
              (identificacao.numeroCC && identificacao.numeroCC.toLowerCase().includes(searchQuery.toLowerCase()))
            );
            setFilteredIdentificacoes(filtered);
          } else {
            setFilteredIdentificacoes(updatedIdentificacoes);
          }
        });
      } else {
        // Fallback para caso o usuário não esteja definido
        console.warn("Usuário não está definido");
        setIdentificacoes([]);
        setFilteredIdentificacoes([]);
        // Criar um unsubscribe dummy para evitar erros
        unsubscribe = () => {};
      }

      // Limpar listener quando o componente for desmontado
      return () => {
        unsubscribe();
      };
    }
  }, [user, loading, profileLoading, router, loadIdentificacoes, searchQuery]);

  // Efeito para atualizar a lista filtrada quando as identificações ou a pesquisa mudam
  useEffect(() => {
    if (searchQuery) {
      handleSearch(searchQuery);
    } else {
      setFilteredIdentificacoes(identificacoes);
    }
  }, [identificacoes, searchQuery, handleSearch]);

  // Referências para os campos de data
  const dataNascimentoRef = useRef<HTMLInputElement>(null);
  const validadeCCRef = useRef<HTMLInputElement>(null);
  const dataOcorrenciaRef = useRef<HTMLInputElement>(null);

  // Formatar data enquanto o usuário digita
  const formatDate = (value: string) => {
    // Remove todos os caracteres não numéricos
    const numbers = value.replace(/\D/g, '');

    // Aplica a máscara DD-MM-AAAA
    if (numbers.length <= 2) {
      return numbers;
    } else if (numbers.length <= 4) {
      return `${numbers.slice(0, 2)}-${numbers.slice(2)}`;
    } else {
      return `${numbers.slice(0, 2)}-${numbers.slice(2, 4)}-${numbers.slice(4, 8)}`;
    }
  };

  // Manipular mudanças nos campos do formulário
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Formatação especial para campos de data
    if (name === 'dataNascimento' || name === 'validadeCC' || name === 'dataOcorrencia') {
      const formattedValue = formatDate(value);
      setFormData(prev => ({ ...prev, [name]: formattedValue }));

      // Atualiza o valor no input para mostrar a formatação
      if (name === 'dataNascimento' && dataNascimentoRef.current) {
        dataNascimentoRef.current.value = formattedValue;
      } else if (name === 'validadeCC' && validadeCCRef.current) {
        validadeCCRef.current.value = formattedValue;
      } else if (name === 'dataOcorrencia' && dataOcorrenciaRef.current) {
        dataOcorrenciaRef.current.value = formattedValue;
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Manipular mudanças nos campos de select
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Validar o formulário
  const validateForm = () => {
    // Campos obrigatórios (apenas nome e data de nascimento são obrigatórios)
    const requiredFields = [
      { field: 'nomeCompleto', label: 'Nome Completo' },
      { field: 'dataNascimento', label: 'Data de Nascimento' }
    ];

    for (const { field, label } of requiredFields) {
      if (!formData[field as keyof typeof formData]) {
        showErrorToast(`O campo ${label} é obrigatório`);
        return false;
      }
    }

    // Validar formato da data de nascimento
    const dateRegex = /^\d{2}-\d{2}-\d{4}$/;
    if (formData.dataNascimento && !dateRegex.test(formData.dataNascimento)) {
      showErrorToast(`A data de nascimento deve estar no formato DD-MM-AAAA`);
      return false;
    }

    return true;
  };

  // Submeter o formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    if (!user) {
      showErrorToast("É necessário estar autenticado para submeter o formulário");
      return;
    }

    setIsSubmitting(true);
    const loadingToast = showLoadingToast("A submeter formulário...");

    try {
      // Preparar os dados da identificação
      const identificacaoData: Omit<Identificacao, "id" | "createdAt" | "updatedAt"> = {
        ...formData,
        createdBy: user.uid,
        teamId: userTeamId || undefined,
      };

      // Adicionar o responsável ID como uma propriedade extra
      const dataWithResponsavel = {
        ...identificacaoData,
        responsavelId: user.uid, // O responsável é o usuário atual por padrão
      };

      // Garantir que o teamId seja sempre definido para membros de equipa
      if (userTeamId) {
        identificacaoData.teamId = userTeamId;
      }

      // Log para debug
      if (!isAdmin && userTeamId) {
        console.log("Adicionando teamId à identificação:", userTeamId);
      } else {
        console.log("Não foi definido teamId para identificação. isAdmin:", isAdmin, "userTeamId:", userTeamId);
      }

      // Log detalhado dos dados da identificação
      console.log("Dados da identificação a serem salvos:", {
        nomeCompleto: identificacaoData.nomeCompleto,
        responsavelId: dataWithResponsavel.responsavelId,
        createdBy: identificacaoData.createdBy,
        teamId: identificacaoData.teamId || "[não definido]",
      });

      // Adicionar a identificação ao Firestore
      // Passamos apenas os dados que correspondem ao tipo Identificacao
      await addIdentificacao(identificacaoData);

      dismissToast(loadingToast);
      showSuccessToast("Identificação registada com sucesso");

      // Atualizar a lista de identificações
      loadIdentificacoes();

      // Limpar o formulário
      setFormData({
        nomeCompleto: "",
        documentoIdentificacao: "Cartão de Cidadão",
        numeroCC: "",
        validadeCC: "",
        nif: "",
        nacionalidade: "",
        dataNascimento: "",
        telefone: "",
        morada: "",
        codigoPostal: "",
        freguesia: "",
        estadoCivil: "",
        profissao: "",
        escolaridade: "",
        nomeMae: "",
        nomePai: "",
        dataOcorrencia: currentDate,
        horaOcorrencia: currentTime,
        localOcorrencia: "",
        observacoes: ""
      });
    } catch (error: unknown) {
      dismissToast(loadingToast);
      showErrorToast("Erro ao submeter formulário");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <Tabs defaultValue="form" className="w-full">
            <TabsList className="mb-4 w-full">
              <TabsTrigger value="form" className="flex-1">Formulário</TabsTrigger>
              <TabsTrigger value="list" className="flex-1">Lista de Identificações</TabsTrigger>
            </TabsList>

            <TabsContent value="form" className="space-y-4">
              <div className="mb-4 hidden sm:block">
                <h2 className="text-xl font-semibold">Nova Identificação</h2>
                <p className="text-muted-foreground mt-1">
                  Preencha o formulário abaixo para registar uma nova identificação
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Dados Pessoais */}
                <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <IconUser className="size-5" />
                        Dados Pessoais
                      </CardTitle>
                      <CardDescription>
                        Informações pessoais do indivíduo
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                      <div className="space-y-2">
                        <Label htmlFor="nomeCompleto">Nome Completo <span className="text-destructive">*</span></Label>
                        <Input
                          id="nomeCompleto"
                          name="nomeCompleto"
                          placeholder="Nome completo"
                          value={formData.nomeCompleto}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="documentoIdentificacao">Documento de Identificação</Label>
                        <Select
                          value={formData.documentoIdentificacao}
                          onValueChange={(value) => {
                            handleSelectChange("documentoIdentificacao", value);
                            // Limpar o campo de número quando o tipo de documento muda
                            setFormData(prev => ({ ...prev, numeroCC: "" }));
                          }}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="documentoIdentificacao">
                            <SelectValue placeholder="Selecione o documento" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Cartão de Cidadão">Cartão de Cidadão</SelectItem>
                            <SelectItem value="Passaporte">Passaporte</SelectItem>
                            <SelectItem value="Título de Residência">Título de Residência</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="numeroCC">
                          {formData.documentoIdentificacao === "Cartão de Cidadão" ? "Nº do CC" :
                           formData.documentoIdentificacao === "Passaporte" ? "Nº do Passaporte" :
                           "Nº do Título de Residência"}
                        </Label>
                        <Input
                          id="numeroCC"
                          name="numeroCC"
                          placeholder={formData.documentoIdentificacao === "Cartão de Cidadão" ? "Número do CC" :
                                      formData.documentoIdentificacao === "Passaporte" ? "Número do Passaporte" :
                                      "Número do Título de Residência"}
                          value={formData.numeroCC}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="validadeCC">
                          {formData.documentoIdentificacao === "Cartão de Cidadão" ? "Validade do CC" :
                           formData.documentoIdentificacao === "Passaporte" ? "Validade do Passaporte" :
                           "Validade do Título de Residência"}
                        </Label>
                        <Input
                          id="validadeCC"
                          name="validadeCC"
                          placeholder="DD-MM-AAAA"
                          value={formData.validadeCC}
                          onChange={handleChange}
                          disabled={isSubmitting}
                          ref={validadeCCRef}
                          maxLength={10}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="nif">NIF</Label>
                        <Input
                          id="nif"
                          name="nif"
                          placeholder="Número de Identificação Fiscal"
                          value={formData.nif}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="nacionalidade">Nacionalidade</Label>
                        <Input
                          id="nacionalidade"
                          name="nacionalidade"
                          placeholder="Nacionalidade"
                          value={formData.nacionalidade}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="dataNascimento">Data de Nascimento <span className="text-destructive">*</span></Label>
                        <Input
                          id="dataNascimento"
                          name="dataNascimento"
                          placeholder="DD-MM-AAAA"
                          value={formData.dataNascimento}
                          onChange={handleChange}
                          disabled={isSubmitting}
                          ref={dataNascimentoRef}
                          maxLength={10}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Contato e Endereço */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <IconHome className="size-5" />
                        Contato e Endereço
                      </CardTitle>
                      <CardDescription>
                        Informações de contato e endereço
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                      <div className="space-y-2">
                        <Label htmlFor="telefone">Telefone</Label>
                        <Input
                          id="telefone"
                          name="telefone"
                          placeholder="Número de telefone"
                          value={formData.telefone}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2 sm:col-span-2">
                        <Label htmlFor="morada">Morada</Label>
                        <Input
                          id="morada"
                          name="morada"
                          placeholder="Morada completa"
                          value={formData.morada}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="codigoPostal">Código Postal</Label>
                        <Input
                          id="codigoPostal"
                          name="codigoPostal"
                          placeholder="XXXX-XXX"
                          value={formData.codigoPostal}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="freguesia">Freguesia</Label>
                        <Input
                          id="freguesia"
                          name="freguesia"
                          placeholder="Freguesia"
                          value={formData.freguesia}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Informações Complementares */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <IconBriefcase className="size-5" />
                        Informações Complementares
                      </CardTitle>
                      <CardDescription>
                        Informações adicionais sobre o indivíduo
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                      <div className="space-y-2">
                        <Label htmlFor="estadoCivil">Estado Civil</Label>
                        <Select
                          value={formData.estadoCivil}
                          onValueChange={(value) => handleSelectChange("estadoCivil", value)}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="estadoCivil">
                            <SelectValue placeholder="Selecione o estado civil" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Solteiro(a)">Solteiro(a)</SelectItem>
                            <SelectItem value="Casado(a)">Casado(a)</SelectItem>
                            <SelectItem value="União de Facto">União de Facto</SelectItem>
                            <SelectItem value="Divorciado(a)">Divorciado(a)</SelectItem>
                            <SelectItem value="Viúvo(a)">Viúvo(a)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="profissao">Profissão</Label>
                        <Input
                          id="profissao"
                          name="profissao"
                          placeholder="Profissão"
                          value={formData.profissao}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="escolaridade">Escolaridade</Label>
                        <Select
                          value={formData.escolaridade}
                          onValueChange={(value) => handleSelectChange("escolaridade", value)}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="escolaridade">
                            <SelectValue placeholder="Selecione a escolaridade" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Ensino Básico">Ensino Básico</SelectItem>
                            <SelectItem value="Ensino Secundário">Ensino Secundário</SelectItem>
                            <SelectItem value="Licenciatura">Licenciatura</SelectItem>
                            <SelectItem value="Mestrado">Mestrado</SelectItem>
                            <SelectItem value="Doutoramento">Doutoramento</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Filiação */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <IconUsers className="size-5" />
                        Filiação
                      </CardTitle>
                      <CardDescription>
                        Informações sobre os pais
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="grid gap-6 sm:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="nomeMae">Nome da Mãe</Label>
                        <Input
                          id="nomeMae"
                          name="nomeMae"
                          placeholder="Nome completo da mãe"
                          value={formData.nomeMae}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="nomePai">Nome do Pai</Label>
                        <Input
                          id="nomePai"
                          name="nomePai"
                          placeholder="Nome completo do pai"
                          value={formData.nomePai}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Dados da Ocorrência */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <IconMapPin className="size-5" />
                        Dados da Ocorrência
                      </CardTitle>
                      <CardDescription>
                        Informações sobre a ocorrência
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                      <div className="space-y-2">
                        <Label htmlFor="dataOcorrencia">Data da Ocorrência</Label>
                        <Input
                          id="dataOcorrencia"
                          name="dataOcorrencia"
                          placeholder="DD-MM-AAAA"
                          value={formData.dataOcorrencia}
                          onChange={handleChange}
                          disabled={isSubmitting}
                          ref={dataOcorrenciaRef}
                          maxLength={10}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="horaOcorrencia">Hora da Ocorrência</Label>
                        <Input
                          id="horaOcorrencia"
                          name="horaOcorrencia"
                          placeholder="HH:MM"
                          value={formData.horaOcorrencia}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="space-y-2 sm:col-span-2 lg:col-span-3">
                        <Label htmlFor="localOcorrencia">Local da Ocorrência</Label>
                        <Input
                          id="localOcorrencia"
                          name="localOcorrencia"
                          placeholder="Endereço completo da ocorrência"
                          value={formData.localOcorrencia}
                          onChange={handleChange}
                          disabled={isSubmitting}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Informações Adicionais */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <IconNotes className="size-5" />
                        Informações Adicionais
                      </CardTitle>
                      <CardDescription>
                        Observações e informações relevantes
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Label htmlFor="observacoes">Observações</Label>
                        <Textarea
                          id="observacoes"
                          name="observacoes"
                          placeholder="Informações adicionais relevantes"
                          value={formData.observacoes}
                          onChange={handleChange}
                          disabled={isSubmitting}
                          className="min-h-[100px]"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Botões de Ação */}
                  <div className="flex flex-col sm:flex-row sm:justify-end gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          type="button"
                          variant="outline"
                          disabled={isSubmitting}
                          className="sm:order-first"
                        >
                          Limpar Formulário
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                          <DialogTitle>Confirmar ação</DialogTitle>
                          <DialogDescription>
                            Tem certeza que deseja limpar todos os campos do formulário? Esta ação não pode ser desfeita.
                          </DialogDescription>
                        </DialogHeader>
                        <DialogFooter className="mt-4 flex flex-col sm:flex-row gap-2">
                          <DialogClose asChild>
                            <Button variant="outline" className="sm:order-first">Cancelar</Button>
                          </DialogClose>
                          <Button
                            type="button"
                            variant="destructive"
                            onClick={() => {
                              // Limpar o formulário
                              setFormData({
                                nomeCompleto: "",
                                documentoIdentificacao: "Cartão de Cidadão",
                                numeroCC: "",
                                validadeCC: "",
                                nif: "",
                                nacionalidade: "",
                                dataNascimento: "",
                                telefone: "",
                                morada: "",
                                codigoPostal: "",
                                freguesia: "",
                                estadoCivil: "",
                                profissao: "",
                                escolaridade: "",
                                nomeMae: "",
                                nomePai: "",
                                dataOcorrencia: currentDate,
                                horaOcorrencia: currentTime,
                                localOcorrencia: "",
                                observacoes: ""
                              });

                              // Fechar o diálogo
                              document.querySelector('[data-state="open"]')?.dispatchEvent(
                                new KeyboardEvent('keydown', {
                                  key: 'Escape',
                                  bubbles: true
                                })
                              );
                            }}
                          >
                            Limpar
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "A submeter..." : "Submeter Formulário"}
                    </Button>
                  </div>
              </form>
            </TabsContent>

            <TabsContent value="list" className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                <div className="hidden sm:block">
                  <h2 className="text-xl font-semibold">Lista de Identificações</h2>
                  <p className="text-muted-foreground mt-1">
                    Visualize e gerencie as identificações registadas no sistema
                  </p>
                </div>
                <div className="w-full sm:w-72">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Pesquisar por nome ou documento..."
                      className="w-full h-10 pl-3 pr-10 rounded-md border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg
                        className="w-4 h-4 text-muted-foreground"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        ></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
                </div>
              ) : (
                <IdentificacoesTable
                  identificacoes={filteredIdentificacoes}
                  onRefresh={loadIdentificacoes}
                />
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
