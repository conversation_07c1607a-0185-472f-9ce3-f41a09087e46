<!DOCTYPE html>
<html lang="pt">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reiniciando Aplicação - EPVS</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f9fafb;
      color: #111827;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    
    .container {
      max-width: 600px;
      padding: 30px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      color: #2563eb;
      margin-bottom: 20px;
    }
    
    p {
      margin-bottom: 15px;
      line-height: 1.5;
    }
    
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #2563eb;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .progress {
      margin-top: 20px;
      width: 100%;
      background-color: #e5e7eb;
      border-radius: 4px;
      height: 8px;
      overflow: hidden;
    }
    
    .progress-bar {
      height: 100%;
      background-color: #2563eb;
      width: 0%;
      transition: width 0.5s ease;
      animation: progress 5s linear forwards;
    }
    
    @keyframes progress {
      0% { width: 0%; }
      100% { width: 100%; }
    }
    
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #111827;
        color: #f9fafb;
      }
      
      .container {
        background-color: #1f2937;
      }
      
      h1 {
        color: #3b82f6;
      }
      
      .progress {
        background-color: #374151;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Reiniciando Aplicação</h1>
    <p>A aplicação está sendo reiniciada e todos os dados temporários estão sendo limpos.</p>
    <p>Por favor, aguarde enquanto este processo é concluído.</p>
    
    <div class="spinner"></div>
    
    <div class="progress">
      <div class="progress-bar"></div>
    </div>
    
    <p id="status">Limpando dados temporários...</p>
  </div>
  
  <script src="/reset-auth.js"></script>
  <script>
    // Atualizar mensagem de status a cada segundo
    const statusMessages = [
      "Limpando dados temporários...",
      "Removendo caches...",
      "Desregistrando Service Workers...",
      "Limpando dados de autenticação...",
      "Preparando redirecionamento..."
    ];
    
    let index = 0;
    const statusElement = document.getElementById('status');
    
    setInterval(() => {
      if (index < statusMessages.length) {
        statusElement.textContent = statusMessages[index];
        index++;
      }
    }, 1000);
  </script>
</body>
</html>
