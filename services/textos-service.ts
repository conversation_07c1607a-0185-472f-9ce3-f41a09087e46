import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe
} from "firebase/firestore";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

export interface Texto {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

const COLLECTION_NAME = "textos";

// Adicionar um novo texto
export async function addTexto(textoData: Omit<Texto, "id" | "createdAt" | "updatedAt">) {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...textoData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de criação
    if (textoData.createdBy) {
      const userInfo = await getUser(textoData.createdBy);
      await logCreationActivity(
        ActivityType.TEXTO_CREATED,
        "Novo Texto",
        `Criou um novo texto: ${textoData.title}`,
        textoData.createdBy,
        userInfo?.teamId,
        {
          title: textoData.title,
          content: textoData.content?.substring(0, 100) + (textoData.content?.length > 100 ? '...' : '')
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar texto:", error);
    throw error;
  }
}

// Obter todos os textos
export async function getTextos(): Promise<Texto[]> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as Texto));
  } catch (error) {
    console.error("Erro ao obter textos:", error);
    throw error;
  }
}

// Atualizar um texto existente
export async function updateTexto(id: string, data: Partial<Texto>, userId?: string) {
  try {
    const textoRef = doc(db, COLLECTION_NAME, id);

    // Obter dados atuais do texto para registar a atividade
    const textoSnap = await getDoc(textoRef);
    const textoData = textoSnap.exists() ? textoSnap.data() : null;

    await updateDoc(textoRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de atualização
    if (userId && textoData) {
      const userInfo = await getUser(userId);
      await logUpdateActivity(
        ActivityType.TEXTO_UPDATED,
        "Texto Atualizado",
        `Atualizou o texto: ${textoData.title}`,
        userId,
        userInfo?.teamId,
        {
          title: textoData.title,
          content: textoData.content?.substring(0, 100) + (textoData.content?.length > 100 ? '...' : '')
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao atualizar texto:", error);
    throw error;
  }
}

// Excluir um texto
export async function deleteTexto(id: string, userId: string) {
  try {
    // Obter dados do texto antes de excluir para registar a atividade
    const textoRef = doc(db, COLLECTION_NAME, id);
    const textoSnap = await getDoc(textoRef);
    const textoData = textoSnap.exists() ? textoSnap.data() : null;

    await deleteDoc(doc(db, COLLECTION_NAME, id));

    // Registar atividade de eliminação
    if (textoData) {
      const userInfo = await getUser(userId);
      await logDeletionActivity(
        ActivityType.TEXTO_DELETED,
        "Texto Eliminado",
        `Eliminou o texto: ${textoData.title}`,
        userId,
        userInfo?.teamId,
        {
          title: textoData.title,
          content: textoData.content?.substring(0, 100) + (textoData.content?.length > 100 ? '...' : '')
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao excluir texto:", error);
    throw error;
  }
}

// Escutar mudanças em textos em tempo real
export function listenToTextos(callback: (textos: Texto[]) => void): Unsubscribe {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("createdAt", "desc")
    );

    return onSnapshot(q, (querySnapshot) => {
      const textos = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as Texto));

      callback(textos);
    }, (error) => {
      console.error("Erro ao escutar textos:", error);
    });
  } catch (error) {
    console.error("Erro ao configurar listener de textos:", error);
    throw error;
  }
}
