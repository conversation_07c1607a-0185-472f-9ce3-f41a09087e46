/**
 * Script para gerenciar o cache do Service Worker e preservar a autenticação
 * Este script é carregado em todas as páginas e garante que o cache não cause problemas
 * enquanto mantém o usuário autenticado até que ele faça logout explicitamente
 */
(function() {
  console.log('Iniciando gerenciamento de cache...');

  // Função para gerenciar o cache do Service Worker
  const manageCache = async () => {
    try {
      // Verificar se estamos na página de login
      const isLoginPage = window.location.pathname.includes('/login');

      // Verificar se o usuário está autenticado
      const isAuthenticated = localStorage.getItem('authUser') !== null;

      // Gerenciar Service Workers apenas se necessário
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();

        // Se estamos na página de login e não estamos autenticados, limpar tudo
        if (isLoginPage && !isAuthenticated) {
          console.log('Página de login e usuário não autenticado: limpando Service Workers');

          for (const registration of registrations) {
            await registration.unregister();
            console.log('Service Worker desregistrado');
          }

          // Limpar apenas caches específicos, não todos
          if ('caches' in window) {
            const cacheNames = await caches.keys();
            const appCaches = cacheNames.filter(name =>
              name.includes('next-data') ||
              name.includes('workbox') ||
              name.includes('webpack')
            );

            await Promise.all(
              appCaches.map(cacheName => {
                console.log('Limpando cache específico:', cacheName);
                return caches.delete(cacheName);
              })
            );
            console.log('Caches específicos foram limpos');
          }
        } else {
          console.log('Mantendo Service Workers e caches para preservar a autenticação');
        }
      }

      // Limpar apenas dados de navegação específicos, não todos
      if (window.sessionStorage) {
        // Limpar apenas dados de navegação, não de autenticação
        sessionStorage.removeItem('lastCheckedPath');
        console.log('Dados de navegação específicos limpos');
      }

      console.log('Gerenciamento de cache concluído');
    } catch (error) {
      console.error('Erro ao gerenciar cache:', error);
    }
  };

  // Executar gerenciamento quando a página carregar
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      // Pequeno atraso para não interferir com o carregamento da página
      setTimeout(manageCache, 1000);
    });
  } else {
    // Pequeno atraso para não interferir com o carregamento da página
    setTimeout(manageCache, 1000);
  }

  // Armazenar a última rota visitada para ajudar no diagnóstico de problemas
  window.addEventListener('beforeunload', () => {
    if (typeof sessionStorage !== 'undefined') {
      sessionStorage.setItem('lastRoute', window.location.pathname);
    }
  });
})();
