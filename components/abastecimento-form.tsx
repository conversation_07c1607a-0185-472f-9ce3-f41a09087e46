"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useAuth } from "@/contexts/auth-context";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Condutor, Abastecimento } from "@/services/condutores-service";

// Schema para validação do formulário
const abastecimentoSchema = z.object({
  quilometros: z.coerce.number().min(0, { message: "Quilómetros devem ser um número positivo" }),
  litros: z.coerce.number().min(0.1, { message: "Quantidade de litros deve ser um número positivo" }),
  precoLitro: z.coerce.number().min(0.1, { message: "Preço por litro deve ser um número positivo" }),
  precoTotal: z.coerce.number().min(0.1, { message: "Preço total deve ser um número positivo" }),
});

type AbastecimentoFormValues = z.infer<typeof abastecimentoSchema>;

interface AbastecimentoFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  condutor: Condutor | null;
  onSave: (id: string, abastecimento: Abastecimento) => Promise<void>;
}

export function AbastecimentoForm({
  open,
  onOpenChange,
  condutor,
  onSave
}: AbastecimentoFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { userProfile } = useAuth();

  // Inicializar o formulário com react-hook-form
  const form = useForm<AbastecimentoFormValues>({
    resolver: zodResolver(abastecimentoSchema),
    defaultValues: {
      quilometros: condutor?.quilometrosIniciais || 0,
      litros: 0,
      precoLitro: 0,
      precoTotal: 0,
    },
  });

  // Resetar o formulário quando o modal é aberto
  useEffect(() => {
    if (open && condutor) {
      // Se já existe um abastecimento, preencher com os dados existentes
      if (condutor.abastecimento) {
        form.reset({
          quilometros: condutor.abastecimento.quilometros,
          litros: condutor.abastecimento.litros,
          precoLitro: condutor.abastecimento.precoLitro,
          precoTotal: condutor.abastecimento.precoTotal,
        });
      } else {
        // Caso contrário, inicializar com valores padrão
        form.reset({
          quilometros: condutor.quilometrosIniciais || 0,
          litros: 0,
          precoLitro: 0,
          precoTotal: 0,
        });
      }
    }
  }, [open, form, condutor]);

  // Calcular o preço total quando a quantidade de litros ou o preço por litro mudar
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if ((name === 'litros' || name === 'precoLitro') && value.litros && value.precoLitro) {
        const litros = parseFloat(value.litros.toString());
        const precoLitro = parseFloat(value.precoLitro.toString());
        if (!isNaN(litros) && !isNaN(precoLitro)) {
          const total = litros * precoLitro;
          form.setValue('precoTotal', parseFloat(total.toFixed(2)));
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Função para lidar com a submissão do formulário
  async function onSubmit(data: AbastecimentoFormValues) {
    if (!condutor) return;

    const loadingToast = toast.loading("A guardar abastecimento...");
    setIsSubmitting(true);

    try {
      // Preparar dados de abastecimento
      const abastecimento: Abastecimento = {
        quilometros: data.quilometros,
        litros: data.litros,
        precoLitro: data.precoLitro,
        precoTotal: data.precoTotal,
      };

      // Salvar o abastecimento
      await onSave(condutor.id, abastecimento);

      toast.dismiss(loadingToast);
      // Notificação removida para evitar duplicação, pois o componente pai já exibe uma notificação

      // Fechar o diálogo
      onOpenChange(false);
    } catch (error: any) {
      toast.dismiss(loadingToast);
      toast.error("Erro ao guardar abastecimento");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Registar Abastecimento</DialogTitle>
          <DialogDescription>
            Preencha os dados do abastecimento de combustível.
          </DialogDescription>
        </DialogHeader>

        {condutor && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
              <FormField
                control={form.control}
                name="quilometros"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Quilómetros do Abastecimento</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4 w-full">
                <FormField
                  control={form.control}
                  name="precoLitro"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Preço por Litro (€)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0.00"
                          step="0.01"
                          {...field}
                          disabled={isSubmitting}
                          className="w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="litros"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Quantidade (L)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0"
                          step="0.01"
                          {...field}
                          disabled={isSubmitting}
                          className="w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 w-full">
                <FormField
                  control={form.control}
                  name="precoTotal"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Preço Total (€)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0.00"
                          step="0.01"
                          {...field}
                          disabled={isSubmitting}
                          className="w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter className="mt-6">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => onOpenChange(false)}
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "A guardar..." : "Guardar Abastecimento"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
