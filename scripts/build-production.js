#!/usr/bin/env node

/**
 * Production Build Script
 * Ensures the application is ready for production deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting production build process...\n');

// Check if required environment files exist
function checkEnvironmentFiles() {
  console.log('📋 Checking environment configuration...');
  
  const envFiles = ['.env.local', '.env.production'];
  const missingFiles = envFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    console.warn(`⚠️  Warning: Missing environment files: ${missingFiles.join(', ')}`);
    console.warn('   Make sure to configure your environment variables before deployment.');
  } else {
    console.log('✅ Environment files found');
  }
}

// Clean previous build
function cleanBuild() {
  console.log('\n🧹 Cleaning previous build...');
  try {
    if (fs.existsSync('.next')) {
      execSync('rm -rf .next', { stdio: 'inherit' });
    }
    console.log('✅ Build directory cleaned');
  } catch (error) {
    console.error('❌ Error cleaning build directory:', error.message);
    process.exit(1);
  }
}

// Install dependencies
function installDependencies() {
  console.log('\n📦 Installing dependencies...');
  try {
    execSync('npm ci', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
  } catch (error) {
    console.error('❌ Error installing dependencies:', error.message);
    process.exit(1);
  }
}

// Build the application
function buildApplication() {
  console.log('\n🔨 Building application...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

// Verify build output
function verifyBuild() {
  console.log('\n🔍 Verifying build output...');
  
  const buildDir = '.next';
  const staticDir = path.join(buildDir, 'static');
  
  if (!fs.existsSync(buildDir)) {
    console.error('❌ Build directory not found');
    process.exit(1);
  }
  
  if (!fs.existsSync(staticDir)) {
    console.error('❌ Static assets directory not found');
    process.exit(1);
  }
  
  console.log('✅ Build output verified');
}

// Main execution
function main() {
  try {
    checkEnvironmentFiles();
    cleanBuild();
    installDependencies();
    buildApplication();
    verifyBuild();
    
    console.log('\n🎉 Production build completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Set up your production environment variables');
    console.log('   2. Deploy using your preferred platform (Vercel, Firebase, etc.)');
    console.log('   3. Test the production deployment thoroughly');
    console.log('\n📖 See PRODUCTION_DEPLOYMENT.md for detailed deployment instructions');
    
  } catch (error) {
    console.error('\n❌ Production build failed:', error.message);
    process.exit(1);
  }
}

main();
