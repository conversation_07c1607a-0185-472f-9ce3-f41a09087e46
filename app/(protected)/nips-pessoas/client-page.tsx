"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { NIPsPessoasTable } from "@/components/nips-pessoas-table";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { IconPlus } from "@tabler/icons-react";
import { SearchBar } from "@/components/search-bar";
import { NIPPessoaForm } from "@/components/nip-pessoa-form";
import {
  addNIPPessoa,
  getNIPsPessoas,
  updateNIPPessoa,
  deleteNIPPessoa,
  listenToNIPsPessoas,
  NIPPessoa
} from "@/services/nips-pessoas-service";

export default function ClientPage() {
  const { user, loading, profileLoading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [nipsPessoas, setNIPsPessoas] = useState<NIPPessoa[]>([]);
  const [filteredNIPsPessoas, setFilteredNIPsPessoas] = useState<NIPPessoa[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Carregar NIPs Pessoas do Firestore
  const loadNIPsPessoas = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const data = await getNIPsPessoas();
      setNIPsPessoas(data);
      setFilteredNIPsPessoas(data);
    } catch (error) {
      console.error("Erro ao carregar NIPs Pessoas:", error);
    } finally {
      setIsLoading(false);
    }
  }, [user, setNIPsPessoas, setFilteredNIPsPessoas, setIsLoading]);

  // Configurar listener em tempo real para NIPs Pessoas
  useEffect(() => {
    // Se o usuário não estiver autenticado e não estiver carregando, redirecionar para o login
    if (!loading && !user) {
      router.push("/login");
    } else if (!loading && !profileLoading && user) {
      // Inicialmente, carregar NIPs Pessoas normalmente para mostrar algo rapidamente
      loadNIPsPessoas();

      // Configurar listener em tempo real para atualizações
      const unsubscribe = listenToNIPsPessoas((updatedNIPsPessoas) => {
        setNIPsPessoas(updatedNIPsPessoas);
        setFilteredNIPsPessoas(updatedNIPsPessoas);
        setIsLoading(false);
      });

      // Limpar listener quando o componente for desmontado
      return () => {
        unsubscribe();
      };
    }
  }, [user, loading, profileLoading, router, loadNIPsPessoas]);

  // Efeito para filtrar NIPs Pessoas com base na pesquisa
  useEffect(() => {
    if (!nipsPessoas.length) return;

    if (!searchQuery.trim()) {
      setFilteredNIPsPessoas(nipsPessoas);
      return;
    }

    const query = searchQuery.toLowerCase().trim();
    const filtered = nipsPessoas.filter(nipPessoa =>
      nipPessoa.nomeCompleto.toLowerCase().includes(query) ||
      nipPessoa.numero.toLowerCase().includes(query)
    );

    setFilteredNIPsPessoas(filtered);
  }, [searchQuery, nipsPessoas]);

  // Função para lidar com a pesquisa
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Manipular o salvamento de um NIP Pessoa
  const handleSaveComplete = async (nipData: {
    nomeCompleto: string;
    numero: string;
    observacoes?: string;
    createdBy: string;
  }) => {
    try {
      // Adicionar o NIP Pessoa ao Firestore
      const id = await addNIPPessoa(nipData);

      // Se a pesquisa estiver vazia, recarregar todos os NIPs Pessoas
      // Caso contrário, apenas adicionar o novo NIP Pessoa se ele corresponder à pesquisa
      if (!searchQuery.trim()) {
        loadNIPsPessoas();
      } else {
        // Verificar se o novo NIP Pessoa corresponde à pesquisa atual
        const query = searchQuery.toLowerCase().trim();
        if (
          nipData.nomeCompleto.toLowerCase().includes(query) ||
          nipData.numero.toLowerCase().includes(query)
        ) {
          // Criar um novo NIP Pessoa com o ID retornado
          const newNIPPessoa = { id, ...nipData } as NIPPessoa;

          // Adicionar o novo NIP Pessoa às listas
          setNIPsPessoas(prev => [newNIPPessoa, ...prev]);
          setFilteredNIPsPessoas(prev => [newNIPPessoa, ...prev]);
        } else {
          // Apenas atualizar a lista completa sem afetar a lista filtrada
          const data = await getNIPsPessoas();
          setNIPsPessoas(data);
        }
      }

      // Fechar o modal
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Erro ao salvar NIP Pessoa:", error);
    }
  };

  // Manipular a exclusão de um NIP Pessoa
  const handleDelete = async (id: string) => {
    if (!user?.uid) {
      console.error("Usuário não autenticado");
      throw new Error("Usuário não autenticado");
    }

    try {
      await deleteNIPPessoa(id, user.uid);
      // Atualizar a lista local removendo o NIP Pessoa excluído
      const updatedNIPsPessoas = nipsPessoas.filter(n => n.id !== id);
      setNIPsPessoas(updatedNIPsPessoas);
      // Atualizar também a lista filtrada
      setFilteredNIPsPessoas(prev => prev.filter(n => n.id !== id));
    } catch (error) {
      console.error("Erro ao excluir NIP Pessoa:", error);
      throw error;
    }
  };

  // Manipular a edição de um NIP Pessoa
  const handleEdit = async (id: string, newNomeCompleto: string, newNumero: string, newObservacoes?: string) => {
    try {
      await updateNIPPessoa(id, { nomeCompleto: newNomeCompleto, numero: newNumero, observacoes: newObservacoes }, user?.uid);
      // Atualizar a lista local com os novos dados
      const updatedNIPsPessoas = nipsPessoas.map(n =>
        n.id === id ? { ...n, nomeCompleto: newNomeCompleto, numero: newNumero, observacoes: newObservacoes } : n
      );
      setNIPsPessoas(updatedNIPsPessoas);

      // Atualizar também a lista filtrada
      setFilteredNIPsPessoas(prev =>
        prev.map(n => n.id === id ? { ...n, nomeCompleto: newNomeCompleto, numero: newNumero, observacoes: newObservacoes } : n)
      );
    } catch (error) {
      console.error("Erro ao atualizar NIP Pessoa:", error);
      throw error;
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">NIPs Pessoas</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie os NIPs de pessoas disponíveis no sistema
              </p>
            </div>
            <Button onClick={() => setIsAddModalOpen(true)} className="shrink-0">
              <IconPlus className="size-4 mr-2" />
              Adicionar NIP Pessoa
            </Button>
          </div>

          {/* Barra de pesquisa */}
          <div className="mb-6">
            <SearchBar
              onSearch={handleSearch}
              placeholder="Pesquisar por nome ou número..."
              className="max-w-full sm:max-w-md"
            />
          </div>

          {/* Seção de listagem */}
          <div className="mt-2">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              </div>
            ) : (
              <NIPsPessoasTable
                nipsPessoas={filteredNIPsPessoas}
                onDelete={handleDelete}
                onEdit={handleEdit}
              />
            )}
          </div>

          {/* Modal de Adição */}
          <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
            <DialogContent className="sm:max-w-xl md:max-w-2xl w-[95vw]">
              <DialogHeader>
                <DialogTitle>Adicionar Novo NIP Pessoa</DialogTitle>
              </DialogHeader>
              <NIPPessoaForm onSaveComplete={handleSaveComplete} />
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
