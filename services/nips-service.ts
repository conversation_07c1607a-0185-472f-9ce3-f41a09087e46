import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe
} from "firebase/firestore";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

export interface NIP {
  id: string;
  local: string;
  numero: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

const COLLECTION_NAME = "nips";

// Adicionar um novo NIP
export async function addNIP(nipData: Omit<NIP, "id" | "createdAt" | "updatedAt">): Promise<string> {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...nipData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de criação
    if (nipData.createdBy) {
      const userInfo = await getUser(nipData.createdBy);
      await logCreationActivity(
        ActivityType.NIP_CREATED,
        "Novo NIP",
        `Criou um novo NIP: ${nipData.numero} - ${nipData.local}`,
        nipData.createdBy,
        userInfo?.teamId,
        {
          numero: nipData.numero,
          local: nipData.local
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar NIP:", error);
    throw error;
  }
}

// Obter todos os NIPs
export async function getNIPs(): Promise<NIP[]> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as NIP));
  } catch (error) {
    console.error("Erro ao obter NIPs:", error);
    throw error;
  }
}

// Atualizar um NIP existente
export async function updateNIP(id: string, data: Partial<NIP>, userId?: string): Promise<boolean> {
  try {
    const nipRef = doc(db, COLLECTION_NAME, id);

    // Obter dados atuais do NIP para registar a atividade
    const nipSnap = await getDoc(nipRef);
    const nipData = nipSnap.exists() ? nipSnap.data() : null;

    await updateDoc(nipRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de atualização
    if (userId && nipData) {
      const userInfo = await getUser(userId);
      await logUpdateActivity(
        ActivityType.NIP_UPDATED,
        "NIP Atualizado",
        `Atualizou o NIP: ${nipData.numero} - ${nipData.local}`,
        userId,
        userInfo?.teamId,
        {
          numero: nipData.numero,
          local: nipData.local
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao atualizar NIP:", error);
    throw error;
  }
}

// Excluir um NIP
export async function deleteNIP(id: string, userId: string): Promise<boolean> {
  try {
    // Obter dados do NIP antes de excluir para registar a atividade
    const nipRef = doc(db, COLLECTION_NAME, id);
    const nipSnap = await getDoc(nipRef);
    const nipData = nipSnap.exists() ? nipSnap.data() : null;

    await deleteDoc(doc(db, COLLECTION_NAME, id));

    // Registar atividade de eliminação
    if (nipData) {
      const userInfo = await getUser(userId);
      await logDeletionActivity(
        ActivityType.NIP_DELETED,
        "NIP Eliminado",
        `Eliminou o NIP: ${nipData.numero} - ${nipData.local}`,
        userId,
        userInfo?.teamId,
        {
          numero: nipData.numero,
          local: nipData.local
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao excluir NIP:", error);
    throw error;
  }
}

// Escutar mudanças em NIPs em tempo real
export function listenToNIPs(callback: (nips: NIP[]) => void): Unsubscribe {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("createdAt", "desc")
    );

    return onSnapshot(q, (querySnapshot) => {
      const nips = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as NIP));

      callback(nips);
    }, (error) => {
      console.error("Erro ao escutar NIPs:", error);
    });
  } catch (error) {
    console.error("Erro ao configurar listener de NIPs:", error);
    throw error;
  }
}
