"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/auth-context";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Condutor, Abastecimento } from "@/services/condutores-service";

// Schema para validação do formulário
const finalizarSchema = z.object({
  quilometrosFinais: z.coerce
    .number()
    .min(0, { message: "Quilómetros finais devem ser um número positivo" }),
  dataHoraFim: z.string().min(1, { message: "Data e hora de fim são obrigatórias" }),
});

type FinalizarFormValues = z.infer<typeof finalizarSchema>;

interface FinalizarServicoFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  condutor: Condutor | null;
  onFinalize: (
    id: string,
    quilometrosFinais: number,
    dataHoraFim: Date,
    abastecimento: Abastecimento | null
  ) => Promise<void>;
}

export function FinalizarServicoForm({
  open,
  onOpenChange,
  condutor,
  onFinalize
}: FinalizarServicoFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { userProfile } = useAuth();

  // Função para formatar data e hora para o formato de input datetime-local
  function formatDateTimeForInput(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  // Inicializar o formulário com react-hook-form
  const form = useForm<FinalizarFormValues>({
    resolver: zodResolver(finalizarSchema),
    defaultValues: {
      quilometrosFinais: (condutor?.quilometrosIniciais || 0) + 1, // Valor mínimo válido
      dataHoraFim: formatDateTimeForInput(new Date()),
    },
    context: {
      quilometrosIniciais: condutor?.quilometrosIniciais || 0,
    },
  });

  // Atualizar o formulário quando o modal é aberto
  useEffect(() => {
    if (open && condutor) {
      form.clearErrors();

      // Se já existem valores finais, usar esses valores
      if (condutor.quilometrosFinais && condutor.dataHoraFim) {
        form.reset({
          quilometrosFinais: condutor.quilometrosFinais,
          dataHoraFim: formatDateTimeForInput(condutor.dataHoraFim),
        });
      } else {
        // Caso contrário, usar valores padrão (quilometrosIniciais + 1 para garantir validação)
        form.reset({
          quilometrosFinais: (condutor.quilometrosIniciais || 0) + 1,
          dataHoraFim: formatDateTimeForInput(new Date()),
        });
      }
    }
  }, [open, condutor, form]);

  // Função para lidar com a submissão do formulário
  async function onSubmit(data: FinalizarFormValues) {
    if (!condutor) return;

    const loadingToast = toast.loading("A finalizar serviço...");
    setIsSubmitting(true);

    try {
      // Validar que os quilômetros finais são maiores que os iniciais
      if (data.quilometrosFinais <= condutor.quilometrosIniciais) {
        toast.dismiss(loadingToast);
        toast.error("Erro de validação", {
          description: `Os quilómetros finais (${data.quilometrosFinais} km) devem ser maiores que os iniciais (${condutor.quilometrosIniciais} km).`,
        });
        setIsSubmitting(false);
        return;
      }

      // Não incluir abastecimento no formulário de finalização
      const abastecimento: Abastecimento | null = null;

      // Finalizar o serviço
      await onFinalize(
        condutor.id,
        data.quilometrosFinais,
        new Date(data.dataHoraFim),
        abastecimento
      );

      toast.dismiss(loadingToast);
      // Notificação removida para evitar duplicação, pois o componente pai já exibe uma notificação

      // Fechar o diálogo
      onOpenChange(false);
    } catch (error: any) {
      toast.dismiss(loadingToast);
      toast.error("Erro ao finalizar serviço");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Finalizar Serviço</DialogTitle>
          <DialogDescription>
            Preencha os dados para finalizar o serviço do condutor.
          </DialogDescription>
        </DialogHeader>

        {condutor && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
              <FormField
                control={form.control}
                name="quilometrosFinais"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Quilómetros Finais</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dataHoraFim"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Data e Hora de Fim</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        {...field}
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter className="mt-6">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => onOpenChange(false)}
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "A finalizar..." : "Finalizar Serviço"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
