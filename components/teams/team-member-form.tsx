"use client";

import { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface User {
  id: string;
  name: string;
  registrationNumber?: string;
}

interface TeamMemberFormProps {
  users: User[];
  onSave: (userId: string) => void;
}

export function TeamMemberForm({ users, onSave }: TeamMemberFormProps) {
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedUserId) {
      toast.error("Selecione um utilizador");
      return;
    }

    setIsSubmitting(true);

    try {
      onSave(selectedUserId);
    } catch (error) {
      console.error("Erro ao adicionar utilizador:", error);
      toast.error("Erro ao adicionar utilizador", {
        description: "Não foi possível adicionar o utilizador à equipa.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="userId">Utilizador</Label>
        <Select
          value={selectedUserId}
          onValueChange={setSelectedUserId}
        >
          <SelectTrigger id="userId">
            <SelectValue placeholder="Selecione um utilizador" />
          </SelectTrigger>
          <SelectContent>
            {users.length === 0 ? (
              <SelectItem value="none" disabled>
                Nenhum utilizador disponível
              </SelectItem>
            ) : (
              users.map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  <div className="flex justify-between w-full">
                    <span>{user.name}</span>
                    {user.registrationNumber && <span className="text-muted-foreground ml-2">{user.registrationNumber}</span>}
                  </div>
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button type="submit" disabled={isSubmitting || users.length === 0}>
          {isSubmitting ? "Adicionando..." : "Adicionar"}
        </Button>
      </div>
    </form>
  );
}
