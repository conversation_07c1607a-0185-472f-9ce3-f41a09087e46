#!/bin/bash

# <PERSON><PERSON>t to deploy Firestore indexes
echo "Deploying Firestore indexes..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Deploy the indexes
firebase deploy --only firestore:indexes

echo "Indexes deployment completed!"
echo "Note: It may take several minutes for the indexes to build."
echo "You can check the status at: https://console.firebase.google.com/v1/r/project/woomanagerapp-77606/firestore/indexes"
