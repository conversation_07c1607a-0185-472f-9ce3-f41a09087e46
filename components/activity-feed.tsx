"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  IconFileText,
  IconUpload,
  IconUserShield,
  IconUserPlus,
  IconUsers,
  IconShield,
  IconBell,
  IconClock,
  IconTrash
} from "@tabler/icons-react";
import {
  Activity,
  ActivityType,
  getRecentActivities,
  listenToActivities,
  formatActivityTimestamp,
  getActivityIcon,
  getActivityColor
} from "@/services/activity-service";

interface ActivityFeedProps {
  className?: string;
}

export function ActivityFeed({ className }: ActivityFeedProps) {
  const { isAdmin, userTeamId, loading: authLoading } = useAuth();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mapeamento de ícones
  const iconMap = {
    IconFileText,
    IconUpload,
    IconUserShield,
    IconUserPlus,
    IconUsers,
    IconShield,
    IconBell,
    IconTrash
  };

  // Carregar atividades iniciais
  useEffect(() => {
    // Não carregar se ainda estiver autenticando
    if (authLoading) return;

    const loadActivities = async () => {
      try {
        setLoading(true);
        setError(null);
        const recentActivities = await getRecentActivities(isAdmin, userTeamId, 15);
        setActivities(recentActivities);
      } catch (error) {
        console.error("Erro ao carregar atividades:", error);
        setError("Erro ao carregar atividades. Tente novamente.");
      } finally {
        setLoading(false);
      }
    };

    loadActivities();
  }, [isAdmin, userTeamId, authLoading]);

  // Escutar atualizações em tempo real
  useEffect(() => {
    const unsubscribe = listenToActivities(
      (newActivities) => {
        setActivities(newActivities);
      },
      isAdmin,
      userTeamId,
      15
    );

    return unsubscribe;
  }, [isAdmin, userTeamId]);



  // Obter iniciais do nome do usuário
  const getUserInitials = (name: string | undefined): string => {
    try {
      if (!name || typeof name !== 'string' || name.trim() === "") {
        return "??";
      }
      return name
        .trim()
        .split(" ")
        .filter(word => word.length > 0)
        .map(word => word.charAt(0))
        .join("")
        .toUpperCase()
        .slice(0, 2) || "??";
    } catch (error) {
      console.error("Error getting user initials:", error, "for name:", name);
      return "??";
    }
  };

  // Renderizar ícone da atividade
  const renderActivityIcon = (type: ActivityType) => {
    const iconName = getActivityIcon(type);
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || IconBell;
    const colorClass = getActivityColor(type);

    return (
      <div className={`flex h-8 w-8 items-center justify-center rounded-full bg-muted ${colorClass}`}>
        <IconComponent className="h-4 w-4" />
      </div>
    );
  };

  // Renderizar badge do tipo de atividade
  const renderActivityBadge = (type: ActivityType) => {
    const badgeText = {
      // CREATE activities
      [ActivityType.REGISTO_CREATED]: "Registo",
      [ActivityType.FORMULARIO_UPLOADED]: "Formulário",
      [ActivityType.CONDUTOR_CREATED]: "Condutor",
      [ActivityType.IDENTIFICACAO_CREATED]: "Identificação",
      [ActivityType.CONTACTO_CREATED]: "Contacto",
      [ActivityType.NIP_CREATED]: "NIP",
      [ActivityType.NIP_PESSOA_CREATED]: "NIP Pessoa",
      [ActivityType.TEXTO_CREATED]: "Texto",
      [ActivityType.VIATURA_CREATED]: "Viatura",
      [ActivityType.ESTABELECIMENTO_CREATED]: "Estabelecimento",
      [ActivityType.USER_REGISTERED]: "Utilizador",
      [ActivityType.TEAM_CREATED]: "Equipa",

      // UPDATE activities
      [ActivityType.REGISTO_UPDATED]: "Atualização",
      [ActivityType.FORMULARIO_UPDATED]: "Atualização",
      [ActivityType.CONDUTOR_UPDATED]: "Atualização",
      [ActivityType.IDENTIFICACAO_UPDATED]: "Atualização",
      [ActivityType.CONTACTO_UPDATED]: "Atualização",
      [ActivityType.NIP_UPDATED]: "Atualização",
      [ActivityType.NIP_PESSOA_UPDATED]: "Atualização",
      [ActivityType.TEXTO_UPDATED]: "Atualização",
      [ActivityType.VIATURA_UPDATED]: "Atualização",
      [ActivityType.ESTABELECIMENTO_UPDATED]: "Atualização",
      [ActivityType.USER_ROLE_CHANGED]: "Papel",

      // DELETE activities
      [ActivityType.REGISTO_DELETED]: "Eliminação",
      [ActivityType.FORMULARIO_DELETED]: "Eliminação",
      [ActivityType.CONDUTOR_DELETED]: "Eliminação",
      [ActivityType.IDENTIFICACAO_DELETED]: "Eliminação",
      [ActivityType.CONTACTO_DELETED]: "Eliminação",
      [ActivityType.NIP_DELETED]: "Eliminação",
      [ActivityType.NIP_PESSOA_DELETED]: "Eliminação",
      [ActivityType.TEXTO_DELETED]: "Eliminação",
      [ActivityType.VIATURA_DELETED]: "Eliminação",
      [ActivityType.ESTABELECIMENTO_DELETED]: "Eliminação"
    };

    const badgeVariant = {
      // CREATE activities
      [ActivityType.REGISTO_CREATED]: "default",
      [ActivityType.FORMULARIO_UPLOADED]: "secondary",
      [ActivityType.CONDUTOR_CREATED]: "outline",
      [ActivityType.IDENTIFICACAO_CREATED]: "outline",
      [ActivityType.CONTACTO_CREATED]: "outline",
      [ActivityType.NIP_CREATED]: "outline",
      [ActivityType.NIP_PESSOA_CREATED]: "outline",
      [ActivityType.TEXTO_CREATED]: "outline",
      [ActivityType.VIATURA_CREATED]: "outline",
      [ActivityType.ESTABELECIMENTO_CREATED]: "outline",
      [ActivityType.USER_REGISTERED]: "default",
      [ActivityType.TEAM_CREATED]: "secondary",

      // UPDATE activities
      [ActivityType.REGISTO_UPDATED]: "secondary",
      [ActivityType.FORMULARIO_UPDATED]: "secondary",
      [ActivityType.CONDUTOR_UPDATED]: "secondary",
      [ActivityType.IDENTIFICACAO_UPDATED]: "secondary",
      [ActivityType.CONTACTO_UPDATED]: "secondary",
      [ActivityType.NIP_UPDATED]: "secondary",
      [ActivityType.NIP_PESSOA_UPDATED]: "secondary",
      [ActivityType.TEXTO_UPDATED]: "secondary",
      [ActivityType.VIATURA_UPDATED]: "secondary",
      [ActivityType.ESTABELECIMENTO_UPDATED]: "secondary",
      [ActivityType.USER_ROLE_CHANGED]: "outline",

      // DELETE activities
      [ActivityType.REGISTO_DELETED]: "destructive",
      [ActivityType.FORMULARIO_DELETED]: "destructive",
      [ActivityType.CONDUTOR_DELETED]: "destructive",
      [ActivityType.IDENTIFICACAO_DELETED]: "destructive",
      [ActivityType.CONTACTO_DELETED]: "destructive",
      [ActivityType.NIP_DELETED]: "destructive",
      [ActivityType.NIP_PESSOA_DELETED]: "destructive",
      [ActivityType.TEXTO_DELETED]: "destructive",
      [ActivityType.VIATURA_DELETED]: "destructive",
      [ActivityType.ESTABELECIMENTO_DELETED]: "destructive"
    };

    return (
      <Badge variant={badgeVariant[type] as any} className="text-xs">
        {badgeText[type]}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconBell className="h-5 w-5" />
            Atividade Recente
          </CardTitle>
          <CardDescription>
            Últimas atividades do projeto
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-start gap-3 animate-pulse">
                <div className="h-8 w-8 rounded-full bg-muted"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="space-y-1.5">
          <CardTitle className="flex items-center gap-2">
            <IconBell className="h-5 w-5" />
            Atividade Recente
          </CardTitle>
          <CardDescription>
            {isAdmin
              ? "Últimas atividades de todas as equipas"
              : "Últimas atividades da sua equipa"
            }
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        {error ? (
          <div className="text-center py-8 text-muted-foreground">
            <IconBell className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">{error}</p>
          </div>
        ) : activities.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <IconBell className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">Nenhuma atividade recente encontrada.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity) => {
              // Add safety checks for activity data
              const safeActivity = {
                ...activity,
                userName: activity.userName || "Utilizador desconhecido",
                title: activity.title || "Atividade",
                description: activity.description || "Sem descrição"
              };

              return (
                <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                  {renderActivityIcon(activity.type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium leading-none mb-1">
                          {safeActivity.title}
                        </p>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {safeActivity.description}
                        </p>
                      </div>
                      {renderActivityBadge(activity.type)}
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <Avatar className="h-5 w-5">
                        <AvatarFallback className="text-xs">
                          {getUserInitials(safeActivity.userName)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">
                        {safeActivity.userName}
                        {activity.userRegistrationNumber && (
                          <span className="ml-1">({activity.userRegistrationNumber})</span>
                        )}
                      </span>
                      <span className="text-xs text-muted-foreground">•</span>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <IconClock className="h-3 w-3" />
                        {formatActivityTimestamp(activity.timestamp)}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
