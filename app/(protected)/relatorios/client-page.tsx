"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { PageHeader } from "@/components/page-header";

import { Registo } from "@/services/registos-service";
import { Condutor } from "@/services/condutores-service";
import { getRegistos, listenToRegistos } from "@/services/registos-service";
import { getCondutores, listenToCondutores } from "@/services/condutores-service";
import { getTeams } from "@/services/teams-service";
import { Team } from "@/types/team";
import { RelatorioFilter } from "@/components/relatorio-filter";
import { RelatorioRegistosSection } from "@/components/relatorio-registos-section";
import { RelatorioCondutoresSection } from "@/components/relatorio-condutores-section";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";


// Interface para os filtros
export interface RelatorioFilterOptions {
  dataInicio: Date | null;
  dataFim: Date | null;
  teamId?: string;
}

export default function ClientPage() {
  // Estados para armazenar os dados
  const [registos, setRegistos] = useState<Registo[]>([]);
  const [condutores, setCondutores] = useState<Condutor[]>([]);
  const [filteredRegistos, setFilteredRegistos] = useState<Registo[]>([]);
  const [filteredCondutores, setFilteredCondutores] = useState<Condutor[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Obter a data atual para os filtros iniciais
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Estado para armazenar os filtros atuais
  const [currentFilters, setCurrentFilters] = useState<RelatorioFilterOptions>({
    dataInicio: today,
    dataFim: today,
    teamId: "all",
  });

  // Obter informações do usuário autenticado
  const { user, userProfile, isAdmin, loading, profileLoading } = useAuth();
  const userTeamId = userProfile?.teamId;

  // Efeito para carregar equipas (apenas para administradores)
  useEffect(() => {
    // Evitar execução durante o carregamento inicial
    if (loading || profileLoading) return;

    // Carregar equipas apenas para administradores
    if (isAdmin && user) {
      getTeams().then(teamsData => {
        setTeams(teamsData);
      }).catch(error => {
        console.error("Erro ao carregar equipas:", error);
      });
    }
  }, [isAdmin, user, loading, profileLoading]);

  // Efeito para carregar registos e condutores
  useEffect(() => {
    // Evitar execução durante o carregamento inicial
    if (loading || profileLoading) return;

    // Verificar se o usuário está autenticado
    if (!user) return;

    setIsLoading(true);

    // Função para carregar registos
    const loadRegistos = async () => {
      try {
        let registosData: Registo[] = [];

        if (!isAdmin && user) {
          if (userTeamId) {
            // Se não for admin mas tiver teamId, filtrar por equipa
            registosData = await getRegistos(userTeamId);
          } else {
            // Se não tiver teamId, filtrar por responsavelId
            registosData = await getRegistos(undefined, user.uid);
          }
        } else if (isAdmin) {
          // Admin vê todos os registos
          registosData = await getRegistos();
        }

        // Atualizar o estado com os dados carregados
        setRegistos(registosData);

        // Retornar os dados para uso posterior
        return registosData;
      } catch (error) {
        console.error("Erro ao carregar registos:", error);
        return [];
      }
    };

    // Função para carregar condutores
    const loadCondutores = async () => {
      try {
        let condutoresData: Condutor[] = [];

        if (!isAdmin && user) {
          if (userTeamId) {
            // Se não for admin mas tiver teamId, filtrar por equipa
            condutoresData = await getCondutores(userTeamId);
          } else {
            // Se não tiver teamId, filtrar por userId
            condutoresData = await getCondutores(undefined, user.uid);
          }
        } else if (isAdmin) {
          // Admin vê todos os condutores
          condutoresData = await getCondutores();
        }

        // Atualizar o estado com os dados carregados
        setCondutores(condutoresData);

        // Retornar os dados para uso posterior
        return condutoresData;
      } catch (error) {
        console.error("Erro ao carregar condutores:", error);
        return [];
      }
    };

    // Carregar dados iniciais
    Promise.all([loadRegistos(), loadCondutores()])
      .then(([registosData, condutoresData]) => {
        // Garantir que os dados iniciais sejam exibidos corretamente
        console.log(`Dados iniciais carregados: ${registosData.length} registos, ${condutoresData.length} condutores`);

        // Armazenar os dados carregados
        setRegistos(registosData);
        setCondutores(condutoresData);

        // Os filtros serão aplicados pelo useEffect abaixo
      })
      .finally(() => setIsLoading(false));

    // Configurar listeners em tempo real
    let unsubscribeRegistos: (() => void) | undefined;
    let unsubscribeCondutores: (() => void) | undefined;

    if (!isAdmin && user) {
      if (userTeamId) {
        // Se não for admin mas tiver teamId, filtrar por equipa
        unsubscribeRegistos = listenToRegistos((updatedRegistos) => {
          console.log(`Listener: ${updatedRegistos.length} registos atualizados`);
          setRegistos(updatedRegistos);
          // Os filtros serão aplicados pelo useEffect
        }, userTeamId);

        unsubscribeCondutores = listenToCondutores((updatedCondutores) => {
          console.log(`Listener: ${updatedCondutores.length} condutores atualizados`);
          setCondutores(updatedCondutores);
          // Os filtros serão aplicados pelo useEffect
        }, userTeamId);
      } else {
        // Se não tiver teamId, filtrar por responsavelId/userId
        unsubscribeRegistos = listenToRegistos((updatedRegistos) => {
          console.log(`Listener: ${updatedRegistos.length} registos atualizados`);
          setRegistos(updatedRegistos);
          // Os filtros serão aplicados pelo useEffect
        }, undefined, user.uid);

        unsubscribeCondutores = listenToCondutores((updatedCondutores) => {
          console.log(`Listener: ${updatedCondutores.length} condutores atualizados`);
          setCondutores(updatedCondutores);
          // Os filtros serão aplicados pelo useEffect
        }, undefined, user.uid);
      }
    } else if (isAdmin) {
      // Admin vê todos os registos e condutores
      unsubscribeRegistos = listenToRegistos((updatedRegistos) => {
        console.log(`Listener: ${updatedRegistos.length} registos atualizados`);
        setRegistos(updatedRegistos);
        // Os filtros serão aplicados pelo useEffect
      });

      unsubscribeCondutores = listenToCondutores((updatedCondutores) => {
        console.log(`Listener: ${updatedCondutores.length} condutores atualizados`);
        setCondutores(updatedCondutores);
        // Os filtros serão aplicados pelo useEffect
      });
    }

    // Limpar listeners ao desmontar
    return () => {
      if (unsubscribeRegistos) unsubscribeRegistos();
      if (unsubscribeCondutores) unsubscribeCondutores();
    };
  }, [user, isAdmin, userTeamId, loading, profileLoading]);

  // Função para aplicar filtros
  const applyFilters = (
    registosData: Registo[],
    condutoresData: Condutor[],
    customFilters?: RelatorioFilterOptions
  ) => {
    // Usar filtros personalizados ou os filtros atuais
    const filtersToApply = customFilters || currentFilters;

    // Filtrar registos
    let filteredRegistosData = [...registosData];
    let filteredCondutoresData = [...condutoresData];

    // Filtrar por equipa (apenas para admin)
    if (isAdmin && filtersToApply.teamId && filtersToApply.teamId !== "all") {
      filteredRegistosData = filteredRegistosData.filter(
        registo => registo.teamId === filtersToApply.teamId
      );

      filteredCondutoresData = filteredCondutoresData.filter(
        condutor => condutor.teamId === filtersToApply.teamId
      );
    }

    // Filtrar por data de início
    if (filtersToApply.dataInicio) {
      const dataInicio = new Date(filtersToApply.dataInicio);
      dataInicio.setHours(0, 0, 0, 0);

      filteredRegistosData = filteredRegistosData.filter(registo => {
        const dataRegisto = new Date(registo.dataRegisto);
        return dataRegisto >= dataInicio;
      });

      filteredCondutoresData = filteredCondutoresData.filter(condutor => {
        const dataHoraInicio = new Date(condutor.dataHoraInicio);
        return dataHoraInicio >= dataInicio;
      });
    }

    // Filtrar por data de fim
    if (filtersToApply.dataFim) {
      const dataFim = new Date(filtersToApply.dataFim);
      dataFim.setHours(23, 59, 59, 999);

      filteredRegistosData = filteredRegistosData.filter(registo => {
        const dataRegisto = new Date(registo.dataRegisto);
        return dataRegisto <= dataFim;
      });

      filteredCondutoresData = filteredCondutoresData.filter(condutor => {
        const dataHoraInicio = new Date(condutor.dataHoraInicio);
        return dataHoraInicio <= dataFim;
      });
    }

    // Atualizar estados
    setFilteredRegistos(filteredRegistosData);
    setFilteredCondutores(filteredCondutoresData);
  };

  // Efeito para aplicar filtros quando os dados ou filtros mudam
  useEffect(() => {
    // Só aplicar filtros se houver dados carregados
    if (registos.length > 0 || condutores.length > 0) {
      console.log("Aplicando filtros automaticamente com os dados carregados");
      applyFilters(registos, condutores, currentFilters);
    }
  }, [registos, condutores, currentFilters]);

  // Função para lidar com a mudança de filtros
  const handleFilter = (options: RelatorioFilterOptions) => {
    console.log("Filtros recebidos:", options);

    // Verificar se os filtros foram limpos
    const isFilterCleared =
      options.dataInicio === null &&
      options.dataFim === null &&
      (options.teamId === "all" || options.teamId === undefined);

    // Se os filtros foram limpos, definir para a data atual
    if (isFilterCleared) {
      console.log("Filtros limpos, definindo para a data atual");
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const todayFilters = {
        dataInicio: today,
        dataFim: today,
        teamId: options.teamId
      };

      // Atualizar o estado dos filtros
      // O useEffect vai aplicar os filtros automaticamente
      setCurrentFilters(todayFilters);
      return;
    }

    // Aplicar os filtros com os novos valores
    console.log("Aplicando filtros:", options);

    // Atualizar o estado dos filtros
    // O useEffect vai aplicar os filtros automaticamente
    setCurrentFilters(options);
  };

  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6 px-4 lg:px-6 max-w-full">
      <PageHeader
        title="Relatório Operacional"
        description="Visualize os registos de detenções e condutores de serviço"
      />

      {/* Filtros */}
      <div className="bg-card rounded-lg border shadow-sm p-4 sm:p-6">
        <h2 className="text-lg font-semibold mb-4">Filtros</h2>
        <RelatorioFilter
          onFilter={handleFilter}
          initialFilters={currentFilters}
          isAdmin={isAdmin}
          teams={teams}
        />
      </div>

      {/* Tabs para alternar entre registos e condutores */}
      <Tabs defaultValue="registos" className="w-full">
        <TabsList className="mb-4 w-full">
          <TabsTrigger value="registos" className="flex items-center gap-1 flex-1">
            <span className="truncate">Registos</span>
            <Badge variant="secondary" className="ml-1 rounded-full px-2 py-0 text-xs shrink-0">
              {filteredRegistos.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="condutores" className="flex items-center gap-1 flex-1">
            <span className="truncate">Condutores</span>
            <Badge variant="secondary" className="ml-1 rounded-full px-2 py-0 text-xs shrink-0">
              {filteredCondutores.length}
            </Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="registos" className="mt-2">
          <RelatorioRegistosSection
            registos={filteredRegistos}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="condutores" className="mt-2">
          <RelatorioCondutoresSection
            condutores={filteredCondutores}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
