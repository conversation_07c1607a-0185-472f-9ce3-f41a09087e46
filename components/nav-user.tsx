"use client"

import * as React from "react"
import {
  IconCreditCard,
  IconDotsVertical,
  IconLogout,
  IconNotification,
  IconUserCircle,
  IconBadge,
  IconCategory,
  IconMail,
  IconUsers,
  IconUserShield,
} from "@tabler/icons-react"

import { useAuth } from "@/contexts/auth-context"
import { useTheme } from "next-themes"
import { useRouter } from "next/navigation"
import { getTeam } from "@/services/teams-service"
import { UserRole } from "@/types/team"
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"

export function NavUser({
  user,
}: {
  user: {
    name: string
    email: string
    avatar: string
  }
}) {
  const { isMobile } = useSidebar()
  const { signOut, userProfile, isAdmin, isTeamLeader, isTeamMember } = useAuth()
  const { theme } = useTheme()
  const router = useRouter()

  // Estado para controlar se o componente está montado (cliente)
  const [mounted, setMounted] = React.useState(false)
  // Estado para armazenar o nome da equipa
  const [teamName, setTeamName] = React.useState<string | null>(null)

  // Evitar problemas de hidratação renderizando apenas no cliente
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Buscar informações da equipa quando o componente montar
  React.useEffect(() => {
    async function fetchTeamInfo() {
      if (userProfile?.teamId) {
        try {
          const teamInfo = await getTeam(userProfile.teamId)
          if (teamInfo) {
            setTeamName(teamInfo.name)
          }
        } catch (error) {
          console.error("Erro ao buscar informações da equipa:", error)
        }
      }
    }

    if (userProfile) {
      fetchTeamInfo()
    }
  }, [userProfile])

  // Função para obter o texto do papel do utilizador
  const getRoleText = () => {
    if (isAdmin) return "Administrador"
    if (isTeamLeader) return "Líder de Equipa"
    if (isTeamMember) return "Membro de Equipa"
    return "Utilizador";
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground gap-3"
            >
              <Avatar className="size-8 border-2 border-primary shadow-sm">
                <AvatarImage
                  src={user.avatar}
                  alt={user.name}
                  className={mounted && theme === "dark" ? "grayscale" : ""}
                />
                <AvatarFallback>
                  {(userProfile?.fullName || user.name).split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 text-left text-sm">
                <span className="truncate font-medium">{userProfile?.fullName || user.name}</span>
              </div>
              <IconDotsVertical className="size-4 text-muted-foreground" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={8}
          >
            <DropdownMenuLabel className="px-3 py-2 font-medium">
              {userProfile?.fullName || user.name}
            </DropdownMenuLabel>

            {userProfile && (
              <>
                <DropdownMenuSeparator />

              <div className="px-3 py-2 space-y-3">
                {user.email && (
                  <div className="flex items-center gap-3">
                    <IconMail className="size-4 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm truncate">{user.email}</span>
                  </div>
                )}
                {userProfile.registrationNumber && (
                  <div className="flex items-center gap-3">
                    <IconBadge className="size-4 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm">{userProfile.registrationNumber}</span>
                  </div>
                )}
                {userProfile.category && (
                  <div className="flex items-center gap-3">
                    <IconCategory className="size-4 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm">{userProfile.category}</span>
                  </div>
                )}
                {userProfile.role && (
                  <div className="flex items-center gap-3">
                    <IconUserShield className="size-4 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm">{getRoleText()}</span>
                  </div>
                )}
                {teamName && (isTeamLeader || isTeamMember) && (
                  <div className="flex items-center gap-3">
                    <IconUsers className="size-4 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm truncate">{teamName}</span>
                  </div>
                )}
              </div>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={() => router.push("/conta")}>
                <IconUserCircle className="mr-2 size-4" />
                <span>Conta</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={signOut} className="text-destructive focus:text-destructive">
              <IconLogout className="mr-2 size-4" />
              <span>Terminar Sessão</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
