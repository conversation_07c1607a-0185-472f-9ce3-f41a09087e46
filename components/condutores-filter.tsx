"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { IconFilter, IconSearch, IconX } from "@tabler/icons-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Viatura } from "@/services/viaturas-service";
import { Condutor } from "@/services/condutores-service";

interface CondutoresFilterProps {
  viaturas: Viatura[];
  onFilter: (filters: FilterOptions) => void;
  isAdmin?: boolean;
  teams?: { id: string; name: string }[];
  users?: { id: string; name: string }[];
}

export interface FilterOptions {
  nome: string;
  viaturaId: string;
  dataInicio: Date | null;
  dataFim: Date | null;
  teamId?: string;
  userId?: string;
  status?: 'all' | 'active' | 'finished';
  hasAbastecimento?: boolean | null;
}

export function CondutoresFilter({ viaturas, onFilter, isAdmin = false, teams = [], users = [] }: CondutoresFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    nome: "",
    viaturaId: "all",
    dataInicio: null,
    dataFim: null,
    teamId: "all",
    userId: "all",
    status: "all",
    hasAbastecimento: null,
  });
  const [isFiltering, setIsFiltering] = useState(false);

  // Função para atualizar um campo específico do filtro
  const updateFilterValue = (field: keyof FilterOptions, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  // Função para aplicar os filtros
  const aplicarFiltros = () => {
    setIsFiltering(
      filters.nome !== "" ||
      filters.viaturaId !== "all" ||
      filters.dataInicio !== null ||
      filters.dataFim !== null ||
      filters.teamId !== "all" ||
      filters.userId !== "all" ||
      filters.status !== "all" ||
      filters.hasAbastecimento !== null
    );

    onFilter(filters);
  };

  // Função para atualizar um campo específico do filtro e aplicar imediatamente
  const updateFilterAndApply = (field: keyof FilterOptions, value: any) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);

    setIsFiltering(
      newFilters.nome !== "" ||
      newFilters.viaturaId !== "all" ||
      newFilters.dataInicio !== null ||
      newFilters.dataFim !== null ||
      newFilters.teamId !== "all" ||
      newFilters.userId !== "all" ||
      newFilters.status !== "all" ||
      newFilters.hasAbastecimento !== null
    );

    onFilter(newFilters);
  };

  // Função para limpar os filtros
  const limparFiltros = () => {
    const resetFilters = {
      nome: "",
      viaturaId: "all",
      dataInicio: null,
      dataFim: null,
      teamId: "all",
      userId: "all",
      hasAbastecimento: null,
    };

    setFilters(resetFilters);
    setIsFiltering(false);
    onFilter(resetFilters);
  };

  return (
    <div className="space-y-4 mb-6">
      {/* Botão de filtro (sempre visível) */}
      <div className="flex justify-end">
        <Button
          variant={isExpanded ? "default" : "outline"}
          onClick={() => setIsExpanded(!isExpanded)}
          className="shrink-0"
        >
          <IconFilter className="size-4 mr-2" />
          Filtros
          {isFiltering && (
            <Badge variant="secondary" className="ml-2 px-1 py-0 text-xs">
              Ativos
            </Badge>
          )}
        </Button>
      </div>

      {/* Filtros (expansíveis) */}
      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 border rounded-md w-full">
          {/* Barra de pesquisa por nome */}
          <div className="col-span-full">
            <label className="text-sm font-medium mb-1.5 block">Nome do Condutor</label>
            <div className="relative">
              <Input
                placeholder="Pesquisar por nome do condutor..."
                value={filters.nome}
                onChange={(e) => {
                  const newValue = e.target.value;
                  updateFilterValue("nome", newValue);
                }}
                className="pl-9"
              />
              <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
            </div>
          </div>

          <div>
            <label className="text-sm font-medium mb-1.5 block">Viatura</label>
            <Select
              value={filters.viaturaId}
              onValueChange={(value) => {
                updateFilterValue("viaturaId", value);
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Todas as viaturas" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as viaturas</SelectItem>
                {viaturas.map((viatura) => (
                  <SelectItem key={viatura.id} value={viatura.id}>
                    {viatura.matricula} - {viatura.modelo}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-1.5 block">Data Inicial</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left">
                  {filters.dataInicio ? (
                    format(filters.dataInicio, "dd/MM/yyyy", { locale: ptBR })
                  ) : (
                    <span className="text-muted-foreground">Selecionar data</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dataInicio || undefined}
                  onSelect={(date) => {
                    updateFilterValue("dataInicio", date);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div>
            <label className="text-sm font-medium mb-1.5 block">Data Final</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left">
                  {filters.dataFim ? (
                    format(filters.dataFim, "dd/MM/yyyy", { locale: ptBR })
                  ) : (
                    <span className="text-muted-foreground">Selecionar data</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dataFim || undefined}
                  onSelect={(date) => {
                    updateFilterValue("dataFim", date);
                  }}
                  initialFocus
                  disabled={(date) =>
                    filters.dataInicio ? date < filters.dataInicio : false
                  }
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Filtros adicionais para administradores */}
          {isAdmin && (
            <>
              {/* Filtro por Equipa */}
              <div>
                <label className="text-sm font-medium mb-1.5 block">Equipa</label>
                <Select
                  value={filters.teamId}
                  onValueChange={(value) => {
                    updateFilterValue("teamId", value);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Todas as equipas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas as equipas</SelectItem>
                    <SelectItem value="admin">Administradores</SelectItem>
                    {teams.map((team) => (
                      <SelectItem key={team.id} value={team.id}>
                        {team.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Filtro por Responsável */}
              <div>
                <label className="text-sm font-medium mb-1.5 block">Responsável</label>
                <Select
                  value={filters.userId}
                  onValueChange={(value) => {
                    updateFilterValue("userId", value);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Todos os responsáveis" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os responsáveis</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>



              {/* Filtro por Abastecimento */}
              <div>
                <label className="text-sm font-medium mb-1.5 block">Abastecimento</label>
                <Select
                  value={filters.hasAbastecimento === null ? "all" : filters.hasAbastecimento ? "yes" : "no"}
                  onValueChange={(value) => {
                    let hasAbastecimento = null;
                    if (value === "yes") hasAbastecimento = true;
                    if (value === "no") hasAbastecimento = false;
                    updateFilterValue("hasAbastecimento", hasAbastecimento);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Todos os registos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os registos</SelectItem>
                    <SelectItem value="yes">Com abastecimento</SelectItem>
                    <SelectItem value="no">Sem abastecimento</SelectItem>
                  </SelectContent>
                </Select>
              </div>


            </>
          )}

          <div className="col-span-full flex justify-end gap-2 mt-2">
            <Button
              variant="outline"
              onClick={limparFiltros}
              className="shrink-0"
            >
              <IconX className="size-4 mr-2" />
              Limpar Filtros
            </Button>
            <Button
              onClick={aplicarFiltros}
              className="shrink-0"
            >
              <IconFilter className="size-4 mr-2" />
              Aplicar Filtros
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
