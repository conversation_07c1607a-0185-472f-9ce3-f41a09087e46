"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  IconDotsVertical,
  IconEdit,
  IconTrash,
  IconEye,
  IconCopy
} from "@tabler/icons-react";
import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";
import { copyToClipboard } from "@/lib/clipboard-utils";
import { formatDate } from "@/lib/utils";
import { Texto } from "@/services/textos-service";

interface TextosTableProps {
  textos: Texto[];
  onDelete: (id: string) => Promise<void>;
  onEdit: (id: string, newTitle: string, newContent: string) => Promise<void>;
}

export function TextosTable({ textos, onDelete, onEdit }: TextosTableProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedTexto, setSelectedTexto] = useState<Texto | null>(null);
  const [newTitle, setNewTitle] = useState("");
  const [newContent, setNewContent] = useState("");

  const handleViewClick = (texto: Texto) => {
    setSelectedTexto(texto);
    setIsViewDialogOpen(true);
  };

  const handleCopyClick = async (texto: Texto) => {
    await copyToClipboard(texto.content, {
      successMessage: "Texto copiado para a área de transferência",
      errorMessage: "Erro ao copiar texto"
    });
  };

  const handleDeleteClick = (texto: Texto) => {
    setSelectedTexto(texto);
    setIsDeleteDialogOpen(true);
  };

  const handleEditClick = (texto: Texto) => {
    setSelectedTexto(texto);
    setNewTitle(texto.title);
    setNewContent(texto.content);
    setIsEditDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedTexto) return;

    try {
      await onDelete(selectedTexto.id);
      showSuccessToast("Texto eliminado com sucesso");
    } catch (error: any) {
      showErrorToast("Erro ao eliminar texto");
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedTexto(null);
    }
  };

  const confirmEdit = async () => {
    if (!selectedTexto || !newTitle.trim() || !newContent.trim()) return;

    try {
      await onEdit(selectedTexto.id, newTitle, newContent);
      showSuccessToast("Texto atualizado com sucesso");
    } catch (error: any) {
      showErrorToast("Erro ao atualizar texto");
    } finally {
      setIsEditDialogOpen(false);
      setSelectedTexto(null);
      setNewTitle("");
      setNewContent("");
    }
  };

  return (
    <>
      <div className="rounded-md border overflow-x-auto">
        <Table className="w-full table-fixed">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[40%]">Título</TableHead>
              <TableHead className="hidden md:table-cell w-[40%]">Prévia</TableHead>
              <TableHead className="hidden md:table-cell w-[20%] text-center">Data de Criação</TableHead>
              <TableHead className="w-[60px] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {textos.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                  Nenhum texto encontrado.
                </TableCell>
              </TableRow>
            ) : (
              textos.map((texto) => (
                <TableRow key={texto.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium max-w-0 overflow-hidden">
                    <div className="truncate">{texto.title}</div>
                    <div className="md:hidden text-xs text-muted-foreground mt-1 truncate">
                      {formatDate(texto.createdAt)}
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div className="truncate text-muted-foreground">
                      {texto.content.substring(0, 100)}{texto.content.length > 100 ? "..." : ""}
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell text-center">{formatDate(texto.createdAt)}</TableCell>
                  <TableCell className="text-right p-2 whitespace-nowrap">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground flex size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-32">
                        <DropdownMenuItem onClick={() => handleViewClick(texto)}>
                          <IconEye className="size-4 mr-2" />
                          Visualizar
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyClick(texto)}>
                          <IconCopy className="size-4 mr-2" />
                          Copiar
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditClick(texto)}>
                          <IconEdit className="size-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(texto)}
                          className="text-destructive focus:text-destructive"
                        >
                          <IconTrash className="size-4 mr-2" />
                          Eliminar
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Diálogo para visualizar texto */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-2xl md:max-w-3xl lg:max-w-4xl h-auto max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedTexto?.title}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4 px-1">
            <div className="whitespace-pre-wrap break-words text-sm bg-muted/30 p-4 rounded-md min-h-[200px] max-h-[50vh] overflow-y-auto">
              {selectedTexto?.content}
            </div>
          </div>
          <DialogFooter className="sm:justify-start">
            <Button
              variant="outline"
              onClick={() => handleCopyClick(selectedTexto!)}
            >
              <IconCopy className="size-4 mr-2" />
              Copiar Texto
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de confirmação para eliminar */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Eliminar Texto</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja eliminar o texto "{selectedTexto?.title}"? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Eliminar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar texto */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-2xl md:max-w-3xl lg:max-w-4xl h-auto max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Texto</DialogTitle>
            <DialogDescription>
              Atualize o título e o conteúdo do texto.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Título</Label>
              <Input
                id="edit-title"
                value={newTitle}
                onChange={(e) => setNewTitle(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-content">Conteúdo</Label>
              <Textarea
                id="edit-content"
                value={newContent}
                onChange={(e) => setNewContent(e.target.value)}
                className="min-h-[250px] resize-y"
                placeholder="Insira o conteúdo do texto"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={confirmEdit} disabled={!newTitle.trim() || !newContent.trim()}>
              Guardar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
