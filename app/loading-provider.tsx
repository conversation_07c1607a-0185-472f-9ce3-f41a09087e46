"use client";

import { usePathname, useSearchParams } from "next/navigation";
import { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";

interface LoadingContextType {
  isRouteChanging: boolean;
  loadingKey: string;
}

const LoadingContext = createContext<LoadingContextType>({
  isRouteChanging: false,
  loadingKey: "",
});

export const useLoading = () => useContext(LoadingContext);

export function LoadingProvider({ children }: { children: React.ReactNode }) {
  const [isRouteChanging, setIsRouteChanging] = useState(false);
  const [loadingKey, setLoadingKey] = useState("");
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const url = pathname + searchParams.toString();
    setLoadingKey(url);
    setIsRouteChanging(false);
  }, [pathname, searchParams]);

  // Interceptar navegações para mostrar indicador de carregamento
  const originalPush = router.push;
  const originalReplace = router.replace;

  useEffect(() => {
    router.push = (...args: Parameters<typeof router.push>) => {
      setIsRouteChanging(true);
      return originalPush.apply(router, args);
    };

    router.replace = (...args: Parameters<typeof router.replace>) => {
      setIsRouteChanging(true);
      return originalReplace.apply(router, args);
    };

    return () => {
      router.push = originalPush;
      router.replace = originalReplace;
    };
  }, [router, originalPush, originalReplace]);

  return (
    <LoadingContext.Provider value={{ isRouteChanging, loadingKey }}>
      {children}
    </LoadingContext.Provider>
  );
}
