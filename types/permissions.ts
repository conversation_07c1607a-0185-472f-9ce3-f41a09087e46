/**
 * Interface para representar permissões de acesso a páginas
 */
export interface PagePermission {
  id: string;
  pageId: string;      // Identificador único da página (ex: "registos", "condutores")
  pageName: string;    // Nome amigável da página (ex: "Registo Expediente", "Condutor de Serviço")
  hidden: boolean;     // Se a página está oculta
  targetType: 'user' | 'team'; // Se a permissão se aplica a um usuário ou equipe
  targetId: string;    // ID do usuário ou equipe
  targetName: string;  // Nome do usuário ou equipe (para exibição)
  createdAt: Date;     // Data de criação
  updatedAt: Date;     // Data de atualização
  createdBy: string;   // ID do usuário que criou a permissão
}

/**
 * Verifica se uma página deve ser ocultada para um usuário
 */
export function shouldHidePage(pageId: string, userPermissions: PagePermission[]): boolean {
  // Verificar permissões do usuário primeiro (prioridade mais alta)
  const userPermission = userPermissions.find(p =>
    p.pageId === pageId && p.targetType === 'user'
  );

  if (userPermission) {
    return userPermission.hidden;
  }

  // Verificar permissões da equipe
  const teamPermission = userPermissions.find(p =>
    p.pageId === pageId && p.targetType === 'team'
  );

  if (teamPermission) {
    return teamPermission.hidden;
  }

  // Se não houver permissões específicas, a página é visível por padrão
  return false;
}

/**
 * Interface para representar uma página no sistema
 */
export interface SystemPage {
  id: string;          // Identificador único da página (ex: "registos", "condutores")
  name: string;        // Nome amigável da página (ex: "Registo Expediente", "Condutor de Serviço")
  url: string;         // URL da página (ex: "/registos", "/condutores")
  category: string;    // Categoria da página (ex: "Operacional", "Documentação")
  icon?: string;       // Ícone da página (opcional)
}

/**
 * Lista de páginas do sistema
 */
export const SYSTEM_PAGES: SystemPage[] = [
  // Operacional
  { id: "condutores", name: "Condutor de Serviço", url: "/condutores", category: "Operacional" },
  { id: "registos", name: "Registo Expediente", url: "/registos", category: "Operacional" },
  { id: "identificacoes", name: "Identificações", url: "/identificacoes", category: "Operacional" },
  { id: "relatorios", name: "Relatório Operacional", url: "/relatorios", category: "Operacional" },

  // Documentação
  { id: "formularios", name: "Formulários", url: "/formularios", category: "Documentação" },
  { id: "textos", name: "Textos", url: "/textos", category: "Documentação" },
  { id: "nips", name: "Nips Locais", url: "/nips", category: "Documentação" },
  { id: "nips-pessoas", name: "NIPs Pessoas", url: "/nips-pessoas", category: "Documentação" },
  { id: "contactos", name: "Contactos", url: "/contactos", category: "Documentação" },

  // Base de Dados
  { id: "estabelecimentos", name: "Estabelecimentos", url: "/estabelecimentos", category: "Base de Dados" },
  { id: "viaturas", name: "Viaturas", url: "/viaturas", category: "Base de Dados" },
  { id: "tipos-crimes", name: "Tipos de Crimes", url: "/tipos-crimes", category: "Base de Dados" },
];
