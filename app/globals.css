@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

/* Picton Blue Color Palette */
:root {
  /* Base colors */
  --color-picton-blue-50: oklch(97.4% 0.014 248.6);
  --color-picton-blue-100: oklch(94.5% 0.027 249.3);
  --color-picton-blue-200: oklch(89.8% 0.042 249.2);
  --color-picton-blue-300: oklch(83.7% 0.072 247.3);
  --color-picton-blue-400: oklch(74.2% 0.136 248.6);
  --color-picton-blue-500: oklch(67.2% 0.145 254.5);
  --color-picton-blue-600: oklch(59.4% 0.142 261.2);
  --color-picton-blue-700: oklch(51.6% 0.134 265.4);
  --color-picton-blue-800: oklch(44.6% 0.117 268);
  --color-picton-blue-900: oklch(38.8% 0.098 271.5);
  --color-picton-blue-950: oklch(26.7% 0.074 272.6);

  /* Theme variables */
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: var(--color-picton-blue-600);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: var(--color-picton-blue-100);
  --secondary-foreground: var(--color-picton-blue-900);
  --muted: var(--color-picton-blue-100);
  --muted-foreground: var(--color-picton-blue-700);
  --accent: var(--color-picton-blue-100);
  --accent-foreground: var(--color-picton-blue-900);
  --destructive: oklch(0.577 0.245 27.325);
  --border: var(--color-picton-blue-200);
  --input: var(--color-picton-blue-200);
  --ring: var(--color-picton-blue-500);
  --chart-1: var(--color-picton-blue-500);
  --chart-2: var(--color-picton-blue-600);
  --chart-3: var(--color-picton-blue-700);
  --chart-4: var(--color-picton-blue-400);
  --chart-5: var(--color-picton-blue-800);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: var(--color-picton-blue-600);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: var(--color-picton-blue-100);
  --sidebar-accent-foreground: var(--color-picton-blue-900);
  --sidebar-border: var(--color-picton-blue-200);
  --sidebar-ring: var(--color-picton-blue-500);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: var(--color-picton-blue-400);
  --primary-foreground: oklch(0.145 0 0);
  --secondary: var(--color-picton-blue-900);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: var(--color-picton-blue-950);
  --muted-foreground: var(--color-picton-blue-300);
  --accent: var(--color-picton-blue-900);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: var(--color-picton-blue-800);
  --input: var(--color-picton-blue-800);
  --ring: var(--color-picton-blue-500);
  --chart-1: var(--color-picton-blue-400);
  --chart-2: var(--color-picton-blue-500);
  --chart-3: var(--color-picton-blue-600);
  --chart-4: var(--color-picton-blue-300);
  --chart-5: var(--color-picton-blue-700);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: var(--color-picton-blue-400);
  --sidebar-primary-foreground: oklch(0.145 0 0);
  --sidebar-accent: var(--color-picton-blue-900);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: var(--color-picton-blue-800);
  --sidebar-ring: var(--color-picton-blue-500);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Garantir que inputs de data e hora tenham a mesma largura que outros inputs */
  input[type="date"],
  input[type="time"],
  input[type="datetime-local"] {
    @apply w-full box-border;
    min-width: 0 !important;
    max-width: 100% !important;
  }
}

/* Estilos personalizados para notificações */
.toaster [data-sonner-toast] {
  font-weight: 500;
}

.toaster .description {
  opacity: 1;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--toast-text-normal, black);
  font-weight: 500;
}

/* Estilos específicos para descrições em cada tipo de toast */
.toaster [data-sonner-toast][data-type="success"] .description {
  color: var(--toast-text-success, black);
  font-weight: 500;
}

.toaster [data-sonner-toast][data-type="error"] .description {
  color: var(--toast-text-error, black);
  font-weight: 500;
}

.toaster [data-sonner-toast][data-type="info"] .description {
  color: var(--toast-text-info, black);
  font-weight: 500;
}

.toaster [data-sonner-toast][data-type="warning"] .description {
  color: var(--toast-text-warning, black);
  font-weight: 500;
}

.toaster [data-sonner-toast][data-type="loading"] .description {
  color: var(--toast-text-loading, black);
  font-weight: 500;
}

/* Garantir que o texto seja sempre visível no modo light */
:root:not(.dark) .toaster .description {
  color: black !important;
  font-weight: 500 !important;
}

/* Garantir que o título das notificações seja sempre visível no modo light */
:root:not(.dark) .toaster [data-sonner-toast] > div:first-of-type {
  color: black !important;
  font-weight: 600 !important;
}

/* Classe para desativar transições durante o logout */
.no-transitions * {
  transition: none !important;
  animation: none !important;
}

/* Custom Scrollbar Styles for Sidebar */
/* Webkit-based browsers (Chrome, Safari, Edge) */
[data-sidebar="content"]::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

[data-sidebar="content"]::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

[data-sidebar="content"]::-webkit-scrollbar-thumb {
  background: var(--sidebar-border);
  border-radius: 3px;
  opacity: 0;
  transition: all 0.2s ease-in-out;
}

[data-sidebar="content"]::-webkit-scrollbar-thumb:hover {
  background: var(--sidebar-accent);
}

/* Show scrollbar on hover */
[data-sidebar="content"]:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

/* Firefox */
[data-sidebar="content"] {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.2s ease-in-out;
}

[data-sidebar="content"]:hover {
  scrollbar-color: var(--sidebar-border) transparent;
}

/* Enhanced scrollbar for dark theme */
.dark [data-sidebar="content"]::-webkit-scrollbar-thumb {
  background: var(--color-picton-blue-800);
}

.dark [data-sidebar="content"]::-webkit-scrollbar-thumb:hover {
  background: var(--color-picton-blue-600);
}

.dark [data-sidebar="content"]:hover {
  scrollbar-color: var(--color-picton-blue-800) transparent;
}

/* Subtle scrollbar for mobile sheet content */
[data-mobile="true"] [data-slot="scroll-area-viewport"]::-webkit-scrollbar {
  width: 4px;
}

[data-mobile="true"] [data-slot="scroll-area-viewport"]::-webkit-scrollbar-track {
  background: transparent;
}

[data-mobile="true"] [data-slot="scroll-area-viewport"]::-webkit-scrollbar-thumb {
  background: var(--sidebar-border);
  border-radius: 2px;
  opacity: 0.6;
}

[data-mobile="true"] [data-slot="scroll-area-viewport"]::-webkit-scrollbar-thumb:hover {
  background: var(--sidebar-accent);
  opacity: 1;
}

/* Custom scrollbar for other scroll areas in the app */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
  transition: background 0.2s ease-in-out;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--muted);
}

/* Enhanced Scrollbar Classes */
/* Hover-only scrollbars */
.enhanced-scrollbar-hover::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.enhanced-scrollbar-hover::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.enhanced-scrollbar-hover::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out, background 0.2s ease-in-out;
}

.enhanced-scrollbar-hover:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

.enhanced-scrollbar-hover::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

.enhanced-scrollbar-hover {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.2s ease-in-out;
}

.enhanced-scrollbar-hover:hover {
  scrollbar-color: var(--border) transparent;
}

/* Always visible scrollbars */
.enhanced-scrollbar-always::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.enhanced-scrollbar-always::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

.enhanced-scrollbar-always::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
  transition: background 0.2s ease-in-out;
}

.enhanced-scrollbar-always::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

.enhanced-scrollbar-always {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--muted);
}

/* Auto scrollbars (browser default with subtle styling) */
.enhanced-scrollbar-auto {
  scrollbar-width: auto;
  scrollbar-color: var(--border) var(--background);
}

/* Scrollbar sizes */
.enhanced-scrollbar-thin::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
}

.enhanced-scrollbar-normal::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.enhanced-scrollbar-thick::-webkit-scrollbar {
  width: 12px !important;
  height: 12px !important;
}

/* Dark theme enhancements for enhanced scrollbars */
.dark .enhanced-scrollbar-hover::-webkit-scrollbar-thumb {
  background: var(--color-picton-blue-800);
}

.dark .enhanced-scrollbar-hover::-webkit-scrollbar-thumb:hover {
  background: var(--color-picton-blue-600);
}

.dark .enhanced-scrollbar-hover:hover {
  scrollbar-color: var(--color-picton-blue-800) transparent;
}

.dark .enhanced-scrollbar-always::-webkit-scrollbar-thumb {
  background: var(--color-picton-blue-800);
}

.dark .enhanced-scrollbar-always::-webkit-scrollbar-thumb:hover {
  background: var(--color-picton-blue-600);
}

.dark .enhanced-scrollbar-always {
  scrollbar-color: var(--color-picton-blue-800) var(--muted);
}
