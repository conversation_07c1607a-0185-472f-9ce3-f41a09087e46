"use client";

import { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/contexts/auth-context";

interface NIPPessoaFormProps {
  onSaveComplete: (nipData: {
    nomeCompleto: string;
    numero: string;
    observacoes?: string;
    createdBy: string;
  }) => void;
}

export function NIPPessoaForm({ onSaveComplete }: NIPPessoaFormProps) {
  const [nomeCompleto, setNomeCompleto] = useState("");
  const [numero, setNumero] = useState("");
  const [observacoes, setObservacoes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();

  const handleNomeCompletoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNomeCompleto(e.target.value);
  };

  const handleNumeroChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNumero(e.target.value);
  };

  const handleObservacoesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setObservacoes(e.target.value);
  };

  const resetForm = () => {
    setNomeCompleto("");
    setNumero("");
    setObservacoes("");
  };

  const handleSave = async () => {
    if (!nomeCompleto.trim()) {
      toast.error("Nome completo obrigatório");
      return;
    }

    if (!numero.trim()) {
      toast.error("Número de NIP obrigatório");
      return;
    }

    if (!user) {
      toast.error("Utilizador não autenticado");
      return;
    }

    setIsSubmitting(true);
    const loadingToast = toast.loading("A guardar NIP Pessoa...");

    try {
      // Preparar os dados do NIP Pessoa
      const nipData: any = {
        nomeCompleto: nomeCompleto,
        numero: numero,
        createdBy: user.uid,
      };

      // Only include observacoes if it has a value
      const trimmedObservacoes = observacoes.trim();
      if (trimmedObservacoes) {
        nipData.observacoes = trimmedObservacoes;
      }

      // Notificar o componente pai sobre o NIP Pessoa guardado
      onSaveComplete(nipData);

      toast.dismiss(loadingToast);
      toast.success("NIP Pessoa guardado com sucesso");

      resetForm();
    } catch (error: any) {
      toast.dismiss(loadingToast);
      toast.error("Erro ao guardar NIP Pessoa");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4 py-2">
      <div className="space-y-2">
        <Label htmlFor="nomeCompleto">Nome Completo da Pessoa</Label>
        <Input
          id="nomeCompleto"
          placeholder="Insira o nome completo da pessoa"
          value={nomeCompleto}
          onChange={handleNomeCompletoChange}
          disabled={isSubmitting}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="numero">Nº NIP</Label>
        <Input
          id="numero"
          placeholder="Insira o número do NIP"
          value={numero}
          onChange={handleNumeroChange}
          disabled={isSubmitting}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="observacoes">Observações</Label>
        <Textarea
          id="observacoes"
          placeholder="Insira observações sobre a pessoa (opcional)"
          value={observacoes}
          onChange={handleObservacoesChange}
          disabled={isSubmitting}
          rows={3}
        />
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-end gap-2 mt-6">
        <Button
          variant="outline"
          type="button"
          onClick={resetForm}
          disabled={isSubmitting || (!nomeCompleto.trim() && !numero.trim() && !observacoes.trim())}
          className="sm:order-first"
        >
          Limpar Campos
        </Button>
        <Button
          onClick={handleSave}
          disabled={isSubmitting || !nomeCompleto.trim() || !numero.trim()}
        >
          {isSubmitting ? "A guardar..." : "Guardar NIP Pessoa"}
        </Button>
      </div>
    </div>
  );
}
