# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
firebase-debug.log*
ui-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example
!.env.local.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Firebase
/.firebase/
.firebase/
firebase-debug.log
firebase-debug.*.log
# Mantenha os arquivos de configuração do Firebase no controle de versão
!firebase.json
!.firebaserc
!firestore.indexes.json
!firestore.rules
!firebase-storage.rules

# Firebase Admin SDK
**/serviceAccountKey.json
**/woomanagerapp-*.json
**/firebase-adminsdk-*.json

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Cache
.cache/
