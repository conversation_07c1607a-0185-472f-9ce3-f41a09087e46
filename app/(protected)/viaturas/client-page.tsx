"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout
import { ViaturasTable } from "@/components/viaturas-table";
import { listenToViaturas, Viatura, addViatura } from "@/services/viaturas-service";
import { Button } from "@/components/ui/button";
import { IconPlus } from "@tabler/icons-react";
import { ViaturaForm } from "@/components/viatura-form";
import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";

export default function ClientPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [viaturas, setViaturas] = useState<Viatura[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Redirecionar para login se não estiver autenticado
  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    }
  }, [user, loading, router]);

  // Não redirecionamos para completar perfil na página de viaturas
  // pois esta página é acessível mesmo sem perfil completo

  // Configurar listener para viaturas em tempo real
  useEffect(() => {
    if (!user) return;

    setIsLoading(true);

    // Configurar listener para atualizações em tempo real
    const unsubscribe = listenToViaturas((data) => {
      setViaturas(data);
      setIsLoading(false);
    });

    // Função de cleanup para remover o listener quando o componente for desmontado
    return () => {
      unsubscribe();
    };
  }, [user]);

  // Mostrar carregamento enquanto verifica autenticação
  if (loading || !user) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-4rem)]">
        <p>A carregar...</p>
      </div>
    );
  }

  // Função para adicionar uma nova viatura
  const handleAddViatura = async (viaturaData: Omit<Viatura, "id" | "createdAt" | "updatedAt">) => {
    try {
      await addViatura(viaturaData);
      setIsAddModalOpen(false);
      showSuccessToast("Viatura adicionada com sucesso");
    } catch (error) {
      console.error("Erro ao adicionar viatura:", error);
      showErrorToast("Erro ao adicionar viatura");
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Viaturas</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie as viaturas disponíveis no sistema
              </p>
            </div>
            <Button onClick={() => setIsAddModalOpen(true)} className="shrink-0">
              <IconPlus className="size-4 mr-2" />
              Adicionar Viatura
            </Button>
          </div>



          <div className="mt-2">
            <ViaturasTable
              viaturas={viaturas}
              loading={isLoading}
              onAddViatura={() => setIsAddModalOpen(true)}
            />
          </div>
        </div>
      </div>

      {/* Modal para adicionar viatura */}
      <ViaturaForm
        open={isAddModalOpen}
        onOpenChange={setIsAddModalOpen}
        onSaveComplete={handleAddViatura}
      />
    </div>
  );
}
