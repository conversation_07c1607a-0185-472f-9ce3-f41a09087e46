"use client";

import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { showErrorToast } from "@/lib/toast-utils";
import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { LocationAutocomplete } from "@/components/location-autocomplete";
import { CrimeTypeAutocomplete } from "@/components/crime-type-autocomplete";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { IconCalendar } from "@tabler/icons-react";
import { getCurrentDateTime } from "@/lib/date-utils";
import { initializeCommonCrimeTypes } from "@/services/crime-types-service";

// Definir o esquema de validação
const autoNoticiaSchema = z.object({
  nppNuipc: z.string().min(1, { message: "NPP/NUIPC é obrigatório" }),
  responsavelId: z.string().min(1, { message: "Responsável é obrigatório" }),
  dataRegisto: z.string().min(1, { message: "Data de registo é obrigatória" }),
  horaRegisto: z.string().min(1, { message: "Hora de registo é obrigatória" }),
  local: z.string().min(1, { message: "Local é obrigatório" }),
  tipoCrime: z.string().min(1, { message: "Tipo de crime é obrigatório" }),
  tipoCrimeId: z.string().optional(),
});

type AutoNoticiaFormValues = z.infer<typeof autoNoticiaSchema>;

interface User {
  id: string;
  name: string;
  registrationNumber?: string;
}

interface AutoNoticiaFormProps {
  onSaveComplete: (autoNoticiaData: {
    id?: string; // ID opcional para edição
    nppNuipc: string;
    responsavelId: string;
    responsavelNome: string;
    dataRegisto: string;
    horaRegisto: string;
    local: string;
    tipoCrime: string;
    tipoCrimeId?: string; // ID do tipo de crime selecionado
    createdBy: string;
  }, isEdit: boolean) => void; // Adicionado flag isEdit
  users: User[];
  initialData?: {
    nppNuipc: string;
    responsavelId: string;
    dataRegisto: string;
    horaRegisto: string;
    local: string;
    tipoCrime: string;
  };
  isEditing?: boolean;
  editingId?: string; // ID do registro sendo editado
  onCancelEdit?: () => void;
}

export function AutoNoticiaForm({ onSaveComplete, users, initialData, isEditing = false, editingId, onCancelEdit }: AutoNoticiaFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  const { date: currentDate, time: currentTime } = getCurrentDateTime();

  // Inicializar tipos de crime comuns se o usuário estiver autenticado
  useEffect(() => {
    if (user) {
      // Tentar inicializar com forçar=false (só adiciona novos) e limpar duplicatas
      initializeCommonCrimeTypes(user.uid, false, true).catch(error => {
        console.error("Erro ao inicializar tipos de crime:", error);
      });
    }
  }, [user]);

  // Inicializar o formulário com react-hook-form
  const form = useForm<AutoNoticiaFormValues>({
    resolver: zodResolver(autoNoticiaSchema),
    defaultValues: initialData || {
      nppNuipc: "",
      responsavelId: "",
      dataRegisto: currentDate,
      horaRegisto: currentTime,
      local: "",
      tipoCrime: "",
    },
  });

  // Atualizar o formulário quando os dados iniciais mudarem (modo de edição)
  useEffect(() => {
    if (initialData && isEditing) {
      form.reset(initialData);
    }
  }, [form, initialData, isEditing]);

  // Função para lidar com a submissão do formulário
  async function onSubmit(data: AutoNoticiaFormValues) {
    if (!user) return;

    setIsSubmitting(true);

    try {
      // Encontrar o responsável selecionado
      const responsavel = users.find(u => u.id === data.responsavelId);
      const responsavelNome = responsavel?.name || "";

      // Obter o teamId do responsável selecionado (se disponível)
      const responsavelTeamId = (responsavel as any)?.teamId;

      // Log para debug
      console.log("Dados do responsável selecionado:", {
        id: responsavel?.id,
        name: responsavel?.name,
        teamId: responsavelTeamId || "[não definido]"
      });

      // Obter o teamId do usuário atual (se disponível)
      const userTeamId = (user as any).teamId;

      // Preparar os dados do auto de notícia
      const autoNoticiaData: any = {
        nppNuipc: data.nppNuipc,
        responsavelId: data.responsavelId,
        responsavelNome,
        dataRegisto: data.dataRegisto,
        horaRegisto: data.horaRegisto,
        local: data.local,
        tipoCrime: data.tipoCrime,
        createdBy: user.uid,
        // Usar o teamId do usuário atual ou do responsável selecionado
        // Isso garante que todos os membros da mesma equipa vejam o registro
        // Usar string vazia como fallback para evitar valores undefined
        teamId: userTeamId || responsavelTeamId || "",
      };

      // Only include tipoCrimeId if it has a value
      if (data.tipoCrimeId) {
        autoNoticiaData.tipoCrimeId = data.tipoCrimeId;
      }

      // Log para debug
      console.log("TeamId definido para o auto de notícia:", autoNoticiaData.teamId || "[não definido]");

      // Notificar o componente pai sobre o auto de notícia guardado
      // Passar o ID se estiver no modo de edição
      if (isEditing && editingId) {
        await onSaveComplete({...autoNoticiaData, id: editingId}, true);
      } else {
        await onSaveComplete(autoNoticiaData, false);
      }

      // Limpar o formulário após salvar com sucesso
      form.reset({
        nppNuipc: "",
        responsavelId: "",
        dataRegisto: currentDate,
        horaRegisto: currentTime,
        local: "",
        tipoCrime: "",
      });
    } catch (error: any) {
      showErrorToast("Erro ao guardar auto de notícia");
    } finally {
      setIsSubmitting(false);
    }
  }



  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
        {isEditing && (
          <div className="bg-muted/50 px-4 py-2 rounded-md mb-4">
            <p className="text-sm font-medium">Modo de edição ativo</p>
            <p className="text-xs text-muted-foreground">Está a editar um registo existente.</p>
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
          <FormField
            control={form.control}
            name="nppNuipc"
            render={({ field }) => (
              <FormItem>
                <FormLabel>NPP/NUIPC</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Introduza o NPP/NUIPC"
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="responsavelId"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Responsável</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                  disabled={isSubmitting}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Selecione um responsável" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        <div className="flex justify-between w-full">
                          <span>{user.name}</span>
                          {user.registrationNumber && <span className="text-muted-foreground ml-2">{user.registrationNumber}</span>}
                        </div>
                      </SelectItem>
                    ))}
                    {users.length === 0 && (
                      <div className="p-2 text-center text-sm text-muted-foreground">
                        Nenhum utilizador disponível para seleção.
                      </div>
                    )}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
          <FormField
            control={form.control}
            name="dataRegisto"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Data de Registo</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                        disabled={isSubmitting}
                      >
                        <IconCalendar className="mr-2 h-4 w-4" />
                        {field.value ? (
                          format(new Date(field.value), "dd/MM/yyyy", { locale: ptBR })
                        ) : (
                          <span>Selecione uma data</span>
                        )}
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value ? new Date(field.value) : undefined}
                      onSelect={(date) => {
                        if (date) {
                          const formattedDate = format(date, "yyyy-MM-dd");
                          field.onChange(formattedDate);
                        }
                      }}
                      initialFocus
                      locale={ptBR}
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="horaRegisto"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Hora de Registo</FormLabel>
                <FormControl>
                  <Input
                    type="time"
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="local"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Local</FormLabel>
              <FormControl>
                <LocationAutocomplete
                  value={field.value}
                  onChange={field.onChange}
                  placeholder="Introduza o local"
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="tipoCrime"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tipo de Crime</FormLabel>
              <FormControl>
                <CrimeTypeAutocomplete
                  value={field.value}
                  onChange={(value, crimeTypeId) => {
                    field.onChange(value);
                    // Armazenar o ID do tipo de crime quando disponível
                    form.setValue("tipoCrimeId", crimeTypeId || "");
                  }}
                  placeholder="Selecione um tipo de crime"
                  disabled={isSubmitting}
                  readOnly={true} // Forçar modo somente leitura
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex flex-col sm:flex-row sm:justify-end gap-2 mt-6">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                type="button"
                onClick={onCancelEdit}
                disabled={isSubmitting}
                className="sm:order-first"
              >
                Cancelar Edição
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? "A guardar..." : "Atualizar Auto de Notícia"}
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  form.reset({
                    nppNuipc: "",
                    responsavelId: "",
                    dataRegisto: currentDate,
                    horaRegisto: currentTime,
                    local: "",
                    tipoCrime: "",
                  });
                }}
                disabled={isSubmitting}
                className="sm:order-first"
              >
                Limpar Campos
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? "A guardar..." : "Guardar Auto de Notícia"}
              </Button>
            </>
          )}
        </div>
      </form>
    </Form>
  );
}
