"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  IconDotsVertical,
  IconEdit,
  IconTrash,
  IconCopy,
  IconPhone,
  IconMail,
  IconPlus,
  IconStar,
  IconStarFilled
} from "@tabler/icons-react";
import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";
import { copyToClipboard } from "@/lib/clipboard-utils";
import { formatDate } from "@/lib/utils";
import { Contacto, ContactInfo } from "@/services/contactos-service";

interface ContactosTableProps {
  contactos: Contacto[];
  onDelete: (id: string) => Promise<void>;
  onEdit: (id: string, contacto: Contacto) => Promise<void>;
}

export function ContactosTable({ contactos, onDelete, onEdit }: ContactosTableProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedContacto, setSelectedContacto] = useState<Contacto | null>(null);
  const [editNome, setEditNome] = useState("");
  const [editTelefones, setEditTelefones] = useState<ContactInfo[]>([]);
  const [editEmails, setEditEmails] = useState<ContactInfo[]>([]);

  // Função para obter o telefone principal
  const getPrimaryPhone = (telefones: ContactInfo[]) => {
    const primary = telefones.find(tel => tel.primary);
    return primary ? primary.value : telefones[0]?.value || "";
  };

  // Função para obter o email principal
  const getPrimaryEmail = (emails: ContactInfo[]) => {
    const primary = emails.find(email => email.primary);
    return primary ? primary.value : emails[0]?.value || "";
  };

  // Função para copiar o telefone para a área de transferência
  const copyPhone = async (telefone: string) => {
    await copyToClipboard(telefone, {
      successMessage: "Telefone copiado",
      errorMessage: "Erro ao copiar telefone"
    });
  };

  // Função para copiar o email para a área de transferência
  const copyEmail = async (email: string) => {
    await copyToClipboard(email, {
      successMessage: "Email copiado",
      errorMessage: "Erro ao copiar email"
    });
  };

  // Função para confirmar a exclusão de um contacto
  const confirmDelete = (contacto: Contacto) => {
    setSelectedContacto(contacto);
    setIsDeleteDialogOpen(true);
  };

  // Função para visualizar detalhes do contacto
  const viewContacto = (contacto: Contacto) => {
    setSelectedContacto(contacto);
    setIsViewDialogOpen(true);
  };

  // Função para editar um contacto
  const editContacto = (contacto: Contacto) => {
    setSelectedContacto(contacto);
    setEditNome(contacto.nome);

    // Criar cópias profundas dos arrays para evitar referências compartilhadas
    const telefonesClone = contacto.telefones.map(tel => ({ ...tel }));
    const emailsClone = contacto.emails.map(email => ({ ...email }));

    setEditTelefones(telefonesClone);
    setEditEmails(emailsClone);
    setIsEditDialogOpen(true);
  };

  // Função para confirmar a edição
  const confirmEdit = async () => {
    if (!selectedContacto || !editNome.trim()) return;

    // Verificar se há pelo menos um telefone e um email válidos
    const validTelefones = editTelefones.filter(tel => tel.value.trim() !== "");
    const validEmails = editEmails.filter(email => email.value.trim() !== "");

    if (validTelefones.length === 0 || validEmails.length === 0) {
      showErrorToast("Deve fornecer pelo menos um telefone e um email válidos");
      return;
    }

    try {
      // Garantir que pelo menos um telefone e um email são primários
      if (!validTelefones.some(tel => tel.primary)) {
        validTelefones[0].primary = true;
      }

      if (!validEmails.some(email => email.primary)) {
        validEmails[0].primary = true;
      }

      const updatedContacto = {
        ...selectedContacto,
        nome: editNome,
        telefones: validTelefones,
        emails: validEmails
      };

      await onEdit(selectedContacto.id, updatedContacto);

      showSuccessToast("Contacto atualizado");
    } catch (error: any) {
      showErrorToast("Erro ao atualizar contacto");
    } finally {
      setIsEditDialogOpen(false);
      setSelectedContacto(null);
      setEditNome("");
      setEditTelefones([]);
      setEditEmails([]);
    }
  };

  // Função para adicionar um telefone no formulário de edição
  const addEditTelefone = () => {
    setEditTelefones([...editTelefones, { value: "", primary: false }]);
  };

  // Função para adicionar um email no formulário de edição
  const addEditEmail = () => {
    setEditEmails([...editEmails, { value: "", primary: false }]);
  };

  // Função para remover um telefone no formulário de edição
  const removeEditTelefone = (index: number) => {
    if (editTelefones.length > 1) {
      const newTelefones = editTelefones.filter((_, i) => i !== index);

      // Se removermos o telefone primário, definir o primeiro como primário
      if (editTelefones[index].primary && newTelefones.length > 0) {
        newTelefones[0] = { ...newTelefones[0], primary: true };
      }

      setEditTelefones(newTelefones);
    }
  };

  // Função para remover um email no formulário de edição
  const removeEditEmail = (index: number) => {
    if (editEmails.length > 1) {
      const newEmails = editEmails.filter((_, i) => i !== index);

      // Se removermos o email primário, definir o primeiro como primário
      if (editEmails[index].primary && newEmails.length > 0) {
        newEmails[0] = { ...newEmails[0], primary: true };
      }

      setEditEmails(newEmails);
    }
  };

  // Função para definir um telefone como primário no formulário de edição
  const setEditPrimaryTelefone = (index: number) => {
    const newTelefones = editTelefones.map((tel, i) => ({
      ...tel,
      primary: i === index
    }));
    setEditTelefones(newTelefones);
  };

  // Função para definir um email como primário no formulário de edição
  const setEditPrimaryEmail = (index: number) => {
    const newEmails = editEmails.map((email, i) => ({
      ...email,
      primary: i === index
    }));
    setEditEmails(newEmails);
  };

  // Função para atualizar o valor de um telefone no formulário de edição
  const handleEditTelefoneChange = (index: number, value: string) => {
    const newTelefones = [...editTelefones];
    newTelefones[index] = { ...newTelefones[index], value };
    setEditTelefones(newTelefones);
  };

  // Função para atualizar o valor de um email no formulário de edição
  const handleEditEmailChange = (index: number, value: string) => {
    const newEmails = [...editEmails];
    newEmails[index] = { ...newEmails[index], value };
    setEditEmails(newEmails);
  };

  // Função para excluir um contacto
  const handleDelete = async () => {
    if (!selectedContacto) return;

    try {
      await onDelete(selectedContacto.id);
      setIsDeleteDialogOpen(false);
      showSuccessToast("Contacto excluído");
    } catch (error) {
      console.error("Erro ao excluir contacto:", error);
      showErrorToast("Erro ao excluir contacto");
    }
  };

  return (
    <>
      <div className="relative flex flex-col gap-4">
        <div className="overflow-hidden rounded-lg border">
          <Table className="w-full">
            <TableHeader className="bg-muted sticky top-0 z-10">
              <TableRow>
                <TableHead className="w-[35%]">Nome</TableHead>
                <TableHead className="w-[20%]">Telefone</TableHead>
                <TableHead className="w-[25%]">Email</TableHead>
                <TableHead className="w-[10%] text-center">Data</TableHead>
                <TableHead className="w-[10%] text-right"></TableHead>
              </TableRow>
            </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {contactos.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center text-muted-foreground">
                  Nenhum contacto encontrado.
                </TableCell>
              </TableRow>
            ) : (
              contactos.map((contacto) => (
                <TableRow key={`contacto-row-${contacto.id}`} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="truncate" title={contacto.nome}>{contacto.nome}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <span className="truncate" title={getPrimaryPhone(contacto.telefones)}>{getPrimaryPhone(contacto.telefones)}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="size-6 shrink-0"
                        onClick={() => copyPhone(getPrimaryPhone(contacto.telefones))}
                        title="Copiar telefone"
                      >
                        <IconCopy className="size-3.5" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <span className="truncate" title={getPrimaryEmail(contacto.emails)}>{getPrimaryEmail(contacto.emails)}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="size-6 shrink-0"
                        onClick={() => copyEmail(getPrimaryEmail(contacto.emails))}
                        title="Copiar email"
                      >
                        <IconCopy className="size-3.5" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="truncate" title={formatDate(contacto.createdAt)}>
                      {formatDate(contacto.createdAt).split(' ')[0]}
                    </div>
                  </TableCell>
                  <TableCell className="text-right p-2 whitespace-nowrap">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground flex size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[160px]">
                        <DropdownMenuItem
                          onClick={() => viewContacto(contacto)}
                          className="cursor-pointer"
                        >
                          Ver detalhes
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => editContacto(contacto)}
                          className="cursor-pointer"
                        >
                          <IconEdit className="size-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => confirmDelete(contacto)}
                          className="text-destructive cursor-pointer"
                        >
                          <IconTrash className="size-4 mr-2" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>

      {/* Modal de Exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Excluir Contacto</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir o contacto "{selectedContacto?.nome}"?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
            >
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Visualização */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-2xl md:max-w-3xl lg:max-w-4xl h-auto max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedContacto?.nome}</DialogTitle>
          </DialogHeader>
          {selectedContacto && (
            <div className="space-y-4 py-4 px-1">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Telefones */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Telefones</h3>
                  <div className="bg-muted/30 p-4 rounded-md space-y-2">
                    {selectedContacto.telefones.length === 0 ? (
                      <p className="text-muted-foreground text-sm">Nenhum telefone registrado</p>
                    ) : (
                      selectedContacto.telefones.map((telefone, index) => (
                        <div key={`telefone-${selectedContacto.id}-${index}`} className="flex items-center justify-between group hover:bg-muted/50 p-1.5 rounded-md transition-colors">
                          <div className="flex items-center gap-2">
                            <IconPhone className="size-4 text-muted-foreground" />
                            <span>{telefone.value}</span>
                            {telefone.primary && (
                              <span className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full">
                                Principal
                              </span>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-7 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => copyPhone(telefone.value)}
                            title="Copiar telefone"
                          >
                            <IconCopy className="size-3.5" />
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                </div>

                {/* Emails */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Emails</h3>
                  <div className="bg-muted/30 p-4 rounded-md space-y-2">
                    {selectedContacto.emails.length === 0 ? (
                      <p className="text-muted-foreground text-sm">Nenhum email registrado</p>
                    ) : (
                      selectedContacto.emails.map((email, index) => (
                        <div key={`email-${selectedContacto.id}-${index}`} className="flex items-center justify-between group hover:bg-muted/50 p-1.5 rounded-md transition-colors">
                          <div className="flex items-center gap-2">
                            <IconMail className="size-4 text-muted-foreground" />
                            <span>{email.value}</span>
                            {email.primary && (
                              <span className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full">
                                Principal
                              </span>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-7 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => copyEmail(email.value)}
                            title="Copiar email"
                          >
                            <IconCopy className="size-3.5" />
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>

              {/* Informações adicionais */}
              <div className="text-xs text-muted-foreground mt-4 pt-4 border-t border-border flex flex-col sm:flex-row sm:justify-between gap-2">
                <div>Criado em: {formatDate(selectedContacto.createdAt)}</div>
                {selectedContacto.updatedAt && selectedContacto.updatedAt !== selectedContacto.createdAt && (
                  <div>Atualizado em: {formatDate(selectedContacto.updatedAt)}</div>
                )}
              </div>
            </div>
          )}
          <DialogFooter className="sm:justify-start">
            <Button
              variant="outline"
              onClick={() => editContacto(selectedContacto!)}
            >
              <IconEdit className="size-4 mr-2" />
              Editar Contacto
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Edição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-2xl md:max-w-3xl lg:max-w-4xl h-auto max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Contacto</DialogTitle>
            <DialogDescription>
              Atualize as informações do contacto.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {/* Nome */}
            <div className="space-y-2">
              <Label htmlFor="edit-nome" className="text-sm">Nome</Label>
              <Input
                id="edit-nome"
                placeholder="Insira o nome do contacto"
                value={editNome}
                onChange={(e) => setEditNome(e.target.value)}
                className="w-full"
              />
            </div>

            {/* Telefones */}
            <div className="space-y-3 mt-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm">Telefones</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addEditTelefone}
                  className="h-8 px-3 text-xs"
                >
                  <IconPlus className="size-3.5 mr-1" />
                  Adicionar
                </Button>
              </div>

              <div className="space-y-2 bg-muted/20 p-3 rounded-md">
                {editTelefones.map((telefone, index) => (
                  <div key={`edit-telefone-${index}`} className="flex items-center gap-2 bg-background p-1.5 rounded-md border border-border/40">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="size-7 shrink-0"
                      onClick={() => setEditPrimaryTelefone(index)}
                      title={telefone.primary ? "Telefone principal" : "Definir como telefone principal"}
                    >
                      {telefone.primary ? (
                        <IconStarFilled className="size-3.5 text-yellow-500" />
                      ) : (
                        <IconStar className="size-3.5" />
                      )}
                    </Button>

                    <Input
                      placeholder="Insira o número de telefone"
                      value={telefone.value}
                      onChange={(e) => handleEditTelefoneChange(index, e.target.value)}
                      className="flex-1 h-8 text-sm"
                    />

                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="size-7 shrink-0"
                      onClick={() => removeEditTelefone(index)}
                      disabled={editTelefones.length <= 1}
                      title="Remover telefone"
                    >
                      <IconTrash className="size-3.5 text-destructive" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Emails */}
            <div className="space-y-3 mt-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm">Emails</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addEditEmail}
                  className="h-8 px-3 text-xs"
                >
                  <IconPlus className="size-3.5 mr-1" />
                  Adicionar
                </Button>
              </div>

              <div className="space-y-2 bg-muted/20 p-3 rounded-md">
                {editEmails.map((email, index) => (
                  <div key={`edit-email-${index}`} className="flex items-center gap-2 bg-background p-1.5 rounded-md border border-border/40">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="size-7 shrink-0"
                      onClick={() => setEditPrimaryEmail(index)}
                      title={email.primary ? "Email principal" : "Definir como email principal"}
                    >
                      {email.primary ? (
                        <IconStarFilled className="size-3.5 text-yellow-500" />
                      ) : (
                        <IconStar className="size-3.5" />
                      )}
                    </Button>

                    <Input
                      placeholder="Insira o endereço de email"
                      value={email.value}
                      onChange={(e) => handleEditEmailChange(index, e.target.value)}
                      className="flex-1 h-8 text-sm"
                    />

                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="size-7 shrink-0"
                      onClick={() => removeEditEmail(index)}
                      disabled={editEmails.length <= 1}
                      title="Remover email"
                    >
                      <IconTrash className="size-3.5 text-destructive" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              onClick={confirmEdit}
              disabled={!editNome.trim() || editTelefones.every(t => !t.value.trim()) || editEmails.every(e => !e.value.trim())}
            >
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
