{"indexes": [{"collectionGroup": "condutores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "dataHoraInicio", "order": "DESCENDING"}]}, {"collectionGroup": "condutores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "dataHoraInicio", "order": "DESCENDING"}]}, {"collectionGroup": "registos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "registos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "responsavelId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "registos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "tipoRegisto", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "registos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "nppNuipc", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "registos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "local", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "registos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "tipoCrime", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "identificacoes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "identificacoes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "responsavelId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "formularios", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "uploadedAt", "order": "DESCENDING"}]}, {"collectionGroup": "activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teamId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}