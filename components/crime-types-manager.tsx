"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { IconTrash } from "@tabler/icons-react";
import { SearchBar } from "@/components/search-bar";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth-context";
import {
  CrimeType,
  listenToCrimeTypes,
  addCrimeType,
  deleteCrimeType,
  initializeCommonCrimeTypes,
  syncCrimeTypeCounts
} from "@/services/crime-types-service";

interface CrimeTypesManagerProps {
  isAddModalOpen: boolean;
  setIsAddModalOpen: (open: boolean) => void;
}

export function CrimeTypesManager({ isAddModalOpen, setIsAddModalOpen }: CrimeTypesManagerProps) {
  const { user } = useAuth();
  const [crimeTypes, setCrimeTypes] = useState<CrimeType[]>([]);
  const [filteredCrimeTypes, setFilteredCrimeTypes] = useState<CrimeType[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [crimeTypeToDelete, setCrimeTypeToDelete] = useState<CrimeType | null>(null);
  const [newCrimeName, setNewCrimeName] = useState("");
  const [newCrimeCategory, setNewCrimeCategory] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Carregar tipos de crime
  useEffect(() => {
    const unsubscribe = listenToCrimeTypes((data) => {
      setCrimeTypes(data);
      setFilteredCrimeTypes(data);
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Filtrar tipos de crime quando o termo de pesquisa mudar
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredCrimeTypes(crimeTypes);
      return;
    }

    const lowerSearchTerm = searchTerm.toLowerCase();
    const filtered = crimeTypes.filter(
      (crimeType) =>
        crimeType.name.toLowerCase().includes(lowerSearchTerm) ||
        (crimeType.category && crimeType.category.toLowerCase().includes(lowerSearchTerm))
    );

    setFilteredCrimeTypes(filtered);
  }, [searchTerm, crimeTypes]);

  // Adicionar novo tipo de crime
  const handleAddCrimeType = async () => {
    if (!newCrimeName.trim() || !user) return;

    setIsSubmitting(true);
    try {
      await addCrimeType({
        name: newCrimeName.trim(),
        category: newCrimeCategory === "sem_categoria" ? undefined : newCrimeCategory,
        createdBy: user.uid,
      });

      setNewCrimeName("");
      setNewCrimeCategory("");
      setIsAddModalOpen(false);
      toast.success("Tipo de crime adicionado com sucesso");
    } catch (error) {
      console.error("Erro ao adicionar tipo de crime:", error);
      toast.error("Erro ao adicionar tipo de crime");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Confirmar remoção de tipo de crime
  const handleConfirmDelete = async () => {
    if (!crimeTypeToDelete) return;

    setIsSubmitting(true);
    try {
      await deleteCrimeType(crimeTypeToDelete.id);
      toast.success("Tipo de crime removido com sucesso");
      setShowDeleteDialog(false);
      setCrimeTypeToDelete(null);
    } catch (error) {
      console.error("Erro ao remover tipo de crime:", error);
      toast.error("Erro ao remover tipo de crime");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Abrir diálogo de remoção
  const handleOpenDeleteDialog = (crimeType: CrimeType) => {
    setCrimeTypeToDelete(crimeType);
    setShowDeleteDialog(true);
  };

  // Função para pesquisar
  const handleSearch = (query: string) => {
    setSearchTerm(query);
  };

  // Sincronizar contadores de tipos de crime com o uso real
  const handleSyncCounts = useCallback(async (silent = false) => {
    if (!user) return;

    try {
      const result = await syncCrimeTypeCounts();
      if (!silent && result.updated > 0) {
        toast.success(`${result.updated} contadores de tipos de crime foram sincronizados com o uso real`);
      } else if (!silent) {
        toast.info("Todos os contadores já estão sincronizados");
      }
    } catch (error) {
      console.error("Erro ao sincronizar contadores de tipos de crime:", error);
      if (!silent) {
        toast.error("Erro ao sincronizar contadores de tipos de crime");
      }
    }
  }, [user]);

  // Inicializar tipos de crime comuns quando o componente for montado
  useEffect(() => {
    if (user) {
      // Inicializar tipos de crime comuns silenciosamente
      initializeCommonCrimeTypes(user.uid, false, true).catch(error => {
        console.error("Erro ao inicializar tipos de crime:", error);
      });
    }
  }, [user]);

  // Sincronizar contadores automaticamente quando o componente é montado
  useEffect(() => {
    if (user) {
      handleSyncCounts(true);
    }
  }, [user, handleSyncCounts]);

  return (
    <>
      <div className="mb-6">
        <SearchBar
          onSearch={handleSearch}
          placeholder="Pesquisar tipos de crime..."
          className="max-w-full sm:max-w-md"
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        </div>
      ) : filteredCrimeTypes.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          Nenhum tipo de crime encontrado
        </div>
      ) : (
        <div className="relative flex flex-col gap-4">
          <div className="overflow-hidden rounded-lg border">
            <Table className="w-full">
              <TableHeader className="bg-muted sticky top-0 z-10">
                <TableRow>
                  <TableHead className="w-[40%]">Nome</TableHead>
                  <TableHead className="w-[35%]">Categoria</TableHead>
                  <TableHead className="w-[15%] text-center">Utilizações</TableHead>
                  <TableHead className="w-[10%] text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="**:data-[slot=table-cell]:first:w-8">
                {filteredCrimeTypes.map((crimeType) => (
                  <TableRow key={crimeType.id} className="hover:bg-muted/50">
                    <TableCell className="font-medium">
                      <div className="truncate" title={crimeType.name}>{crimeType.name}</div>
                    </TableCell>
                    <TableCell>
                      {crimeType.category ? (
                        <Badge variant="outline">{crimeType.category}</Badge>
                      ) : (
                        <span className="text-muted-foreground text-sm">Sem categoria</span>
                      )}
                    </TableCell>
                    <TableCell className="text-center">{crimeType.count}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleOpenDeleteDialog(crimeType)}
                        title={crimeType.count > 0 ? "Remover tipo de crime em uso (requer confirmação adicional)" : "Remover tipo de crime"}
                      >
                        <IconTrash className="size-4 text-destructive" />
                        <span className="sr-only">Remover</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      <div className="flex justify-between mt-4">
        <div className="text-sm text-muted-foreground">
          Total: {filteredCrimeTypes.length} tipos de crime
          {searchTerm && ` (filtrado de ${crimeTypes.length})`}
        </div>
        <div className="text-sm text-muted-foreground">
          Nota: Remover tipos de crime em uso requer confirmação adicional
        </div>
      </div>

      {/* Diálogo para adicionar novo tipo de crime */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar Tipo de Crime</DialogTitle>
            <DialogDescription>
              Preencha os campos abaixo para adicionar um novo tipo de crime ao sistema.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div>
              <label className="text-sm font-medium mb-1.5 block">Nome do Tipo de Crime:</label>
              <Input
                value={newCrimeName}
                onChange={(e) => setNewCrimeName(e.target.value)}
                placeholder="Introduza o nome do tipo de crime"
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-1.5 block">Categoria (opcional):</label>
              <Select value={newCrimeCategory} onValueChange={setNewCrimeCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sem_categoria">Sem categoria</SelectItem>
                  <SelectItem value="Crimes contra as pessoas">Crimes contra as pessoas</SelectItem>
                  <SelectItem value="Crimes contra o património">Crimes contra o património</SelectItem>
                  <SelectItem value="Crimes contra a vida em sociedade">Crimes contra a vida em sociedade</SelectItem>
                  <SelectItem value="Crimes contra o Estado">Crimes contra o Estado</SelectItem>
                  <SelectItem value="Outros crimes">Outros crimes</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>Cancelar</Button>
            <Button
              onClick={handleAddCrimeType}
              disabled={isSubmitting || !newCrimeName.trim()}
            >
              {isSubmitting ? "A adicionar..." : "Adicionar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para confirmar remoção */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Remoção</DialogTitle>
            <DialogDescription>
              {crimeTypeToDelete && crimeTypeToDelete.count > 0 ? (
                <span className="text-amber-500 font-medium">
                  Atenção: Este tipo de crime está em uso em {crimeTypeToDelete.count} registos.
                  Removê-lo pode causar inconsistências nos dados.
                </span>
              ) : (
                "Tem a certeza que deseja remover este tipo de crime? Esta ação não pode ser desfeita."
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <p className="font-medium">{crimeTypeToDelete?.name}</p>
            {crimeTypeToDelete?.category && (
              <p className="text-sm text-muted-foreground">
                Categoria: {crimeTypeToDelete.category}
              </p>
            )}

            {crimeTypeToDelete && crimeTypeToDelete.count > 0 && (
              <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
                <p className="text-sm text-amber-800">
                  Este tipo de crime está a ser usado em {crimeTypeToDelete.count} registos.
                  Se o remover, esses registos ainda manterão o nome do crime, mas não estarão
                  associados a um tipo de crime válido no sistema.
                </p>
                <p className="text-sm text-amber-800 mt-2 font-medium">
                  Tem a certeza que deseja continuar?
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>Cancelar</Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
              disabled={isSubmitting}
            >
              {isSubmitting ? "A remover..." : crimeTypeToDelete && crimeTypeToDelete.count > 0 ? "Sim, remover mesmo assim" : "Remover"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
