"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { IconCalendar, IconFilter, IconX } from "@tabler/icons-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { RelatorioFilterOptions } from "@/app/(protected)/relatorios/client-page";
import { Team } from "@/types/team";

interface RelatorioFilterProps {
  onFilter: (options: RelatorioFilterOptions) => void;
  initialFilters?: RelatorioFilterOptions;
  isAdmin?: boolean;
  teams?: Team[];
}

export function RelatorioFilter({
  onFilter,
  initialFilters,
  isAdmin = false,
  teams = [],
}: RelatorioFilterProps) {
  // Estado único para todos os filtros
  const [filters, setFilters] = useState<RelatorioFilterOptions>({
    dataInicio: null,
    dataFim: null,
    teamId: "all",
  });

  // Estado para controlar se há filtros ativos
  const [isFiltering, setIsFiltering] = useState(false);

  // Efeito para atualizar os filtros quando o componente pai envia novos filtros
  useEffect(() => {
    if (initialFilters) {
      const newFilters = {
        dataInicio: initialFilters.dataInicio,
        dataFim: initialFilters.dataFim,
        teamId: initialFilters.teamId || "all",
      };

      setFilters(newFilters);

      // Verificar se há filtros ativos
      const isActive = Boolean(
        initialFilters.dataInicio !== null ||
        initialFilters.dataFim !== null ||
        (initialFilters.teamId && initialFilters.teamId !== "all")
      );

      setIsFiltering(isActive);

      // Não chamamos onFilter aqui para evitar loops infinitos
      // Os filtros iniciais já são aplicados no componente pai
    }
  }, [initialFilters]);

  // Função para aplicar os filtros
  const aplicarFiltros = () => {
    // Verificar se há filtros ativos
    const isActive = Boolean(
      filters.dataInicio !== null ||
      filters.dataFim !== null ||
      filters.teamId !== "all"
    );

    // Atualizar o estado local
    setIsFiltering(isActive);

    // Criar uma cópia dos filtros para evitar problemas de referência
    const currentFilters = {
      dataInicio: filters.dataInicio,
      dataFim: filters.dataFim,
      teamId: filters.teamId
    };

    // Notificar o componente pai com os filtros atualizados
    onFilter(currentFilters);
  };

  // Função para atualizar um valor de filtro
  const updateFilterValue = (key: keyof RelatorioFilterOptions, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // Verificar se há filtros ativos
    const isActive = Boolean(
      (key === "dataInicio" ? value : filters.dataInicio) !== null ||
      (key === "dataFim" ? value : filters.dataFim) !== null ||
      filters.teamId !== "all"
    );

    setIsFiltering(isActive);

    // Não aplicamos os filtros automaticamente para evitar loops infinitos
    // O usuário precisa clicar em "Aplicar Filtros" para aplicar as mudanças
  };

  // Função para limpar os filtros
  const limparFiltros = () => {
    // Criar um objeto com os filtros completamente resetados
    const resetFilters = {
      dataInicio: null,
      dataFim: null,
      teamId: "all",
    };

    // Atualizar o estado local
    setFilters(resetFilters);
    setIsFiltering(false);

    // Notificar o componente pai com os filtros limpos
    // Usar uma cópia explícita para garantir que os valores sejam passados corretamente
    const cleanFilters = {
      dataInicio: null,
      dataFim: null,
      teamId: "all",
    };

    // Chamar a função de filtro com os valores limpos
    onFilter(cleanFilters);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {/* Data de Início */}
        <div className="w-full">
          <label className="text-sm font-medium mb-1.5 block">Data de Início</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <IconCalendar className="mr-2 h-4 w-4" />
                {filters.dataInicio ? (
                  format(filters.dataInicio, "dd/MM/yyyy", { locale: ptBR })
                ) : (
                  <span>Selecione uma data</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={filters.dataInicio || undefined}
                onSelect={(date) => updateFilterValue("dataInicio", date)}
                initialFocus
                locale={ptBR}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Data de Fim */}
        <div className="w-full">
          <label className="text-sm font-medium mb-1.5 block">Data de Fim</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <IconCalendar className="mr-2 h-4 w-4" />
                {filters.dataFim ? (
                  format(filters.dataFim, "dd/MM/yyyy", { locale: ptBR })
                ) : (
                  <span>Selecione uma data</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={filters.dataFim || undefined}
                onSelect={(date) => updateFilterValue("dataFim", date)}
                initialFocus
                locale={ptBR}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Filtro por Equipa (apenas para administradores) */}
        {isAdmin && (
          <div className="w-full">
            <label className="text-sm font-medium mb-1.5 block">Equipa</label>
            <Select
              value={filters.teamId}
              onValueChange={(value) => {
                updateFilterValue("teamId", value);
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Todas as equipas" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as equipas</SelectItem>
                <SelectItem value="admin">Administradores</SelectItem>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-end gap-2 mt-4">
        <Button
          variant="outline"
          onClick={limparFiltros}
          className="w-full sm:w-auto shrink-0"
        >
          <IconX className="size-4 mr-2" />
          Limpar Filtros
        </Button>
        <Button
          onClick={aplicarFiltros}
          className="w-full sm:w-auto shrink-0"
        >
          <IconFilter className="size-4 mr-2" />
          Aplicar Filtros
        </Button>
      </div>
    </div>
  );
}
