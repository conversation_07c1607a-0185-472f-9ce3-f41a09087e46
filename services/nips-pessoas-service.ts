import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe
} from "firebase/firestore";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

export interface NIPPessoa {
  id: string;
  nomeCompleto: string;
  numero: string;
  observacoes?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

const COLLECTION_NAME = "nips-pessoas";

// Adicionar um novo NIP Pessoa
export async function addNIPPessoa(nipData: Omit<NIPPessoa, "id" | "createdAt" | "updatedAt">): Promise<string> {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...nipData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de criação
    if (nipData.createdBy) {
      const userInfo = await getUser(nipData.createdBy);
      await logCreationActivity(
        ActivityType.NIP_PESSOA_CREATED,
        "Novo NIP Pessoa",
        `Criou um novo NIP Pessoa: ${nipData.nomeCompleto}`,
        nipData.createdBy,
        userInfo?.teamId,
        {
          nomeCompleto: nipData.nomeCompleto,
          numero: nipData.numero
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar NIP Pessoa:", error);
    throw error;
  }
}

// Obter todos os NIPs Pessoas
export async function getNIPsPessoas(): Promise<NIPPessoa[]> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as NIPPessoa));
  } catch (error) {
    console.error("Erro ao obter NIPs Pessoas:", error);
    throw error;
  }
}

// Atualizar um NIP Pessoa existente
export async function updateNIPPessoa(id: string, data: Partial<NIPPessoa>, userId?: string): Promise<boolean> {
  try {
    const nipRef = doc(db, COLLECTION_NAME, id);

    // Obter dados atuais do NIP Pessoa para registar a atividade
    const nipSnap = await getDoc(nipRef);
    const nipData = nipSnap.exists() ? nipSnap.data() : null;

    await updateDoc(nipRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de atualização
    if (userId && nipData) {
      const userInfo = await getUser(userId);
      await logUpdateActivity(
        ActivityType.NIP_PESSOA_UPDATED,
        "NIP Pessoa Atualizado",
        `Atualizou o NIP Pessoa: ${nipData.nomeCompleto}`,
        userId,
        userInfo?.teamId,
        {
          nomeCompleto: nipData.nomeCompleto,
          numero: nipData.numero
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao atualizar NIP Pessoa:", error);
    throw error;
  }
}

// Excluir um NIP Pessoa
export async function deleteNIPPessoa(id: string, userId: string): Promise<boolean> {
  try {
    // Obter dados do NIP Pessoa antes de excluir para registar a atividade
    const nipRef = doc(db, COLLECTION_NAME, id);
    const nipSnap = await getDoc(nipRef);
    const nipData = nipSnap.exists() ? nipSnap.data() : null;

    await deleteDoc(doc(db, COLLECTION_NAME, id));

    // Registar atividade de eliminação
    if (nipData) {
      const userInfo = await getUser(userId);
      await logDeletionActivity(
        ActivityType.NIP_PESSOA_DELETED,
        "NIP Pessoa Eliminado",
        `Eliminou o NIP Pessoa: ${nipData.nomeCompleto}`,
        userId,
        userInfo?.teamId,
        {
          nomeCompleto: nipData.nomeCompleto,
          numero: nipData.numero
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao excluir NIP Pessoa:", error);
    throw error;
  }
}

// Escutar mudanças em NIPs Pessoas em tempo real
export function listenToNIPsPessoas(callback: (nipsPessoas: NIPPessoa[]) => void): Unsubscribe {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("createdAt", "desc")
    );

    return onSnapshot(q, (querySnapshot) => {
      const nipsPessoas = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as NIPPessoa));

      callback(nipsPessoas);
    }, (error) => {
      console.error("Erro ao escutar NIPs Pessoas:", error);
    });
  } catch (error) {
    console.error("Erro ao configurar listener de NIPs Pessoas:", error);
    throw error;
  }
}
