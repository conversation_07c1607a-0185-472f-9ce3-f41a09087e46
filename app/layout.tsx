import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import { Suspense } from "react";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";
import { ThemeProvider } from "@/components/theme-provider";
import { LoadingProvider } from "./loading-provider";
import { RouteProgress } from "@/components/route-progress";
import { ClientToaster } from "@/components/client-toaster";
import { FocusCleaner } from "@/components/ui/focus-cleaner";


const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport: Viewport = {
  // A cor do tema será gerenciada dinamicamente pelo script theme-color.js
  // themeColor: "#3B82F6",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: "EPVS",
  description: "Sistema de gestão para a sua equipa",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "EPVS",
  },
  robots: {
    index: false,
    follow: false,
    googleBot: {
      index: false,
      follow: false,
      'max-image-preview': 'none',
      'max-snippet': -1,
      'max-video-preview': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt" suppressHydrationWarning>
      <head>
        <meta name="robots" content="noindex, nofollow" />
        <meta name="googlebot" content="noindex, nofollow" />
        {/* Viewport é gerenciado pelo Next.js através da exportação viewport */}
        <meta name="theme-color" content="#3B82F6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="EPVS" />
        <meta name="format-detection" content="telephone=no" />
        <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png" />
        <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#3B82F6" />
        <link rel="icon" type="image/png" href="/favicon.png" />
        <script src="/clear-cache.js" defer></script>
        <script src="/register-sw.js" defer></script>
        <script src="/theme-color.js" defer></script>
        <noscript>
          <meta http-equiv="refresh" content="0;url=/noscript.html" />
        </noscript>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem enableColorScheme={false} disableTransitionOnChange>
          <AuthProvider>
            <Suspense fallback={<div>Loading...</div>}>
              <LoadingProvider>
                <RouteProgress />
                {children}
                {/* Componente para gerenciar o foco e evitar problemas de acessibilidade */}
                <FocusCleaner />
              </LoadingProvider>
            </Suspense>
            {/* Componente cliente para o Toaster */}
            <ClientToaster />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
