/**
 * Utilitário para gerenciar notificações toast e evitar duplicatas
 */

import { toast, ToastT } from "sonner";

// Armazenar IDs de toasts recentes para evitar duplicatas
const recentToasts: Map<string, { id: string; timestamp: number }> = new Map();

// Tempo em ms para considerar um toast como duplicado (500ms por padrão)
const DUPLICATE_THRESHOLD = 500;

// Gerar uma chave única para o toast baseada no tipo e mensagem
function getToastKey(type: string, message: string, description?: string): string {
  return `${type}:${message}:${description || ""}`;
}

// Limpar toasts antigos do registro
function cleanupOldToasts() {
  const now = Date.now();

  for (const [key, data] of recentToasts.entries()) {
    if (now - data.timestamp > DUPLICATE_THRESHOLD) {
      recentToasts.delete(key);
    }
  }
}

// Interface para opções de toast
interface ToastOptions {
  description?: string;
  duration?: number;
  icon?: React.ReactNode;
  id?: string;
  important?: boolean;
  action?: {
    label: string;
    onClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
  };
  cancel?: {
    label: string;
    onClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
  };
  onDismiss?: (toast: ToastT) => void;
  onAutoClose?: (toast: ToastT) => void;
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right" | "top-center" | "bottom-center";
  unstyled?: boolean;
  className?: string;
  style?: React.CSSProperties;
  closeButton?: boolean;
  dismissible?: boolean;
}

// Função para mostrar toast de sucesso evitando duplicatas
export function showSuccessToast(message: string, options?: ToastOptions): string | number {
  cleanupOldToasts();

  // Remover a descrição para evitar redundância
  const newOptions = { ...options, description: undefined };

  const key = getToastKey("success", message);

  // Verificar se já existe um toast recente com a mesma mensagem
  if (recentToasts.has(key)) {
    return recentToasts.get(key)!.id;
  }

  // Criar novo toast
  const id = toast.success(message, newOptions);

  // Registrar o toast
  recentToasts.set(key, { id: id.toString(), timestamp: Date.now() });

  return id;
}

// Função para mostrar toast de erro evitando duplicatas
export function showErrorToast(message: string, options?: ToastOptions): string | number {
  cleanupOldToasts();

  // Remover a descrição para evitar redundância
  const newOptions = { ...options, description: undefined };

  const key = getToastKey("error", message);

  // Verificar se já existe um toast recente com a mesma mensagem
  if (recentToasts.has(key)) {
    return recentToasts.get(key)!.id;
  }

  // Criar novo toast
  const id = toast.error(message, newOptions);

  // Registrar o toast
  recentToasts.set(key, { id: id.toString(), timestamp: Date.now() });

  return id;
}

// Função para mostrar toast de informação evitando duplicatas
export function showInfoToast(message: string, options?: ToastOptions): string | number {
  cleanupOldToasts();

  // Remover a descrição para evitar redundância
  const newOptions = { ...options, description: undefined };

  const key = getToastKey("info", message);

  // Verificar se já existe um toast recente com a mesma mensagem
  if (recentToasts.has(key)) {
    return recentToasts.get(key)!.id;
  }

  // Criar novo toast
  const id = toast.info(message, newOptions);

  // Registrar o toast
  recentToasts.set(key, { id: id.toString(), timestamp: Date.now() });

  return id;
}

// Função para mostrar toast de aviso evitando duplicatas
export function showWarningToast(message: string, options?: ToastOptions): string | number {
  cleanupOldToasts();

  // Remover a descrição para evitar redundância
  const newOptions = { ...options, description: undefined };

  const key = getToastKey("warning", message);

  // Verificar se já existe um toast recente com a mesma mensagem
  if (recentToasts.has(key)) {
    return recentToasts.get(key)!.id;
  }

  // Criar novo toast
  const id = toast.warning(message, newOptions);

  // Registrar o toast
  recentToasts.set(key, { id: id.toString(), timestamp: Date.now() });

  return id;
}

// Função para mostrar toast de carregamento
export function showLoadingToast(message: string, options?: ToastOptions): string | number {
  // Não precisamos verificar duplicatas para toasts de carregamento
  // Remover a descrição para evitar redundância
  const newOptions = { ...options, description: undefined };
  return toast.loading(message, newOptions);
}

// Função para atualizar um toast existente (útil para progresso)
export function updateToast(toastId: string | number, message: string, options?: ToastOptions): void {
  try {
    // Primeiro, tentar dispensar o toast antigo
    toast.dismiss(toastId);

    // Criar um novo toast com a mensagem atualizada
    // Nota: Infelizmente, a biblioteca Sonner não suporta atualização direta de toasts
    // então precisamos dispensar e recriar
    const newOptions = { ...options, description: undefined };
    toast.loading(message, newOptions);
  } catch (error) {
    console.warn("Erro ao atualizar toast:", error);
    // Em caso de erro, criar um novo toast
    showLoadingToast(message, options);
  }
}

// Função para dispensar um toast
export function dismissToast(toastId: string | number) {
  try {
    toast.dismiss(toastId);
  } catch (error) {
    console.warn("Erro ao dispensar toast:", error);
  }
}

// Função para dispensar todos os toasts
export function dismissAllToasts() {
  try {
    toast.dismiss();
  } catch (error) {
    console.warn("Erro ao dispensar todos os toasts:", error);
  }
}

// Exportar funções originais do toast para casos onde queremos o comportamento padrão
export { toast };
