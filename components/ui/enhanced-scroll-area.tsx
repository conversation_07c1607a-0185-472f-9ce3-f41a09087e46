"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface EnhancedScrollAreaProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The scrollbar behavior - 'hover' shows only on hover, 'always' shows always, 'auto' uses browser default
   */
  scrollbarBehavior?: 'hover' | 'always' | 'auto'
  /**
   * The scrollbar size - 'thin', 'normal', or 'thick'
   */
  scrollbarSize?: 'thin' | 'normal' | 'thick'
  /**
   * Whether to use smooth scrolling
   */
  smoothScroll?: boolean
  /**
   * Whether to contain overscroll behavior
   */
  overscrollContain?: boolean
}

export function EnhancedScrollArea({
  className,
  children,
  scrollbarBehavior = 'hover',
  scrollbarSize = 'thin',
  smoothScroll = true,
  overscrollContain = true,
  ...props
}: EnhancedScrollAreaProps) {
  const scrollAreaRef = React.useRef<HTMLDivElement>(null)

  // Handle scroll behavior classes
  const scrollClasses = cn(
    "overflow-auto",
    smoothScroll && "scroll-smooth",
    overscrollContain && "overscroll-contain",
    // Scrollbar behavior classes
    scrollbarBehavior === 'hover' && "enhanced-scrollbar-hover",
    scrollbarBehavior === 'always' && "enhanced-scrollbar-always",
    scrollbarBehavior === 'auto' && "enhanced-scrollbar-auto",
    // Scrollbar size classes
    scrollbarSize === 'thin' && "enhanced-scrollbar-thin",
    scrollbarSize === 'normal' && "enhanced-scrollbar-normal",
    scrollbarSize === 'thick' && "enhanced-scrollbar-thick",
    className
  )

  return (
    <div
      ref={scrollAreaRef}
      className={scrollClasses}
      {...props}
    >
      {children}
    </div>
  )
}

// Hook for programmatic scrolling
export function useEnhancedScroll(ref: React.RefObject<HTMLElement>) {
  const scrollToTop = React.useCallback(() => {
    if (ref.current) {
      ref.current.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [ref])

  const scrollToBottom = React.useCallback(() => {
    if (ref.current) {
      ref.current.scrollTo({ top: ref.current.scrollHeight, behavior: 'smooth' })
    }
  }, [ref])

  const scrollToElement = React.useCallback((selector: string) => {
    if (ref.current) {
      const element = ref.current.querySelector(selector)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
      }
    }
  }, [ref])

  const scrollBy = React.useCallback((options: { top?: number; left?: number }) => {
    if (ref.current) {
      ref.current.scrollBy({ ...options, behavior: 'smooth' })
    }
  }, [ref])

  return {
    scrollToTop,
    scrollToBottom,
    scrollToElement,
    scrollBy,
  }
}
