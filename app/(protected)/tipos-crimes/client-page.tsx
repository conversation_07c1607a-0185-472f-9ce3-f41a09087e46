"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { CrimeTypesManager } from "@/components/crime-types-manager";
import { Button } from "@/components/ui/button";
import { IconPlus } from "@tabler/icons-react";

export default function ClientPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Redirecionar para login se não estiver autenticado
  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    }
  }, [user, loading, router]);

  // Se estiver carregando ou não houver usuário, mostrar tela de carregamento
  if (loading || !user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      <div className="px-4 lg:px-6">
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="hidden sm:block">
            <h1 className="text-2xl font-bold tracking-tight">Tipos de Crimes</h1>
            <p className="text-muted-foreground mt-2">
              Gerencie os tipos de crimes disponíveis no sistema
            </p>
          </div>
          <Button onClick={() => setIsAddModalOpen(true)} className="shrink-0">
            <IconPlus className="size-4 mr-2" />
            Adicionar Tipo de Crime
          </Button>
        </div>

        <CrimeTypesManager
          isAddModalOpen={isAddModalOpen}
          setIsAddModalOpen={setIsAddModalOpen}
        />
      </div>
    </div>
  );
}
