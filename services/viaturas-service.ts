import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe,
  where,
  getDoc
} from "firebase/firestore";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

export interface Viatura {
  id: string;
  matricula: string;
  modelo: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

const COLLECTION_NAME = "viaturas";

// Adicionar uma nova viatura
export async function addViatura(viaturaData: Omit<Viatura, "id" | "createdAt" | "updatedAt">): Promise<string> {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...viaturaData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de criação
    if (viaturaData.createdBy) {
      const userInfo = await getUser(viaturaData.createdBy);
      await logCreationActivity(
        ActivityType.VIATURA_CREATED,
        "Nova Viatura",
        `Criou uma nova viatura: ${viaturaData.matricula} - ${viaturaData.modelo}`,
        viaturaData.createdBy,
        userInfo?.teamId,
        {
          matricula: viaturaData.matricula,
          modelo: viaturaData.modelo
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar viatura:", error);
    throw error;
  }
}

// Obter todas as viaturas
export async function getViaturas(): Promise<Viatura[]> {
  try {
    const q = query(collection(db, COLLECTION_NAME), orderBy("matricula", "asc"));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        matricula: data.matricula || "",
        modelo: data.modelo || "",
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()) : new Date(),
        updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()) : new Date(),
        createdBy: data.createdBy || "",
      } as Viatura;
    });
  } catch (error) {
    console.error("Erro ao obter viaturas:", error);
    throw error;
  }
}

// Obter uma viatura específica
export async function getViatura(id: string): Promise<Viatura | null> {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        matricula: data.matricula || "",
        modelo: data.modelo || "",
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()) : new Date(),
        updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()) : new Date(),
        createdBy: data.createdBy || "",
      } as Viatura;
    }

    return null;
  } catch (error) {
    console.error("Erro ao obter viatura:", error);
    throw error;
  }
}

// Atualizar uma viatura existente
export async function updateViatura(id: string, viaturaData: Partial<Omit<Viatura, "id" | "createdAt" | "updatedAt">>, userId?: string): Promise<void> {
  try {
    // Obter dados atuais da viatura para registar a atividade
    const viaturaRef = doc(db, COLLECTION_NAME, id);
    const viaturaSnap = await getDoc(viaturaRef);
    const currentViaturaData = viaturaSnap.exists() ? viaturaSnap.data() : null;

    await updateDoc(viaturaRef, {
      ...viaturaData,
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de atualização
    if (userId && currentViaturaData) {
      const userInfo = await getUser(userId);
      await logUpdateActivity(
        ActivityType.VIATURA_UPDATED,
        "Viatura Atualizada",
        `Atualizou a viatura: ${currentViaturaData.matricula} - ${currentViaturaData.modelo}`,
        userId,
        userInfo?.teamId,
        {
          matricula: currentViaturaData.matricula,
          modelo: currentViaturaData.modelo
        }
      );
    }
  } catch (error) {
    console.error("Erro ao atualizar viatura:", error);
    throw error;
  }
}

// Excluir uma viatura
export async function deleteViatura(id: string, userId?: string): Promise<void> {
  try {
    // Obter dados da viatura antes de excluir para registar a atividade
    const viaturaRef = doc(db, COLLECTION_NAME, id);
    const viaturaSnap = await getDoc(viaturaRef);
    const viaturaData = viaturaSnap.exists() ? viaturaSnap.data() : null;

    await deleteDoc(viaturaRef);

    // Registar atividade de eliminação
    if (userId && viaturaData) {
      const userInfo = await getUser(userId);
      await logDeletionActivity(
        ActivityType.VIATURA_DELETED,
        "Viatura Eliminada",
        `Eliminou a viatura: ${viaturaData.matricula} - ${viaturaData.modelo}`,
        userId,
        userInfo?.teamId,
        {
          matricula: viaturaData.matricula,
          modelo: viaturaData.modelo
        }
      );
    }
  } catch (error) {
    console.error("Erro ao excluir viatura:", error);
    throw error;
  }
}

// Verificar se uma matrícula já existe no sistema
export async function checkMatriculaExists(matricula: string, excludeId?: string): Promise<boolean> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where("matricula", "==", matricula.toUpperCase())
    );
    const querySnapshot = await getDocs(q);

    // Se estamos editando, excluir o próprio registro da verificação
    if (excludeId) {
      return querySnapshot.docs.some(doc => doc.id !== excludeId);
    }

    return !querySnapshot.empty;
  } catch (error) {
    console.error("Erro ao verificar matrícula:", error);
    throw error;
  }
}

// Configurar um listener para mudanças nas viaturas
export function listenToViaturas(callback: (viaturas: Viatura[]) => void): Unsubscribe {
  const q = query(collection(db, COLLECTION_NAME), orderBy("matricula", "asc"));

  return onSnapshot(q, (querySnapshot) => {
    const viaturas = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        matricula: data.matricula || "",
        modelo: data.modelo || "",
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()) : new Date(),
        updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()) : new Date(),
        createdBy: data.createdBy || "",
      } as Viatura;
    });

    callback(viaturas);
  }, (error) => {
    console.error("Erro ao escutar viaturas:", error);
  });
}
