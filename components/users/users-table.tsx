"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { IconDotsVertical, IconEdit, IconUserShield, IconUserEdit, IconTrash, IconMail } from "@tabler/icons-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { UserRole } from "@/types/team";

interface User {
  id: string;
  name: string;
  email?: string;
  registrationNumber?: string;
  category?: string;
  role?: UserRole;
  teamId?: string;
  teamName?: string;
}

interface UsersTableProps {
  users: User[];
  onEditRole: (user: User) => void;
  onEditProfile: (user: User) => void;
  onDelete?: (user: User) => void;
  onSendPasswordReset?: (email: string) => void;
  teams?: { id: string; name: string }[];
}

export function UsersTable({ users, onEditRole, onEditProfile, onDelete, onSendPasswordReset, teams = [] }: UsersTableProps) {
  // Função para obter o nome da equipe pelo ID
  const getTeamName = (teamId?: string) => {
    if (!teamId) return "Sem equipa";
    const team = teams.find(t => t.id === teamId);
    return team ? team.name : "Equipa desconhecida";
  };
  // Função para obter o texto do papel do usuário
  const getRoleText = (role?: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "Administrador";
      case UserRole.TEAM_LEADER:
        return "Líder de Equipa";
      case UserRole.TEAM_MEMBER:
        return "Membro de Equipa";
      default:
        return "Sem papel";
    }
  };

  // Função para obter a variante do badge do papel
  const getRoleBadgeVariant = (role?: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "destructive";
      case UserRole.TEAM_LEADER:
        return "default";
      case UserRole.TEAM_MEMBER:
        return "secondary";
      default:
        return "outline";
    }
  };

  return (
    <div className="relative flex flex-col gap-4">
      <div className="overflow-hidden rounded-lg border">
        <Table className="w-full">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[25%]">Utilizador</TableHead>
              <TableHead className="w-[12%]">Matrícula</TableHead>
              <TableHead className="w-[18%]">Categoria</TableHead>
              <TableHead className="w-[15%]">Papel</TableHead>
              <TableHead className="w-[20%]">Equipa</TableHead>
              <TableHead className="w-[10%] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center text-muted-foreground">
                  Nenhum utilizador encontrado.
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id} className="hover:bg-muted/50">
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="size-8">
                        <AvatarFallback>
                          {user.name.split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium truncate">{user.name}</div>
                        {user.email && (
                          <div className="text-sm text-muted-foreground truncate">{user.email}</div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{user.registrationNumber || "N/A"}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{user.category || "N/A"}</div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role) as any}>
                      {getRoleText(user.role)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">
                      {user.teamId ? (
                        <Badge variant="outline" className="font-normal">
                          {getTeamName(user.teamId)}
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground text-sm">Sem equipa</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem onClick={() => onEditProfile(user)}>
                          <IconUserEdit className="mr-2 size-4" />
                          Editar Perfil
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEditRole(user)}>
                          <IconUserShield className="mr-2 size-4" />
                          Editar Papel
                        </DropdownMenuItem>
                        {onSendPasswordReset && user.email && (
                          <DropdownMenuItem
                            onClick={() => onSendPasswordReset(user.email || "")}
                          >
                            <IconMail className="mr-2 size-4" />
                            Reenviar Email
                          </DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem
                            onClick={() => onDelete(user)}
                            className="text-destructive focus:text-destructive"
                          >
                            <IconTrash className="mr-2 size-4" />
                            Excluir Utilizador
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
