"use client";

import { useState, useCallback, useEffect } from "react";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useAuth } from "@/contexts/auth-context";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Viatura, checkMatriculaExists } from "@/services/viaturas-service";

// Schema para validação do formulário
const viaturaSchema = z.object({
  matricula: z.string().min(1, { message: "A matrícula é obrigatória" }),
  modelo: z.string().min(1, { message: "O modelo é obrigatório" }),
});

type ViaturaFormValues = z.infer<typeof viaturaSchema>;

interface ViaturaFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSaveComplete: (viaturaData: Omit<Viatura, "id" | "createdAt" | "updatedAt">) => void;
  initialData?: Viatura;
}

export function ViaturaForm({ open, onOpenChange, onSaveComplete, initialData }: ViaturaFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [matriculaError, setMatriculaError] = useState<string>("");
  const [isCheckingMatricula, setIsCheckingMatricula] = useState(false);
  const { user } = useAuth();

  // Inicializar o formulário com react-hook-form
  const form = useForm<ViaturaFormValues>({
    resolver: zodResolver(viaturaSchema),
    defaultValues: initialData
      ? {
          matricula: initialData.matricula,
          modelo: initialData.modelo,
        }
      : {
          matricula: "",
          modelo: "",
        },
    mode: "onChange", // Validar ao alterar os campos
  });

  // Função para verificar se a matrícula já existe (com debounce)
  const checkMatriculaUniqueness = useCallback(
    async (matricula: string) => {
      if (!matricula.trim()) {
        setMatriculaError("");
        return;
      }

      setIsCheckingMatricula(true);
      try {
        const exists = await checkMatriculaExists(matricula, initialData?.id);
        if (exists) {
          setMatriculaError("Esta matrícula já existe no sistema");
        } else {
          setMatriculaError("");
        }
      } catch (error) {
        console.error("Erro ao verificar matrícula:", error);
        setMatriculaError("");
      } finally {
        setIsCheckingMatricula(false);
      }
    },
    [initialData?.id]
  );

  // Debounce para a verificação de matrícula
  useEffect(() => {
    const matricula = form.watch("matricula");
    if (!matricula) {
      setMatriculaError("");
      return;
    }

    const timeoutId = setTimeout(() => {
      checkMatriculaUniqueness(matricula);
    }, 500); // 500ms de debounce

    return () => clearTimeout(timeoutId);
  }, [form.watch("matricula"), checkMatriculaUniqueness]);

  // Limpar erros quando o modal é fechado
  useEffect(() => {
    if (!open) {
      setMatriculaError("");
      setIsCheckingMatricula(false);
    }
  }, [open]);

  // Função para lidar com a submissão do formulário
  async function onSubmit(data: ViaturaFormValues) {
    if (!user) return;

    // Verificar se há erro de matrícula duplicada
    if (matriculaError) {
      showErrorToast("Corrija os erros antes de continuar");
      return;
    }

    const loadingToast = showLoadingToast(initialData ? "A atualizar viatura..." : "A adicionar viatura...");
    setIsSubmitting(true);

    try {
      // Preparar os dados da viatura
      const viaturaData: Omit<Viatura, "id" | "createdAt" | "updatedAt"> = {
        matricula: data.matricula.toUpperCase(),
        modelo: data.modelo,
        createdBy: user.uid,
      };

      // Notificar o componente pai sobre a viatura guardada
      await onSaveComplete(viaturaData);

      dismissToast(loadingToast);

      // Limpar o formulário apenas se não estiver editando
      if (!initialData) {
        form.reset();
      }

      // O componente pai é responsável por fechar o modal
    } catch (error: any) {
      dismissToast(loadingToast);
      showErrorToast(initialData ? "Erro ao atualizar viatura" : "Erro ao adicionar viatura");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{initialData ? "Editar Viatura" : "Nova Viatura"}</DialogTitle>
          <DialogDescription>
            {initialData
              ? "Edite os detalhes da viatura selecionada."
              : "Adicione uma nova viatura ao sistema."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
            <FormField
              control={form.control}
              name="matricula"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Matrícula</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ex: AA-00-AA"
                      {...field}
                      disabled={isSubmitting}
                      onChange={(e) => {
                        // Converter para maiúsculas em tempo real
                        const upperValue = e.target.value.toUpperCase();
                        field.onChange(upperValue);
                      }}
                      className={matriculaError ? "border-destructive" : ""}
                    />
                  </FormControl>
                  <FormMessage />
                  {/* Mostrar erro de matrícula duplicada */}
                  {matriculaError && (
                    <p className="text-sm font-medium text-destructive">
                      {matriculaError}
                    </p>
                  )}
                  {/* Mostrar indicador de verificação */}
                  {isCheckingMatricula && (
                    <p className="text-sm text-muted-foreground">
                      A verificar matrícula...
                    </p>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="modelo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Modelo</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ex: Renault Clio"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="mt-6">
              <Button
                variant="outline"
                type="button"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || !!matriculaError || isCheckingMatricula}
              >
                {isSubmitting ? "A guardar..." : initialData ? "Atualizar" : "Guardar"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
