"use client";

import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  IconFileText,
  IconUserShield,
} from "@tabler/icons-react";
import { Registo } from "@/services/registos-service";
import { Skeleton } from "@/components/ui/skeleton";

interface RelatorioRegistosSectionProps {
  registos: Registo[];
  isLoading: boolean;
}

export function RelatorioRegistosSection({ registos, isLoading }: RelatorioRegistosSectionProps) {
  // Função para obter o tipo de registo formatado
  const getTipoRegistoDisplay = (tipoRegisto: string) => {
    switch (tipoRegisto) {
      case "detencao":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800">
            <IconUserShield className="size-3.5 mr-1" />
            Detenção
          </Badge>
        );
      case "autoNoticia":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
            <IconFileText className="size-3.5 mr-1" />
            Auto de Notícia
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            Desconhecido
          </Badge>
        );
    }
  };

  // Renderizar esqueletos durante o carregamento
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={`skeleton-${index}`} className="@container/card flex flex-col">
            <CardHeader className="px-6 pb-3">
              <div className="flex justify-between items-start">
                <div className="flex flex-col gap-1.5">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-4 w-32" />
                </div>
                <Skeleton className="h-6 w-24" />
              </div>
            </CardHeader>
            <CardContent className="px-6 pt-0 flex-1">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex flex-col min-w-0">
                  <Skeleton className="h-3 w-16 mb-1" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <div className="flex flex-col min-w-0">
                  <Skeleton className="h-3 w-16 mb-1" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <div className="flex flex-col min-w-0">
                  <Skeleton className="h-3 w-16 mb-1" />
                  <Skeleton className="h-4 w-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div>
      {registos.length === 0 ? (
        <div className="text-center py-8 bg-muted/20 rounded-md">
          <p className="text-muted-foreground">Nenhum registo encontrado para o período selecionado.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {registos.map((registo) => (
            <Card key={registo.id} className="@container/card dark:*:data-[slot=card]:bg-card *:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs flex flex-col">
              <CardHeader className="px-6 pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex flex-col gap-1.5">
                    <CardTitle className="leading-none font-semibold">
                      {registo.nppNuipc}
                    </CardTitle>
                    <CardDescription className="text-sm">
                      {registo.dataRegisto} {registo.horaRegisto}
                    </CardDescription>
                  </div>
                  {getTipoRegistoDisplay(registo.tipoRegisto)}
                </div>
              </CardHeader>

              <CardContent className="px-6 pt-0 flex-1">
                <div className="grid grid-cols-1 gap-3">
                  <div className="flex flex-col min-w-0">
                    <span className="text-xs text-muted-foreground">Responsável</span>
                    <div className="flex items-center gap-1">
                      <span className="text-sm font-medium truncate max-w-full">{registo.responsavelNome}</span>
                      {registo.responsavelMatricula && (
                        <span className="text-xs text-muted-foreground whitespace-nowrap">[{registo.responsavelMatricula}]</span>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col w-full">
                    <span className="text-xs text-muted-foreground">Local</span>
                    <span className="text-sm break-words hyphens-auto">{registo.local}</span>
                  </div>

                  <div className="flex flex-col min-w-0">
                    <span className="text-xs text-muted-foreground">Tipo de Crime</span>
                    <span className="text-sm truncate max-w-full">{registo.tipoCrime}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
