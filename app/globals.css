@import 'tailwindcss';

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.585 0.233 277.117);
  --primary-to: oklch(0.511 0.262 276.966);
  --primary-foreground: oklch(0.962 0.018 272.314);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.183 0.006 285.79);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.183 0.006 285.79);
  --destructive: oklch(0.637 0.237 25.331);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.871 0.006 286.286);
  --ring: oklch(0.871 0.006 286.286);
  --chart-1: oklch(0.585 0.233 277.117);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.956 0.002 286.35);
  --sidebar-foreground: oklch(0.37 0.013 285.805);
  --sidebar-primary: oklch(0.673 0.182 276.935);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.871 0.006 286.286);
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
}
.dark {
  --background: oklch(0.183 0.006 285.79);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.37 0.013 285.805);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.274 0.006 286.033);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.585 0.233 277.117);
  --primary-to: oklch(0.511 0.262 276.966);
  --primary-foreground: oklch(0.962 0.018 272.314);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.21 0.006 285.885);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.21 0.006 285.885); /* Custom */
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.637 0.237 25.331);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.246 0.009 285.69); /* Custom */
  --input: oklch(0.246 0.009 285.69); /* Custom */
  --ring: oklch(0.442 0.017 285.786);
  --chart-1: oklch(0.585 0.233 277.117);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.967 0.001 286.375);
  --sidebar-primary: oklch(0.673 0.182 276.935);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.967 0.001 286.375);
  --sidebar-border: oklch(0.274 0.006 286.033);
  --sidebar-ring: oklch(0.442 0.017 285.786);
}

@theme inline {
  --font-sans:
    var(--font-sans), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-mono:
    var(--font-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    'Liberation Mono', 'Courier New', monospace;

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-primary: var(--primary);
  --color-primary-to: var(--primary-to);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);
  --color-chart-6: var(--chart-6);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-xs: 0 1px 1px rgba(0, 0, 0, 0.02), 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 1px rgba(0, 0, 0, 0.03), 0 2px 2px rgba(0, 0, 0, 0.03), 0 2px 4px rgba(0, 0, 0, 0.03);
  --shadow-md: 0 1px 1px rgba(0, 0, 0, 0.02), 0 2px 2px rgba(0, 0, 0, 0.02), 0 4px 4px rgba(0, 0, 0, 0.02), 0 8px 8px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 1px 1px rgba(0, 0, 0, 0.02), 0 2px 2px rgba(0, 0, 0, 0.02), 0 4px 4px rgba(0, 0, 0, 0.02), 0 8px 8px rgba(0, 0, 0, 0.02), 0 16px 16px rgba(0, 0, 0, 0.02);
  --shadow-xl: 0 1px 1px rgba(0, 0, 0, 0.02), 0 2px 2px rgba(0, 0, 0, 0.02), 0 4px 4px rgba(0, 0, 0, 0.02), 0 8px 8px rgba(0, 0, 0, 0.02), 0 16px 16px rgba(0, 0, 0, 0.02), 0 32px 32px rgba(0, 0, 0, 0.02);
  --shadow-2xl: 0 1px 1px rgba(0, 0, 0, 0.02), 0 2px 2px rgba(0, 0, 0, 0.02), 0 4px 4px rgba(0, 0, 0, 0.02), 0 8px 8px rgba(0, 0, 0, 0.02), 0 16px 16px rgba(0, 0, 0, 0.02), 0 32px 32px rgba(0, 0, 0, 0.02), 0 64px 64px rgba(0, 0, 0, 0.02);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-zinc-200, currentColor);
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Garantir que inputs de data e hora tenham a mesma largura que outros inputs */
  input[type="date"],
  input[type="time"],
  input[type="datetime-local"] {
    @apply w-full box-border;
    min-width: 0 !important;
    max-width: 100% !important;
  }
}

/* Estilos personalizados para notificações */
.toaster [data-sonner-toast] {
  font-weight: 500;
}

.toaster .description {
  opacity: 1;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--toast-text-normal, black);
  font-weight: 500;
}

/* Estilos específicos para descrições em cada tipo de toast */
.toaster [data-sonner-toast][data-type="success"] .description {
  color: var(--toast-text-success, black);
  font-weight: 500;
}

.toaster [data-sonner-toast][data-type="error"] .description {
  color: var(--toast-text-error, black);
  font-weight: 500;
}

.toaster [data-sonner-toast][data-type="info"] .description {
  color: var(--toast-text-info, black);
  font-weight: 500;
}

.toaster [data-sonner-toast][data-type="warning"] .description {
  color: var(--toast-text-warning, black);
  font-weight: 500;
}

.toaster [data-sonner-toast][data-type="loading"] .description {
  color: var(--toast-text-loading, black);
  font-weight: 500;
}

/* Garantir que o texto seja sempre visível no modo light */
:root:not(.dark) .toaster .description {
  color: black !important;
  font-weight: 500 !important;
}

/* Garantir que o título das notificações seja sempre visível no modo light */
:root:not(.dark) .toaster [data-sonner-toast] > div:first-of-type {
  color: black !important;
  font-weight: 600 !important;
}

/* Classe para desativar transições durante o logout */
.no-transitions * {
  transition: none !important;
  animation: none !important;
}

/* Custom Scrollbar Styles for Sidebar */
/* Webkit-based browsers (Chrome, Safari, Edge) */
[data-sidebar="content"]::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

[data-sidebar="content"]::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

[data-sidebar="content"]::-webkit-scrollbar-thumb {
  background: var(--sidebar-border);
  border-radius: 3px;
  opacity: 0;
  transition: all 0.2s ease-in-out;
}

[data-sidebar="content"]::-webkit-scrollbar-thumb:hover {
  background: var(--sidebar-accent);
}

/* Show scrollbar on hover */
[data-sidebar="content"]:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

/* Firefox */
[data-sidebar="content"] {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.2s ease-in-out;
}

[data-sidebar="content"]:hover {
  scrollbar-color: var(--sidebar-border) transparent;
}

/* Enhanced scrollbar for dark theme */
.dark [data-sidebar="content"]::-webkit-scrollbar-thumb {
  background: var(--sidebar-border);
}

.dark [data-sidebar="content"]::-webkit-scrollbar-thumb:hover {
  background: var(--sidebar-accent);
}

.dark [data-sidebar="content"]:hover {
  scrollbar-color: var(--sidebar-border) transparent;
}

/* Subtle scrollbar for mobile sheet content */
[data-mobile="true"] [data-slot="scroll-area-viewport"]::-webkit-scrollbar {
  width: 4px;
}

[data-mobile="true"] [data-slot="scroll-area-viewport"]::-webkit-scrollbar-track {
  background: transparent;
}

[data-mobile="true"] [data-slot="scroll-area-viewport"]::-webkit-scrollbar-thumb {
  background: var(--sidebar-border);
  border-radius: 2px;
  opacity: 0.6;
}

[data-mobile="true"] [data-slot="scroll-area-viewport"]::-webkit-scrollbar-thumb:hover {
  background: var(--sidebar-accent);
  opacity: 1;
}

/* Custom scrollbar for other scroll areas in the app */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
  transition: background 0.2s ease-in-out;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--muted);
}

/* Enhanced Scrollbar Classes */
/* Hover-only scrollbars */
.enhanced-scrollbar-hover::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.enhanced-scrollbar-hover::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.enhanced-scrollbar-hover::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out, background 0.2s ease-in-out;
}

.enhanced-scrollbar-hover:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

.enhanced-scrollbar-hover::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

.enhanced-scrollbar-hover {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.2s ease-in-out;
}

.enhanced-scrollbar-hover:hover {
  scrollbar-color: var(--border) transparent;
}

/* Always visible scrollbars */
.enhanced-scrollbar-always::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.enhanced-scrollbar-always::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

.enhanced-scrollbar-always::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
  transition: background 0.2s ease-in-out;
}

.enhanced-scrollbar-always::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

.enhanced-scrollbar-always {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--muted);
}

/* Auto scrollbars (browser default with subtle styling) */
.enhanced-scrollbar-auto {
  scrollbar-width: auto;
  scrollbar-color: var(--border) var(--background);
}

/* Scrollbar sizes */
.enhanced-scrollbar-thin::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
}

.enhanced-scrollbar-normal::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.enhanced-scrollbar-thick::-webkit-scrollbar {
  width: 12px !important;
  height: 12px !important;
}

/* Dark theme enhancements for enhanced scrollbars */
.dark .enhanced-scrollbar-hover::-webkit-scrollbar-thumb {
  background: var(--border);
}

.dark .enhanced-scrollbar-hover::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

.dark .enhanced-scrollbar-hover:hover {
  scrollbar-color: var(--border) transparent;
}

.dark .enhanced-scrollbar-always::-webkit-scrollbar-thumb {
  background: var(--border);
}

.dark .enhanced-scrollbar-always::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

.dark .enhanced-scrollbar-always {
  scrollbar-color: var(--border) var(--muted);
}
