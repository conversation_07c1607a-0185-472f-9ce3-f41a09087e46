"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removido import do SharedLayout
import { NIPsTable } from "@/components/nips-table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { IconPlus } from "@tabler/icons-react";
import { SearchBar } from "@/components/search-bar";
import { NIPForm } from "@/components/nip-form";
import {
  addNIP,
  getNIPs,
  updateNIP,
  deleteNIP,
  listenToNIPs,
  NIP
} from "@/services/nips-service";

export default function ClientPage() {
  const { user, loading, profileLoading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [nips, setNIPs] = useState<NIP[]>([]);
  const [filteredNIPs, setFilteredNIPs] = useState<NIP[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Carregar NIPs do Firestore
  const loadNIPs = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const data = await getNIPs();
      setNIPs(data);
      setFilteredNIPs(data);
    } catch (error) {
      console.error("Erro ao carregar NIPs:", error);
    } finally {
      setIsLoading(false);
    }
  }, [user, setNIPs, setFilteredNIPs, setIsLoading]);

  // Configurar listener em tempo real para NIPs
  useEffect(() => {
    // Se o usuário não estiver autenticado e não estiver carregando, redirecionar para o login
    if (!loading && !user) {
      router.push("/login");
    } else if (!loading && !profileLoading && user) {
      // Inicialmente, carregar NIPs normalmente para mostrar algo rapidamente
      loadNIPs();

      // Configurar listener em tempo real para atualizações
      const unsubscribe = listenToNIPs((updatedNIPs) => {
        setNIPs(updatedNIPs);
        setFilteredNIPs(updatedNIPs);
        setIsLoading(false);
      });

      // Limpar listener quando o componente for desmontado
      return () => {
        unsubscribe();
      };
    }
  }, [user, loading, profileLoading, router, loadNIPs]);

  // Efeito para filtrar NIPs com base na pesquisa
  useEffect(() => {
    if (!nips.length) return;

    if (!searchQuery.trim()) {
      setFilteredNIPs(nips);
      return;
    }

    const query = searchQuery.toLowerCase().trim();
    const filtered = nips.filter(nip =>
      nip.local.toLowerCase().includes(query) ||
      nip.numero.toLowerCase().includes(query)
    );

    setFilteredNIPs(filtered);
  }, [searchQuery, nips]);

  // Função para lidar com a pesquisa
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Manipular o salvamento de um NIP
  const handleSaveComplete = async (nipData: {
    local: string;
    numero: string;
    createdBy: string;
  }) => {
    try {
      // Adicionar o NIP ao Firestore
      const id = await addNIP(nipData);

      // Se a pesquisa estiver vazia, recarregar todos os NIPs
      // Caso contrário, apenas adicionar o novo NIP se ele corresponder à pesquisa
      if (!searchQuery.trim()) {
        loadNIPs();
      } else {
        // Verificar se o novo NIP corresponde à pesquisa atual
        const query = searchQuery.toLowerCase().trim();
        if (
          nipData.local.toLowerCase().includes(query) ||
          nipData.numero.toLowerCase().includes(query)
        ) {
          // Criar um novo NIP com o ID retornado
          const newNIP = { id, ...nipData } as NIP;

          // Adicionar o novo NIP às listas
          setNIPs(prev => [newNIP, ...prev]);
          setFilteredNIPs(prev => [newNIP, ...prev]);
        } else {
          // Apenas atualizar a lista completa sem afetar a lista filtrada
          const data = await getNIPs();
          setNIPs(data);
        }
      }

      // Fechar o modal
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Erro ao salvar NIP:", error);
    }
  };

  // Manipular a exclusão de um NIP
  const handleDelete = async (id: string) => {
    if (!user?.uid) {
      console.error("Usuário não autenticado");
      throw new Error("Usuário não autenticado");
    }

    try {
      await deleteNIP(id, user.uid);
      // Atualizar a lista local removendo o NIP excluído
      const updatedNIPs = nips.filter(n => n.id !== id);
      setNIPs(updatedNIPs);
      // Atualizar também a lista filtrada
      setFilteredNIPs(prev => prev.filter(n => n.id !== id));
    } catch (error) {
      console.error("Erro ao excluir NIP:", error);
      throw error;
    }
  };

  // Manipular a edição de um NIP
  const handleEdit = async (id: string, newLocal: string, newNumero: string) => {
    try {
      await updateNIP(id, { local: newLocal, numero: newNumero }, user?.uid);
      // Atualizar a lista local com os novos dados
      const updatedNIPs = nips.map(n =>
        n.id === id ? { ...n, local: newLocal, numero: newNumero } : n
      );
      setNIPs(updatedNIPs);

      // Atualizar também a lista filtrada
      setFilteredNIPs(prev =>
        prev.map(n => n.id === id ? { ...n, local: newLocal, numero: newNumero } : n)
      );
    } catch (error) {
      console.error("Erro ao atualizar NIP:", error);
      throw error;
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Nips Locais</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie os NIPs disponíveis no sistema
              </p>
            </div>
            <Button onClick={() => setIsAddModalOpen(true)} className="shrink-0">
              <IconPlus className="size-4 mr-2" />
              Adicionar NIP
            </Button>
          </div>

          {/* Barra de pesquisa */}
          <div className="mb-6">
            <SearchBar
              onSearch={handleSearch}
              placeholder="Pesquisar por local ou número..."
              className="max-w-full sm:max-w-md"
            />
          </div>

          {/* Seção de listagem */}
          <div className="mt-2">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              </div>
            ) : (
              <NIPsTable
                nips={filteredNIPs}
                onDelete={handleDelete}
                onEdit={handleEdit}
              />
            )}
          </div>

          {/* Modal de Adição */}
          <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
            <DialogContent className="sm:max-w-xl md:max-w-2xl w-[95vw]">
              <DialogHeader>
                <DialogTitle>Adicionar Novo NIP</DialogTitle>
              </DialogHeader>
              <NIPForm onSaveComplete={handleSaveComplete} />
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
