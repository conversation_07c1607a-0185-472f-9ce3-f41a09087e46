"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { IconDotsVertical, IconDownload, IconEdit, IconTrash } from "@tabler/icons-react";
import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";
import { formatFileSize, formatDate } from "@/lib/utils";

export interface Formulario {
  id: string;
  name: string;
  title: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: Date;
  uploadedBy: string;
  teamId?: string;
}

interface FormulariosTableProps {
  formularios: Formulario[];
  onDelete: (id: string) => Promise<void>;
  onEdit: (id: string, newTitle: string) => Promise<void>;
}

export function FormulariosTable({ formularios, onDelete, onEdit }: FormulariosTableProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedFormulario, setSelectedFormulario] = useState<Formulario | null>(null);
  const [newTitle, setNewTitle] = useState("");

  const handleDownload = (formulario: Formulario) => {
    // Criar um link temporário para download
    const link = document.createElement("a");
    link.href = formulario.url;
    link.target = "_blank";
    link.download = formulario.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDeleteClick = (formulario: Formulario) => {
    setSelectedFormulario(formulario);
    setIsDeleteDialogOpen(true);
  };

  const handleEditClick = (formulario: Formulario) => {
    setSelectedFormulario(formulario);
    setNewTitle(formulario.title);
    setIsEditDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedFormulario) return;

    try {
      await onDelete(selectedFormulario.id);
      showSuccessToast("Formulário eliminado com sucesso");
    } catch (error: any) {
      showErrorToast("Erro ao eliminar formulário");
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedFormulario(null);
    }
  };

  const confirmEdit = async () => {
    if (!selectedFormulario || !newTitle.trim()) return;

    try {
      await onEdit(selectedFormulario.id, newTitle);
      showSuccessToast("Formulário atualizado com sucesso");
    } catch (error: any) {
      showErrorToast("Erro ao atualizar formulário");
    } finally {
      setIsEditDialogOpen(false);
      setSelectedFormulario(null);
      setNewTitle("");
    }
  };

  // Função para obter o ícone com base no tipo de arquivo
  const getFileTypeDisplay = (formulario: Formulario) => {
    const { type, name } = formulario;

    if (type.includes("pdf") || name.endsWith(".pdf")) {
      return "PDF";
    } else if (type.includes("spreadsheet") || type.includes("excel") || name.endsWith(".xlsx") || name.endsWith(".xls")) {
      return "Excel";
    } else if (type.includes("word") || name.endsWith(".docx") || name.endsWith(".doc")) {
      return "Word";
    } else if (type.includes("presentation") || name.endsWith(".pptx") || name.endsWith(".ppt")) {
      return "PowerPoint";
    } else if (type.includes("text") || name.endsWith(".txt")) {
      return "Texto";
    } else {
      return "Documento";
    }
  };

  return (
    <>
      <div className="rounded-md border overflow-x-auto">
        <Table className="w-full table-fixed">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[40%]">Título</TableHead>
              <TableHead className="hidden sm:table-cell w-[15%] text-center">Tipo</TableHead>
              <TableHead className="hidden md:table-cell w-[15%] text-center">Tamanho</TableHead>
              <TableHead className="hidden md:table-cell w-[20%] text-center">Data de Upload</TableHead>
              <TableHead className="w-[60px] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {formularios.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center text-muted-foreground">
                  Nenhum formulário encontrado.
                </TableCell>
              </TableRow>
            ) : (
              formularios.map((formulario) => (
                <TableRow key={formulario.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium max-w-0 overflow-hidden">
                    <div className="truncate">{formulario.title}</div>
                    <div className="sm:hidden text-xs text-muted-foreground mt-1 truncate">
                      {getFileTypeDisplay(formulario)} • {formatFileSize(formulario.size)}
                    </div>
                  </TableCell>
                  <TableCell className="hidden sm:table-cell text-center">{getFileTypeDisplay(formulario)}</TableCell>
                  <TableCell className="hidden md:table-cell text-center">{formatFileSize(formulario.size)}</TableCell>
                  <TableCell className="hidden md:table-cell text-center">{formatDate(formulario.uploadedAt)}</TableCell>
                  <TableCell className="text-right p-2 whitespace-nowrap">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground flex size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-32">
                        <DropdownMenuItem onClick={() => handleDownload(formulario)}>
                          <IconDownload className="size-4 mr-2" />
                          Download
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditClick(formulario)}>
                          <IconEdit className="size-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(formulario)}
                          className="text-destructive focus:text-destructive"
                        >
                          <IconTrash className="size-4 mr-2" />
                          Eliminar
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Diálogo de confirmação para eliminar */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Eliminar Formulário</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja eliminar o formulário "{selectedFormulario?.title}"? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Eliminar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar título */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Formulário</DialogTitle>
            <DialogDescription>
              Atualize o título do formulário.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Título</Label>
              <Input
                id="edit-title"
                value={newTitle}
                onChange={(e) => setNewTitle(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={confirmEdit} disabled={!newTitle.trim()}>
              Guardar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
