// Configurações globais da aplicação

// Configurações da API de localização
export const locationConfig = {
  // Chave da API OpenCage Data
  apiKey: process.env.NEXT_PUBLIC_OPENCAGE_API_KEY || "",

  // URL base da API
  apiUrl: "https://api.opencagedata.com/geocode/v1/json",

  // Configurações padrão para requisições
  defaultParams: {
    language: "pt",
    countrycode: "pt",
    limit: 5
  },

  // Configurações para normalização de endereços
  addressNormalization: {
    // Se verdadeiro, remove números de porta e códigos postais dos endereços
    // Definido como false para garantir que os endereços completos sejam usados
    normalizeAddresses: false,
    // Se verdadeiro, remove endereços duplicados (mesmo endereço normalizado)
    removeDuplicates: false,
    // Se verdadeiro, inclui estabelecimentos personalizados nos resultados
    includeCustomPOIs: true,
    // Se verdadeiro, permite pesquisar por número de porta (ex: "Rua da Trindade 46")
    searchByHouseNumber: true
  }
};

// Outras configurações da aplicação podem ser adicionadas aqui
