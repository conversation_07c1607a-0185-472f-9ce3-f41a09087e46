"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import { categories } from "@/lib/categories";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

// Definir o esquema de validação
const profileSchema = z.object({
  fullName: z.string().min(3, "O nome completo deve ter pelo menos 3 caracteres"),
  registrationNumber: z.string().min(1, "O número de matrícula é obrigatório"),
  category: z.string().min(1, "A categoria é obrigatória"),
  email: z.string().email("Email inválido").optional().or(z.literal("")),
  canEditProfile: z.boolean().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

interface UserProfileFormProps {
  userId: string;
  initialData: {
    fullName?: string;
    registrationNumber?: string;
    category?: string;
    email?: string;
    canEditProfile?: boolean;
  };
  onSave: (userId: string, data: ProfileFormValues) => Promise<void>;
  onCancel: () => void;
  isSelfEdit?: boolean;
}

export function UserProfileForm({ userId, initialData, onSave, onCancel, isSelfEdit = false }: UserProfileFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      fullName: initialData.fullName || "",
      registrationNumber: initialData.registrationNumber || "",
      category: initialData.category || "",
      email: initialData.email || "",
      canEditProfile: initialData.canEditProfile !== false, // Por padrão, o utilizador pode editar seu perfil
    },
  });

  // Atualizar o formulário quando os dados iniciais mudarem
  useEffect(() => {
    if (initialData) {
      form.reset({
        fullName: initialData.fullName || "",
        registrationNumber: initialData.registrationNumber || "",
        category: initialData.category || "",
        email: initialData.email || "",
        canEditProfile: initialData.canEditProfile !== false,
      });
    }
  }, [initialData, form]);

  const onSubmit = async (data: ProfileFormValues) => {
    setIsSubmitting(true);
    const loadingToast = showLoadingToast("A guardar alterações...");

    try {
      await onSave(userId, data);

      dismissToast(loadingToast);
      showSuccessToast("Perfil atualizado com sucesso");
    } catch (error) {
      console.error("Erro ao salvar perfil:", error);
      dismissToast(loadingToast);
      showErrorToast("Erro ao guardar informações");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome Completo</FormLabel>
              <FormControl>
                <Input placeholder="Introduza o nome completo" {...field} />
              </FormControl>
              <FormDescription>
                Este é o nome que será exibido no sistema.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="registrationNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Número de Matrícula</FormLabel>
              <FormControl>
                <Input placeholder="Introduza o número de matrícula" {...field} />
              </FormControl>
              <FormDescription>
                Número de identificação único do utilizador.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Categoria</FormLabel>
              <Select
                value={field.value}
                onValueChange={field.onChange}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a categoria" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(categories).map(([group, items]) => (
                    <SelectGroup key={group}>
                      <SelectLabel>{group}</SelectLabel>
                      {items.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                Categoria profissional do utilizador.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="Introduza o email (opcional)" {...field} />
              </FormControl>
              <FormDescription>
                Email de contacto do utilizador (opcional).
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="canEditProfile"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox
                  checked={isSelfEdit ? true : field.value}
                  onCheckedChange={isSelfEdit ? undefined : field.onChange}
                  disabled={isSelfEdit}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  Permitir edição de perfil
                </FormLabel>
                <FormDescription>
                  {isSelfEdit ? (
                    "Não é possível bloquear a edição do seu próprio perfil."
                  ) : (
                    "Se desmarcado, o utilizador não poderá editar o seu próprio perfil."
                  )}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            Guardar Alterações
          </Button>
        </div>
      </form>
    </Form>
  );
}
