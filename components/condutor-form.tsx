"use client";

import { useState, useEffect } from "react";
import { showErrorToast, showInfoToast } from "@/lib/toast-utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useAuth } from "@/contexts/auth-context";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { getSystemUsers } from "@/services/users-service";
import { listenToViaturas, Viatura } from "@/services/viaturas-service";
import { Condutor, Abastecimento } from "@/services/condutores-service";

// Schema para validação do formulário
const condutorSchema = z
  .object({
    userId: z.string().min(1, { message: "Selecione um condutor" }),
    viaturaId: z.string().min(1, { message: "Selecione uma viatura" }),
    quilometrosIniciais: z.coerce.number().min(0, { message: "Quilómetros iniciais devem ser um número positivo" }),
    quilometrosFinais: z.coerce.number().min(0, { message: "Quilómetros finais devem ser um número positivo" }).optional().nullable(),
    dataHoraInicio: z.string().min(1, { message: "Data e hora de início são obrigatórias" }),
    dataHoraFim: z.string().optional().nullable(),
  })
  .refine(
    (data) => {
      // Se quilometrosFinais for nulo ou undefined, a validação passa
      if (data.quilometrosFinais === null || data.quilometrosFinais === undefined) {
        return true;
      }
      // Caso contrário, verifica se quilometrosFinais > quilometrosIniciais
      return data.quilometrosFinais > data.quilometrosIniciais;
    },
    {
      message: "Quilómetros finais devem ser maiores que os quilómetros iniciais",
      path: ["quilometrosFinais"],
    }
  );

type CondutorFormValues = z.infer<typeof condutorSchema>;

interface CondutorFormProps {
  onSaveComplete: (condutorData: Omit<Condutor, "id" | "createdAt" | "updatedAt">) => void;
  initialData?: Condutor;
  onCancel?: () => void;
}

export function CondutorForm({ onSaveComplete, initialData, onCancel }: CondutorFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [users, setUsers] = useState<{ id: string; name: string; registrationNumber?: string; role?: string; teamId?: string }[]>([]);
  const [viaturas, setViaturas] = useState<Viatura[]>([]);

  const { user, userProfile, isAdmin, userTeamId } = useAuth();

  // Inicializar o formulário com react-hook-form
  const form = useForm<CondutorFormValues>({
    resolver: zodResolver(condutorSchema),
    defaultValues: initialData
      ? {
          userId: initialData.userId,
          viaturaId: initialData.viaturaId,
          quilometrosIniciais: initialData.quilometrosIniciais,
          quilometrosFinais: initialData.quilometrosFinais,
          dataHoraInicio: formatDateTimeForInput(initialData.dataHoraInicio),
          dataHoraFim: initialData.dataHoraFim ? formatDateTimeForInput(initialData.dataHoraFim) : null,
        }
      : {
          userId: "",
          viaturaId: "",
          quilometrosIniciais: 0,
          quilometrosFinais: null,
          dataHoraInicio: formatDateTimeForInput(new Date()),
          dataHoraFim: null,
        },
  });

  // Carregar usuários ao montar o componente
  useEffect(() => {
    async function loadUsers() {
      try {
        // Se for admin, carregar todos os usuários; caso contrário, apenas os da equipe
        console.log("Carregando usuários com teamId:", userTeamId);

        let usersData: { id: string; name: string; registrationNumber?: string; role?: string; teamId?: string }[] = [];
        if (isAdmin) {
          // Admin vê todos os usuários
          usersData = await getSystemUsers();
        } else if (userTeamId) {
          // Membros e líderes de equipe veem apenas os usuários da sua equipe
          usersData = await getSystemUsers(userTeamId);
        } else {
          // Se não for admin e não tiver teamId, retornar lista vazia
          console.warn("Usuário não é admin e não tem teamId definido");
        }

        console.log(`Carregados ${usersData.length} usuários`);
        setUsers(usersData);
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
        showErrorToast("Não foi possível carregar a lista de condutores");
      }
    }

    loadUsers();
  }, [isAdmin, userTeamId]);

  // Configurar listener para viaturas em tempo real
  useEffect(() => {
    // Configurar listener para atualizações em tempo real
    const unsubscribe = listenToViaturas((data) => {
      setViaturas(data);
    });

    // Função de cleanup para remover o listener quando o componente for desmontado
    return () => {
      unsubscribe();
    };
  }, []);

  // Atualizar o formulário quando initialData mudar (para edição)
  useEffect(() => {
    if (initialData) {
      form.reset({
        userId: initialData.userId,
        viaturaId: initialData.viaturaId,
        quilometrosIniciais: initialData.quilometrosIniciais,
        quilometrosFinais: initialData.quilometrosFinais,
        dataHoraInicio: formatDateTimeForInput(initialData.dataHoraInicio),
        dataHoraFim: initialData.dataHoraFim ? formatDateTimeForInput(initialData.dataHoraFim) : null,
      });
    } else {
      // Se não houver initialData, definir a hora atual para o campo de data e hora de início
      form.setValue("dataHoraInicio", formatDateTimeForInput(new Date()));
    }
  }, [initialData, form]);

  // Função para formatar data e hora para o formato de input datetime-local
  function formatDateTimeForInput(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  // Função para lidar com a submissão do formulário
  async function onSubmit(data: CondutorFormValues) {
    if (!user) return;

    // Removido o toast de carregamento daqui, pois o componente pai já gerencia seu próprio toast
    setIsSubmitting(true);

    try {
      // Encontrar o nome do usuário selecionado
      const selectedUser = users.find(u => u.id === data.userId);
      const userName = selectedUser?.name || "Utilizador desconhecido";

      // Encontrar informações da viatura selecionada
      const selectedViatura = viaturas.find(v => v.id === data.viaturaId);
      const viaturaInfo = selectedViatura
        ? `${selectedViatura.matricula} - ${selectedViatura.modelo}`
        : "Viatura desconhecida";

      // No formulário inicial, não há abastecimento
      const abastecimento: Abastecimento | null = null;

      // Encontrar o teamId do usuário selecionado
      const selectedUserTeamId = selectedUser?.teamId;

      // Log para debug
      console.log("Dados do usuário selecionado:", {
        id: selectedUser?.id,
        name: selectedUser?.name,
        teamId: selectedUserTeamId || "[não definido]"
      });

      // Preparar os dados do condutor
      const condutorData: Omit<Condutor, "id" | "createdAt" | "updatedAt"> = {
        userId: data.userId,
        userName,
        viaturaId: data.viaturaId,
        viaturaInfo,
        quilometrosIniciais: data.quilometrosIniciais,
        quilometrosFinais: data.quilometrosFinais === undefined ? null : data.quilometrosFinais,
        dataHoraInicio: new Date(data.dataHoraInicio),
        dataHoraFim: data.dataHoraFim ? new Date(data.dataHoraFim) : null,
        abastecimento,
        createdBy: user.uid,
        // Usar o teamId do usuário atual (que está criando o registro)
        // Isso garante que todos os membros da mesma equipa vejam o registro
        teamId: userTeamId || selectedUserTeamId,
      };

      // Log para debug
      console.log("TeamId definido para o condutor:", condutorData.teamId || "[não definido]");

      // Notificar o componente pai sobre o registo guardado
      onSaveComplete(condutorData);

      // Limpar o formulário apenas se não estiver editando um registro existente
      if (!initialData) {
        resetForm();
      }
    } catch (error) {
      console.error("Erro ao preparar dados do condutor:", error);
      showErrorToast("Erro ao preparar dados do registo");
    } finally {
      setIsSubmitting(false);
    }
  }

  // Função para resetar o formulário
  const resetForm = () => {
    // Se estiver no modo de edição e tiver dados iniciais, voltar para os dados originais
    if (initialData) {
      // Resetar para os valores originais
      form.reset({
        userId: initialData.userId,
        viaturaId: initialData.viaturaId,
        quilometrosIniciais: initialData.quilometrosIniciais,
        quilometrosFinais: initialData.quilometrosFinais,
        dataHoraInicio: formatDateTimeForInput(initialData.dataHoraInicio),
        dataHoraFim: initialData.dataHoraFim ? formatDateTimeForInput(initialData.dataHoraFim) : null,
      }, {
        // Garantir que todos os campos sejam marcados como não tocados e não sujos
        keepDefaultValues: false,
        keepDirty: false,
        keepErrors: false,
        keepIsSubmitted: false,
        keepTouched: false,
        keepIsValid: false,
        keepSubmitCount: false
      });
    } else {
      // Se estiver no modo de criação, limpar todos os campos
      form.reset({
        userId: "",
        viaturaId: "",
        quilometrosIniciais: 0,
        quilometrosFinais: null,
        dataHoraInicio: formatDateTimeForInput(new Date()),
        dataHoraFim: null,
      }, {
        // Garantir que todos os campos sejam marcados como não tocados e não sujos
        keepDefaultValues: false,
        keepDirty: false,
        keepErrors: false,
        keepIsSubmitted: false,
        keepTouched: false,
        keepIsValid: false,
        keepSubmitCount: false
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
        <div className="grid grid-cols-1 gap-4 w-full">
          {/* Seleção de Condutor */}
          <FormField
            control={form.control}
            name="userId"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Nome do Condutor</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isSubmitting}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Selecione um condutor" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        <div className="flex justify-between w-full">
                          <span>{user.name}</span>
                          {user.registrationNumber && <span className="text-muted-foreground ml-2">{user.registrationNumber}</span>}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Seleção de Viatura */}
          <FormField
            control={form.control}
            name="viaturaId"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Viatura</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isSubmitting}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Selecione uma viatura" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {viaturas.map((viatura) => (
                      <SelectItem key={viatura.id} value={viatura.id}>
                        {viatura.matricula} - {viatura.modelo}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {initialData ? (
          // Modo de edição - mostrar todos os campos
          <>
            <div className="grid grid-cols-1 gap-4 w-full">
              {/* Quilômetros Iniciais */}
              <FormField
                control={form.control}
                name="quilometrosIniciais"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Quilómetros Iniciais</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Quilômetros Finais */}
              <FormField
                control={form.control}
                name="quilometrosFinais"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Quilómetros Finais</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Preencher no final do serviço"
                        value={field.value === null || field.value === undefined ? "" : field.value}
                        onChange={(e) => field.onChange(e.target.value === "" ? undefined : Number(e.target.value))}
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 w-full">
              {/* Data e Hora de Início */}
              <FormField
                control={form.control}
                name="dataHoraInicio"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Data e Hora de Início</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        {...field}
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Data e Hora de Fim */}
              <FormField
                control={form.control}
                name="dataHoraFim"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Data e Hora de Fim</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        value={field.value === null || field.value === undefined ? "" : field.value}
                        onChange={(e) => field.onChange(e.target.value || "")}
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        ) : (
          // Modo de criação - mostrar apenas os campos necessários
          <div className="grid grid-cols-1 gap-4">
            {/* Quilômetros Iniciais */}
            <FormField
              control={form.control}
              name="quilometrosIniciais"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Quilómetros Iniciais</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="0"
                      {...field}
                      disabled={isSubmitting}
                      className="w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Data e Hora de Início */}
            <FormField
              control={form.control}
              name="dataHoraInicio"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Data e Hora de Início</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      {...field}
                      disabled={isSubmitting}
                      className="w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <div className="flex flex-col sm:flex-row sm:justify-end gap-2 mt-6">
          {initialData ? (
            <Button
              variant="outline"
              type="button"
              onClick={() => {
                // Resetar o formulário para os valores originais
                resetForm();

                // Forçar uma limpeza completa do formulário
                setTimeout(() => {
                  // Se estamos cancelando a edição, não precisamos limpar os campos
                  // O componente será desmontado ou atualizado com novos dados
                }, 0);

                // Notificar o componente pai para sair do modo de edição
                if (onCancel) onCancel();

                // Mostrar mensagem de confirmação
                showInfoToast("Edição cancelada");
              }}
              disabled={isSubmitting}
              className="sm:order-first"
            >
              Cancelar Edição
            </Button>
          ) : (
            <Button
              variant="outline"
              type="button"
              onClick={resetForm}
              disabled={isSubmitting}
              className="sm:order-first"
            >
              Limpar Campos
            </Button>
          )}
          <Button
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? "A guardar..." : initialData ? "Guardar Alterações" : "Iniciar Serviço"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
