import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe
} from "firebase/firestore";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

export interface ContactInfo {
  value: string;
  primary?: boolean;
}

export interface Contacto {
  id: string;
  nome: string;
  telefones: ContactInfo[];
  emails: ContactInfo[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

const COLLECTION_NAME = "contactos";

// Adicionar um novo contacto
export async function addContacto(contactoData: Omit<Contacto, "id" | "createdAt" | "updatedAt">): Promise<string> {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...contactoData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de criação
    if (contactoData.createdBy) {
      const userInfo = await getUser(contactoData.createdBy);
      await logCreationActivity(
        ActivityType.CONTACTO_CREATED,
        "Novo Contacto",
        `Criou um novo contacto: ${contactoData.nome}`,
        contactoData.createdBy,
        userInfo?.teamId,
        {
          nome: contactoData.nome,
          telefones: contactoData.telefones,
          emails: contactoData.emails
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar contacto:", error);
    throw error;
  }
}

// Obter todos os contactos
export async function getContactos(): Promise<Contacto[]> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as Contacto));
  } catch (error) {
    console.error("Erro ao obter contactos:", error);
    throw error;
  }
}

// Atualizar um contacto existente
export async function updateContacto(id: string, data: Partial<Contacto>, userId?: string): Promise<boolean> {
  try {
    const contactoRef = doc(db, COLLECTION_NAME, id);

    // Obter dados atuais do contacto para registar a atividade
    const contactoSnap = await getDoc(contactoRef);
    const contactoData = contactoSnap.exists() ? contactoSnap.data() : null;

    await updateDoc(contactoRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de atualização
    if (userId && contactoData) {
      const userInfo = await getUser(userId);
      await logUpdateActivity(
        ActivityType.CONTACTO_UPDATED,
        "Contacto Atualizado",
        `Atualizou o contacto: ${contactoData.nome}`,
        userId,
        userInfo?.teamId,
        {
          nome: contactoData.nome,
          telefones: contactoData.telefones,
          emails: contactoData.emails
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao atualizar contacto:", error);
    throw error;
  }
}

// Excluir um contacto
export async function deleteContacto(id: string, userId: string): Promise<boolean> {
  try {
    // Obter dados do contacto antes de excluir para registar a atividade
    const contactoRef = doc(db, COLLECTION_NAME, id);
    const contactoSnap = await getDoc(contactoRef);
    const contactoData = contactoSnap.exists() ? contactoSnap.data() : null;

    await deleteDoc(doc(db, COLLECTION_NAME, id));

    // Registar atividade de eliminação
    if (contactoData) {
      const userInfo = await getUser(userId);
      await logDeletionActivity(
        ActivityType.CONTACTO_DELETED,
        "Contacto Eliminado",
        `Eliminou o contacto: ${contactoData.nome}`,
        userId,
        userInfo?.teamId,
        {
          nome: contactoData.nome,
          organizacao: contactoData.organizacao
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao excluir contacto:", error);
    throw error;
  }
}

// Escutar mudanças em contactos em tempo real
export function listenToContactos(callback: (contactos: Contacto[]) => void): Unsubscribe {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("createdAt", "desc")
    );

    return onSnapshot(q, (querySnapshot) => {
      const contactos = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as Contacto));

      callback(contactos);
    }, (error) => {
      console.error("Erro ao escutar contactos:", error);
    });
  } catch (error) {
    console.error("Erro ao configurar listener de contactos:", error);
    throw error;
  }
}
