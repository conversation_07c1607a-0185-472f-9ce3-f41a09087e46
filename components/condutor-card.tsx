"use client";

import { useState } from "react";
import { toast } from "sonner";
import {
  <PERSON>,
  Card<PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
  CardContent,
  CardAction,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  IconEdit,
  IconTrash,
  IconDotsVertical,
  IconDots,
  IconCar,
  IconGasStation,
  IconRoad,
  IconClock,
  IconUser,
  IconCheck,
  IconDroplet,
} from "@tabler/icons-react";
import { Condutor } from "@/services/condutores-service";

interface CondutorCardProps {
  condutor: Condutor;
  onEdit: (condutor: Condutor) => void;
  onDelete: (id: string) => Promise<void>;
  onFinalize: (condutor: Condutor) => void;
  onAbastecer: (condutor: Condutor) => void;
}

export function CondutorCard({ condutor, onEdit, onDelete, onFinalize, onAbastecer }: CondutorCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Formatar data e hora
  const formatDateTime = (date: Date | null): string => {
    if (!date) return "Não definido";
    return new Intl.DateTimeFormat("pt-PT", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  // Verificar se o serviço está finalizado
  const isFinalized = condutor.quilometrosFinais !== null && condutor.dataHoraFim !== null;

  // Calcular a distância percorrida
  const distanciaPercorrida = isFinalized
    ? condutor.quilometrosFinais! - condutor.quilometrosIniciais
    : null;

  // Função para lidar com a exclusão
  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onDelete(condutor.id);
      // A notificação de sucesso é exibida na página principal
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Erro ao excluir registo:", error);
      toast.error("Erro ao excluir registo");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <Card className="@container/card dark:*:data-[slot=card]:bg-card *:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs flex flex-col min-h-[400px]">
        <CardHeader className="px-6 pb-3">
          <div className="flex flex-col gap-2">
            <div className="flex justify-between items-start">
              <div className="flex flex-col gap-2">
                <CardTitle className="leading-none font-semibold truncate">{condutor.userName}</CardTitle>
                <CardDescription className="text-sm truncate">{condutor.viaturaInfo}</CardDescription>
                <Badge
                  variant={isFinalized ? "default" : "outline"}
                  className="whitespace-nowrap self-start mt-1"
                >
                  {isFinalized ? "Finalizado" : "Em Progresso"}
                </Badge>
              </div>
              <div className="shrink-0">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="size-8">
                      <IconDotsVertical className="size-4" />
                      <span className="sr-only">Ações</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onEdit(condutor)}>
                      <IconEdit className="mr-2 size-4" />
                      Editar
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setIsDeleteDialogOpen(true)}
                      className="text-destructive focus:text-destructive"
                    >
                      <IconTrash className="mr-2 size-4" />
                      Excluir
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-6 pt-0 flex-1">
          <div className="grid grid-cols-1 gap-3 mb-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Início</span>
                <span className="text-sm">{formatDateTime(condutor.dataHoraInicio)}</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Fim</span>
                <span className="text-sm">{formatDateTime(condutor.dataHoraFim)}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Km Iniciais</span>
                <span className="text-sm">{condutor.quilometrosIniciais} km</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Km Finais</span>
                <span className="text-sm">
                  {condutor.quilometrosFinais !== null ? `${condutor.quilometrosFinais} km` : "Não definido"}
                </span>
              </div>
            </div>
          </div>

          {distanciaPercorrida !== null && (
            <div className="flex items-center justify-between mb-3 border-t pt-3">
              <span className="text-sm font-medium">Distância Percorrida</span>
              <Badge variant="secondary">{distanciaPercorrida} km</Badge>
            </div>
          )}

          {condutor.abastecimento && (
            <div className="border-t pt-3 mb-3">
              <span className="text-sm font-medium mb-2 block">Abastecimento</span>
              <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Quilómetros</span>
                  <span className="text-sm">{condutor.abastecimento.quilometros} km</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Litros</span>
                  <span className="text-sm">{condutor.abastecimento.litros} L</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Preço por Litro</span>
                  <span className="text-sm">{condutor.abastecimento.precoLitro.toFixed(2)} €/L</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Preço Total</span>
                  <span className="text-sm">{condutor.abastecimento.precoTotal.toFixed(2)} €</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="px-6 pt-3 border-t mt-auto">
          {!isFinalized ? (
            <div className="flex gap-2 w-full">
              <Button
                variant="default"
                size="sm"
                className="flex-1"
                onClick={() => onFinalize(condutor)}
              >
                <IconCheck className="mr-2 size-4" />
                Finalizar
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex-1"
                onClick={() => onAbastecer(condutor)}
              >
                <IconDroplet className="mr-2 size-4" />
                Abastecer
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between w-full">
              <span className="text-sm text-muted-foreground">
                {condutor.dataHoraFim ? `Finalizado em ${formatDateTime(condutor.dataHoraFim)}` : "Serviço finalizado"}
              </span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="ml-auto">
                    <IconDots className="size-4" />
                    <span className="sr-only">Ações</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onAbastecer(condutor)}>
                    <IconDroplet className="mr-2 size-4" />
                    Registar Abastecimento
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Diálogo de confirmação de exclusão */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir este registo? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "A excluir..." : "Excluir"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
