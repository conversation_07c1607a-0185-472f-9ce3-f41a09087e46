"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { IconDotsVertical, IconUserMinus, IconUserCheck, IconUserX } from "@tabler/icons-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

interface User {
  id: string;
  name: string;
  registrationNumber?: string;
  category?: string;
}

interface TeamMembersListProps {
  users: User[];
  onRemove: (userId: string) => void;
  onPromote?: (userId: string) => void;
  onDemote?: (userId: string) => void;
  showPromoteButton?: boolean;
  showDemoteButton?: boolean;
}

export function TeamMembersList({
  users,
  onRemove,
  onPromote,
  onDemote,
  showPromoteButton = false,
  showDemoteButton = false
}: TeamMembersListProps) {
  const [userToRemove, setUserToRemove] = useState<User | null>(null);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);

  const handleRemoveClick = (user: User) => {
    setUserToRemove(user);
    setIsRemoveDialogOpen(true);
  };

  const confirmRemove = () => {
    if (userToRemove) {
      onRemove(userToRemove.id);
      setIsRemoveDialogOpen(false);
      setUserToRemove(null);
    }
  };

  return (
    <>
      <div className="rounded-md border overflow-x-auto">
        <Table className="w-full table-fixed">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[40%]">Utilizador</TableHead>
              <TableHead className="w-[20%]">Matrícula</TableHead>
              <TableHead className="w-[30%]">Categoria</TableHead>
              <TableHead className="w-[10%] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                  Nenhum utilizador encontrado.
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id} className="hover:bg-muted/50">
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="size-8">
                        <AvatarFallback>
                          {user.name.split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium truncate">{user.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{user.registrationNumber || "N/A"}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{user.category || "N/A"}</div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        {showPromoteButton && onPromote && (
                          <DropdownMenuItem onClick={() => onPromote(user.id)}>
                            <IconUserCheck className="mr-2 size-4" />
                            Promover a líder
                          </DropdownMenuItem>
                        )}
                        {showDemoteButton && onDemote && (
                          <DropdownMenuItem onClick={() => onDemote(user.id)}>
                            <IconUserX className="mr-2 size-4" />
                            Rebaixar a membro
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem
                          onClick={() => handleRemoveClick(user)}
                          className="text-destructive focus:text-destructive"
                        >
                          <IconUserMinus className="mr-2 size-4" />
                          Remover
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Diálogo de confirmação de remoção */}
      <Dialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar remoção</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja remover {userToRemove?.name} da equipa? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRemoveDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={confirmRemove}>
              Remover
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
