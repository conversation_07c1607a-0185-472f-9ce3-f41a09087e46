import { db } from "@/lib/firebase";
import { Estabelecimento } from "@/types/estabelecimento";
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot,
  Unsubscribe,
  onSnapshot
} from "firebase/firestore";
import { getUserInfo } from "./auth-service";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

// Nome da coleção no Firestore
const COLLECTION_NAME = "estabelecimentos";

/**
 * Converte um documento do Firestore para o tipo Estabelecimento
 */
function convertToEstabelecimento(doc: QueryDocumentSnapshot<DocumentData>): Estabelecimento {
  const data = doc.data();

  return {
    id: doc.id,
    nome: data.nome,
    tipo: data.tipo,
    endereco: data.endereco,
    coordenadas: data.coordenadas,
    descricao: data.descricao,
    horarioFuncionamento: data.horarioFuncionamento,
    telefone: data.telefone,
    website: data.website,
    criadoPor: data.criadoPor,
    criadoEm: data.criadoEm?.toDate(),
    atualizadoPor: data.atualizadoPor,
    atualizadoEm: data.atualizadoEm?.toDate()
  };
}

/**
 * Adiciona um novo estabelecimento
 */
export async function addEstabelecimento(estabelecimento: Omit<Estabelecimento, "id" | "criadoPor" | "criadoEm" | "atualizadoPor" | "atualizadoEm">): Promise<string> {
  try {
    const userInfo = await getUserInfo();

    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...estabelecimento,
      criadoPor: userInfo?.matricula || "Sistema",
      criadoEm: Timestamp.now(),
      atualizadoPor: userInfo?.matricula || "Sistema",
      atualizadoEm: Timestamp.now()
    });

    // Registar atividade de criação
    if (userInfo?.uid) {
      const user = await getUser(userInfo.uid);
      await logCreationActivity(
        ActivityType.ESTABELECIMENTO_CREATED,
        "Novo Estabelecimento",
        `Criou um novo estabelecimento: ${estabelecimento.nome}`,
        userInfo.uid,
        user?.teamId,
        {
          nome: estabelecimento.nome,
          tipo: estabelecimento.tipo,
          endereco: estabelecimento.endereco
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar estabelecimento:", error);
    throw error;
  }
}

/**
 * Atualiza um estabelecimento existente
 */
export async function updateEstabelecimento(id: string, estabelecimento: Partial<Omit<Estabelecimento, "id" | "criadoPor" | "criadoEm" | "atualizadoPor" | "atualizadoEm">>): Promise<void> {
  try {
    const userInfo = await getUserInfo();

    // Obter dados atuais do estabelecimento para registar a atividade
    const estabelecimentoAtual = await getEstabelecimentoById(id);

    await updateDoc(doc(db, COLLECTION_NAME, id), {
      ...estabelecimento,
      atualizadoPor: userInfo?.matricula || "Sistema",
      atualizadoEm: Timestamp.now()
    });

    // Registar atividade de atualização
    if (userInfo?.uid && estabelecimentoAtual) {
      const user = await getUser(userInfo.uid);
      await logUpdateActivity(
        ActivityType.ESTABELECIMENTO_UPDATED,
        "Estabelecimento Atualizado",
        `Atualizou o estabelecimento: ${estabelecimentoAtual.nome}`,
        userInfo.uid,
        user?.teamId,
        {
          nome: estabelecimentoAtual.nome,
          tipo: estabelecimentoAtual.tipo,
          endereco: estabelecimentoAtual.endereco
        }
      );
    }
  } catch (error) {
    console.error("Erro ao atualizar estabelecimento:", error);
    throw error;
  }
}

/**
 * Exclui um estabelecimento
 */
export async function deleteEstabelecimento(id: string): Promise<void> {
  try {
    const userInfo = await getUserInfo();

    // Obter dados do estabelecimento antes de excluir para registar a atividade
    const estabelecimentoData = await getEstabelecimentoById(id);

    await deleteDoc(doc(db, COLLECTION_NAME, id));

    // Registar atividade de eliminação
    if (userInfo?.uid && estabelecimentoData) {
      const user = await getUser(userInfo.uid);
      await logDeletionActivity(
        ActivityType.ESTABELECIMENTO_DELETED,
        "Estabelecimento Eliminado",
        `Eliminou o estabelecimento: ${estabelecimentoData.nome}`,
        userInfo.uid,
        user?.teamId,
        {
          nome: estabelecimentoData.nome,
          tipo: estabelecimentoData.tipo,
          endereco: estabelecimentoData.endereco
        }
      );
    }
  } catch (error) {
    console.error("Erro ao excluir estabelecimento:", error);
    throw error;
  }
}

/**
 * Obtém todos os estabelecimentos
 */
export async function getEstabelecimentos(): Promise<Estabelecimento[]> {
  try {
    const q = query(collection(db, COLLECTION_NAME), orderBy("nome"));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(convertToEstabelecimento);
  } catch (error) {
    console.error("Erro ao obter estabelecimentos:", error);
    throw error;
  }
}

/**
 * Busca estabelecimentos por termo de pesquisa
 */
export async function searchEstabelecimentos(searchTerm: string): Promise<Estabelecimento[]> {
  try {
    // Como o Firestore não suporta pesquisa de texto completo,
    // vamos buscar todos os estabelecimentos e filtrar no cliente
    const estabelecimentos = await getEstabelecimentos();

    if (!searchTerm.trim()) {
      return estabelecimentos;
    }

    const searchTermLower = searchTerm.toLowerCase();

    return estabelecimentos.filter(estabelecimento =>
      estabelecimento.nome.toLowerCase().includes(searchTermLower) ||
      estabelecimento.endereco.toLowerCase().includes(searchTermLower) ||
      estabelecimento.tipo.toLowerCase().includes(searchTermLower)
    );
  } catch (error) {
    console.error("Erro ao buscar estabelecimentos:", error);
    throw error;
  }
}

/**
 * Configura um listener para mudanças nos estabelecimentos
 */
export function listenToEstabelecimentos(callback: (estabelecimentos: Estabelecimento[]) => void): Unsubscribe {
  const q = query(collection(db, COLLECTION_NAME), orderBy("nome"));

  return onSnapshot(q, (querySnapshot) => {
    const estabelecimentos = querySnapshot.docs.map(convertToEstabelecimento);
    callback(estabelecimentos);
  });
}

/**
 * Obtém um estabelecimento pelo ID
 */
export async function getEstabelecimentoById(id: string): Promise<Estabelecimento | null> {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDocs(query(collection(db, COLLECTION_NAME), where("__name__", "==", id)));

    if (docSnap.empty) {
      return null;
    }

    return convertToEstabelecimento(docSnap.docs[0]);
  } catch (error) {
    console.error("Erro ao obter estabelecimento por ID:", error);
    throw error;
  }
}
