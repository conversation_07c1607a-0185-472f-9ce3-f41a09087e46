import { db } from "@/lib/firebase";
import { Team, UserRole } from "@/types/team";
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot,
  Unsubscribe,
  onSnapshot,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
  setDoc
} from "firebase/firestore";
import { getUserInfo } from "./auth-service";

// Nome da coleção no Firestore
const COLLECTION_NAME = "teams";

/**
 * Adiciona uma nova equipa
 */
export async function addTeam(team: Omit<Team, "id" | "createdAt" | "updatedAt" | "createdBy">): Promise<string> {
  try {
    const userInfo = await getUserInfo();

    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...team,
      createdBy: userInfo?.uid || "Sistema",
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar equipa:", error);
    throw error;
  }
}

/**
 * Atualiza uma equipa existente
 */
export async function updateTeam(id: string, team: Partial<Omit<Team, "id" | "createdAt" | "createdBy">>): Promise<void> {
  try {
    await updateDoc(doc(db, COLLECTION_NAME, id), {
      ...team,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error("Erro ao atualizar equipa:", error);
    throw error;
  }
}

/**
 * Exclui uma equipa
 */
export async function deleteTeam(id: string): Promise<void> {
  try {
    await deleteDoc(doc(db, COLLECTION_NAME, id));
  } catch (error) {
    console.error("Erro ao excluir equipa:", error);
    throw error;
  }
}

/**
 * Obtém uma equipa pelo ID
 */
export async function getTeam(id: string): Promise<Team | null> {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        name: data.name,
        description: data.description,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy,
        leaders: data.leaders || [],
        members: data.members || []
      };
    }

    return null;
  } catch (error) {
    console.error("Erro ao obter equipa:", error);
    throw error;
  }
}

/**
 * Obtém todas as equipas
 */
export async function getTeams(): Promise<Team[]> {
  try {
    const q = query(collection(db, COLLECTION_NAME), orderBy("name"));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        description: data.description,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy,
        leaders: data.leaders || [],
        members: data.members || []
      };
    });
  } catch (error) {
    console.error("Erro ao obter equipas:", error);
    throw error;
  }
}

/**
 * Adiciona um membro a uma equipa
 */
export async function addTeamMember(teamId: string, userId: string): Promise<void> {
  try {
    // 1. Atualizar a equipa
    await updateDoc(doc(db, COLLECTION_NAME, teamId), {
      members: arrayUnion(userId),
      updatedAt: serverTimestamp()
    });

    // 2. Atualizar o usuário
    const userDocRef = doc(db, "users", userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      const oldTeamId = userData.teamId;
      const oldRole = userData.role;

      // Se o usuário já pertence a outra equipa, removê-lo dessa equipa
      if (oldTeamId && oldTeamId !== teamId) {
        const oldTeamRef = doc(db, COLLECTION_NAME, oldTeamId);
        const oldTeamDoc = await getDoc(oldTeamRef);

        if (oldTeamDoc.exists()) {
          const oldTeamData = oldTeamDoc.data();

          // Remover o usuário da lista de líderes ou membros, dependendo do papel anterior
          if (oldRole === UserRole.TEAM_LEADER) {
            await updateDoc(oldTeamRef, {
              leaders: arrayRemove(userId),
              updatedAt: serverTimestamp()
            });
          } else if (oldRole === UserRole.TEAM_MEMBER) {
            await updateDoc(oldTeamRef, {
              members: arrayRemove(userId),
              updatedAt: serverTimestamp()
            });
          }
        }
      }

      // Atualizar o papel e a equipa do usuário
      await setDoc(userDocRef, {
        role: UserRole.TEAM_MEMBER,
        teamId: teamId,
        updatedAt: serverTimestamp()
      }, { merge: true });
    }
  } catch (error) {
    console.error("Erro ao adicionar membro à equipa:", error);
    throw error;
  }
}

/**
 * Remove um membro de uma equipa
 */
export async function removeTeamMember(teamId: string, userId: string): Promise<void> {
  try {
    // 1. Atualizar a equipa
    await updateDoc(doc(db, COLLECTION_NAME, teamId), {
      members: arrayRemove(userId),
      updatedAt: serverTimestamp()
    });

    // 2. Atualizar o usuário
    const userDocRef = doc(db, "users", userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();

      // Verificar se o usuário pertence a esta equipa
      if (userData.teamId === teamId) {
        // Remover o papel e a equipa do usuário
        await setDoc(userDocRef, {
          role: null,
          teamId: null,
          updatedAt: serverTimestamp()
        }, { merge: true });
      }
    }
  } catch (error) {
    console.error("Erro ao remover membro da equipa:", error);
    throw error;
  }
}

/**
 * Adiciona um líder a uma equipa
 */
export async function addTeamLeader(teamId: string, userId: string): Promise<void> {
  try {
    // 1. Atualizar a equipa
    await updateDoc(doc(db, COLLECTION_NAME, teamId), {
      leaders: arrayUnion(userId),
      updatedAt: serverTimestamp()
    });

    // 2. Atualizar o usuário
    const userDocRef = doc(db, "users", userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      const oldTeamId = userData.teamId;
      const oldRole = userData.role;

      // Se o usuário já pertence a outra equipa, removê-lo dessa equipa
      if (oldTeamId && oldTeamId !== teamId) {
        const oldTeamRef = doc(db, COLLECTION_NAME, oldTeamId);
        const oldTeamDoc = await getDoc(oldTeamRef);

        if (oldTeamDoc.exists()) {
          const oldTeamData = oldTeamDoc.data();

          // Remover o usuário da lista de líderes ou membros, dependendo do papel anterior
          if (oldRole === UserRole.TEAM_LEADER) {
            await updateDoc(oldTeamRef, {
              leaders: arrayRemove(userId),
              updatedAt: serverTimestamp()
            });
          } else if (oldRole === UserRole.TEAM_MEMBER) {
            await updateDoc(oldTeamRef, {
              members: arrayRemove(userId),
              updatedAt: serverTimestamp()
            });
          }
        }
      }

      // Atualizar o papel e a equipa do usuário
      await setDoc(userDocRef, {
        role: UserRole.TEAM_LEADER,
        teamId: teamId,
        updatedAt: serverTimestamp()
      }, { merge: true });
    }
  } catch (error) {
    console.error("Erro ao adicionar líder à equipa:", error);
    throw error;
  }
}

/**
 * Remove um líder de uma equipa
 */
export async function removeTeamLeader(teamId: string, userId: string): Promise<void> {
  try {
    // 1. Atualizar a equipa
    await updateDoc(doc(db, COLLECTION_NAME, teamId), {
      leaders: arrayRemove(userId),
      updatedAt: serverTimestamp()
    });

    // 2. Atualizar o usuário
    const userDocRef = doc(db, "users", userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();

      // Verificar se o usuário pertence a esta equipa
      if (userData.teamId === teamId) {
        // Remover o papel e a equipa do usuário
        await setDoc(userDocRef, {
          role: null,
          teamId: null,
          updatedAt: serverTimestamp()
        }, { merge: true });
      }
    }
  } catch (error) {
    console.error("Erro ao remover líder da equipa:", error);
    throw error;
  }
}

/**
 * Configura um listener para mudanças nas equipas
 */
export function listenToTeams(callback: (teams: Team[]) => void): Unsubscribe {
  const q = query(collection(db, COLLECTION_NAME), orderBy("name"));

  return onSnapshot(q, (querySnapshot) => {
    const teams = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        description: data.description,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy,
        leaders: data.leaders || [],
        members: data.members || []
      };
    });

    callback(teams);
  });
}

/**
 * Obtém a equipa de um usuário
 */
export async function getUserTeam(userId: string): Promise<Team | null> {
  try {
    // Primeiro, verificar se o usuário tem teamId no perfil
    const userDocRef = doc(db, "users", userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      if (userData.teamId) {
        // Se o usuário tem teamId, buscar a equipa diretamente
        console.log("Buscando equipa pelo teamId:", userData.teamId);
        const teamDoc = await getDoc(doc(db, COLLECTION_NAME, userData.teamId));

        if (teamDoc.exists()) {
          const data = teamDoc.data();
          return {
            id: teamDoc.id,
            name: data.name,
            description: data.description,
            createdAt: data.createdAt?.toDate(),
            updatedAt: data.updatedAt?.toDate(),
            createdBy: data.createdBy,
            leaders: data.leaders || [],
            members: data.members || []
          };
        }
      }
    }

    // Se não encontrou pelo teamId, tentar pelos arrays de membros e líderes
    console.log("Buscando equipa pelos arrays de membros e líderes");

    // Buscar equipa onde o usuário é membro
    let q = query(
      collection(db, COLLECTION_NAME),
      where("members", "array-contains", userId)
    );

    let querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        description: data.description,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy,
        leaders: data.leaders || [],
        members: data.members || []
      };
    }

    // Se não for membro, verificar se é líder
    q = query(
      collection(db, COLLECTION_NAME),
      where("leaders", "array-contains", userId)
    );

    querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        description: data.description,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        createdBy: data.createdBy,
        leaders: data.leaders || [],
        members: data.members || []
      };
    }

    return null;
  } catch (error) {
    console.error("Erro ao obter equipa do usuário:", error);
    throw error;
  }
}
