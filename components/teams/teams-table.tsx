"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { IconDotsVertical, IconEdit, IconTrash, IconEye } from "@tabler/icons-react";
import { Team } from "@/types/team";
import { getSystemUsers } from "@/services/users-service";

interface TeamsTableProps {
  teams: Team[];
  onDelete: (team: Team) => void; // Alterado para receber o objeto team completo
  onEdit: (team: Team) => void;
  onViewDetails: (id: string) => void;
}

export function TeamsTable({ teams, onDelete, onEdit, onViewDetails }: TeamsTableProps) {
  // Estado para armazenar os usuários do sistema
  const [users, setUsers] = useState<{ id: string; name: string }[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);

  // Efeito para carregar usuários do sistema
  useEffect(() => {
    const loadUsers = async () => {
      try {
        setIsLoadingUsers(true);
        const usersData = await getSystemUsers();
        setUsers(usersData);
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
      } finally {
        setIsLoadingUsers(false);
      }
    };

    loadUsers();
  }, []);

  // Função para lidar com o clique no botão de exclusão
  const handleDeleteClick = (team: Team) => {
    onDelete(team); // Passa o objeto team completo para a função de exclusão
  };


  // Função para contar membros válidos (que existem no sistema)
  const countValidMembers = (team: Team) => {
    return users.filter(user => team.members.includes(user.id)).length;
  };

  // Função para contar líderes válidos (que existem no sistema)
  const countValidLeaders = (team: Team) => {
    return users.filter(user => team.leaders.includes(user.id)).length;
  };

  return (
    <>
      <div className="rounded-md border overflow-x-auto">
        <Table className="w-full table-fixed">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[40%]">Nome</TableHead>
              <TableHead className="w-[25%] text-center">Membros</TableHead>
              <TableHead className="w-[25%] text-center">Líderes</TableHead>
              <TableHead className="w-[10%] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {teams.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                  Nenhuma equipa encontrada.
                </TableCell>
              </TableRow>
            ) : (
              teams.map((team) => (
                <TableRow key={team.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="truncate">{team.name}</div>
                  </TableCell>
                  <TableCell className="text-center">
                    {isLoadingUsers ? (
                      <div className="flex justify-center">
                        <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                      </div>
                    ) : (
                      countValidMembers(team)
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {isLoadingUsers ? (
                      <div className="flex justify-center">
                        <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                      </div>
                    ) : (
                      countValidLeaders(team)
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem onClick={() => onViewDetails(team.id)}>
                          <IconEye className="mr-2 size-4" />
                          Ver detalhes
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEdit(team)}>
                          <IconEdit className="mr-2 size-4" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(team)}
                          className="text-destructive focus:text-destructive"
                        >
                          <IconTrash className="mr-2 size-4" />
                          Excluir
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* O diálogo de confirmação foi movido para a página principal */}
    </>
  );
}
