import { UserRole } from "@/types/team";

/**
 * Verifica se um usuário tem permissão para acessar uma página ou recurso
 * @param userRole Papel do usuário no sistema
 * @param requiredRole Papel mínimo necessário para acessar o recurso
 * @returns Verdadeiro se o usuário tem permissão, falso caso contrário
 */
export function hasPermission(userRole: UserRole | undefined, requiredRole: UserRole): boolean {
  // Se o usuário não tem papel definido, não tem permissão
  if (!userRole) return false;
  
  // Admins têm acesso a tudo
  if (userRole === UserRole.ADMIN) return true;
  
  // Team Leaders têm acesso a recursos de Team Leader e Team Member
  if (userRole === UserRole.TEAM_LEADER) {
    return requiredRole === UserRole.TEAM_LEADER || requiredRole === UserRole.TEAM_MEMBER;
  }
  
  // Team Members só têm acesso a recursos de Team Member
  if (userRole === UserRole.TEAM_MEMBER) {
    return requiredRole === UserRole.TEAM_MEMBER;
  }
  
  return false;
}

/**
 * Verifica se um usuário é admin
 * @param userRole Papel do usuário no sistema
 * @returns Verdadeiro se o usuário é admin, falso caso contrário
 */
export function isAdmin(userRole: UserRole | undefined): boolean {
  return userRole === UserRole.ADMIN;
}

/**
 * Verifica se um usuário é líder de equipa
 * @param userRole Papel do usuário no sistema
 * @returns Verdadeiro se o usuário é líder de equipa, falso caso contrário
 */
export function isTeamLeader(userRole: UserRole | undefined): boolean {
  return userRole === UserRole.TEAM_LEADER;
}

/**
 * Verifica se um usuário é membro de equipa
 * @param userRole Papel do usuário no sistema
 * @returns Verdadeiro se o usuário é membro de equipa, falso caso contrário
 */
export function isTeamMember(userRole: UserRole | undefined): boolean {
  return userRole === UserRole.TEAM_MEMBER;
}

/**
 * Verifica se um usuário tem acesso a uma equipa específica
 * @param userRole Papel do usuário no sistema
 * @param userTeamId ID da equipa do usuário
 * @param teamId ID da equipa a ser acessada
 * @returns Verdadeiro se o usuário tem acesso à equipa, falso caso contrário
 */
export function hasTeamAccess(userRole: UserRole | undefined, userTeamId: string | undefined, teamId: string): boolean {
  // Admins têm acesso a todas as equipas
  if (userRole === UserRole.ADMIN) return true;
  
  // Outros usuários só têm acesso à própria equipa
  return userTeamId === teamId;
}

/**
 * Verifica se um usuário pode gerenciar membros de uma equipa
 * @param userRole Papel do usuário no sistema
 * @param userTeamId ID da equipa do usuário
 * @param teamId ID da equipa a ser gerenciada
 * @returns Verdadeiro se o usuário pode gerenciar membros da equipa, falso caso contrário
 */
export function canManageTeamMembers(userRole: UserRole | undefined, userTeamId: string | undefined, teamId: string): boolean {
  // Admins podem gerenciar membros de qualquer equipa
  if (userRole === UserRole.ADMIN) return true;
  
  // Team Leaders só podem gerenciar membros da própria equipa
  if (userRole === UserRole.TEAM_LEADER && userTeamId === teamId) return true;
  
  return false;
}
