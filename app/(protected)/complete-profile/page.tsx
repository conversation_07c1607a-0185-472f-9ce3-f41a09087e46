"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { ProfileForm } from "@/components/profile-form";

export default function CompleteProfilePage() {
  const { user, loading, userProfile, profileLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Se o usuário não estiver autenticado e não estiver carregando, redirecionar para o login
    if (!loading && !user) {
      console.log("Usuário não autenticado, redirecionando para login");
      router.push("/login");
      return;
    }

    // Se o perfil já estiver completo, redirecionar para o dashboard
    if (!profileLoading && userProfile?.profileCompleted) {
      console.log("Perfil já está completo, redirecionando para dashboard");
      router.push("/dashboard");
    }
  }, [user, loading, userProfile, profileLoading, router]);

  const handleProfileComplete = () => {
    console.log("Perfil completado com sucesso, redirecionando para dashboard");
    router.push("/dashboard");
  };

  if (loading || profileLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-muted-foreground">A carregar...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background p-4">
      <div className="w-full max-w-lg space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold tracking-tight">Complete o seu Perfil</h1>
          <p className="text-muted-foreground mt-2">
            Precisamos de algumas informações adicionais para personalizar a sua experiência.
          </p>
        </div>

        <ProfileForm onComplete={handleProfileComplete} />
      </div>
    </div>
  );
}
