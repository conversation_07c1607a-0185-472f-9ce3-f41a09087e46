"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { IconPlus } from "@tabler/icons-react";
import { SearchBar } from "@/components/search-bar";
import { TeamForm } from "@/components/teams/team-form";
import { TeamsTable } from "@/components/teams/teams-table";
import { Team } from "@/types/team";
import {
  addTeam,
  getTeams,
  updateTeam,
  deleteTeam,
  listenToTeams
} from "@/services/teams-service";
import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";

export function ClientPage() {
  const { user, loading, profileLoading, isAdmin } = useAuth();
  const router = useRouter();

  // Estados
  const [isLoading, setIsLoading] = useState(true);
  const [teams, setTeams] = useState<Team[]>([]);
  const [filteredTeams, setFilteredTeams] = useState<Team[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [teamToEdit, setTeamToEdit] = useState<Team | null>(null);

  // Estados para a dupla proteção na exclusão
  const [teamToDelete, setTeamToDelete] = useState<Team | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");

  // Efeito para verificar se o usuário é admin
  useEffect(() => {
    if (!loading && !profileLoading && !isAdmin) {
      showErrorToast("Não tem permissão para aceder a esta página");
      router.push("/dashboard");
    }
  }, [loading, profileLoading, isAdmin, router]);

  // Efeito para carregar equipas
  useEffect(() => {
    let unsubscribe: (() => void) | null = null;

    const loadTeams = async () => {
      try {
        setIsLoading(true);

        // Configurar listener para atualizações em tempo real
        unsubscribe = listenToTeams((teamsData) => {
          setTeams(teamsData);
          setFilteredTeams(teamsData);
          setIsLoading(false);
        });
      } catch (error) {
        console.error("Erro ao carregar equipas:", error);
        showErrorToast("Não foi possível carregar a lista de equipas");
        setIsLoading(false);
      }
    };

    if (user && isAdmin) {
      loadTeams();
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user, isAdmin]);

  // Função para adicionar uma nova equipa
  const handleAddTeam = async (teamData: Omit<Team, "id" | "createdAt" | "updatedAt" | "createdBy">) => {
    try {
      await addTeam(teamData);
      showSuccessToast("Equipa adicionada com sucesso");
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Erro ao adicionar equipa:", error);
      showErrorToast("Não foi possível adicionar a equipa");
    }
  };

  // Função para editar uma equipa
  const handleEditTeam = async (teamData: Partial<Omit<Team, "id" | "createdAt" | "createdBy">>) => {
    if (!teamToEdit) return;

    try {
      await updateTeam(teamToEdit.id, teamData);
      showSuccessToast("Equipa atualizada com sucesso");
      setIsEditModalOpen(false);
      setTeamToEdit(null);
    } catch (error) {
      console.error("Erro ao atualizar equipa:", error);
      showErrorToast("Não foi possível atualizar a equipa");
    }
  };

  // Função para iniciar o processo de exclusão
  const handleInitiateDelete = (team: Team) => {
    setTeamToDelete(team);
    setIsDeleteModalOpen(true);
  };

  // Função para avançar para a segunda etapa de confirmação
  const handleContinueDelete = () => {
    setIsDeleteModalOpen(false);
    // Limpar o texto de confirmação antes de abrir o segundo modal
    setDeleteConfirmText("");
    setTimeout(() => {
      setIsDeleteConfirmModalOpen(true);
    }, 100);
  };

  // Função para executar a exclusão após a dupla confirmação
  const handleDeleteTeam = async () => {
    if (!teamToDelete) return;

    // Verificar se o texto de confirmação corresponde ao nome da equipa
    if (deleteConfirmText !== teamToDelete.name) {
      showErrorToast("O nome digitado não corresponde ao nome da equipa");
      return;
    }

    try {
      await deleteTeam(teamToDelete.id);
      showSuccessToast("Equipa excluída com sucesso");
      setIsDeleteConfirmModalOpen(false);
      setTeamToDelete(null);
    } catch (error) {
      console.error("Erro ao excluir equipa:", error);
      showErrorToast("Não foi possível excluir a equipa");
    }
  };

  // Função para abrir o modal de edição
  const handleEdit = (team: Team) => {
    setTeamToEdit(team);
    setIsEditModalOpen(true);
  };

  // Função para pesquisar equipas (memoizada para evitar loops infinitos)
  const handleSearch = useCallback((searchTerm: string) => {
    if (!searchTerm.trim()) {
      setFilteredTeams(teams);
      return;
    }

    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    const filtered = teams.filter(team =>
      team.name.toLowerCase().includes(lowerCaseSearchTerm) ||
      (team.description && team.description.toLowerCase().includes(lowerCaseSearchTerm))
    );

    setFilteredTeams(filtered);
  }, [teams]);

  // Função para visualizar detalhes de uma equipa
  const handleViewDetails = (teamId: string) => {
    router.push(`/admin/teams/${teamId}`);
  };

  return (
    <div>
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="hidden sm:block">
              <h1 className="text-2xl font-bold tracking-tight">Equipas</h1>
              <p className="text-muted-foreground mt-2">
                Gerencie as equipas do sistema
              </p>
            </div>
            <Button onClick={() => setIsAddModalOpen(true)} className="shrink-0">
              <IconPlus className="size-4 mr-2" />
              Adicionar Equipa
            </Button>
          </div>

          {/* Barra de pesquisa */}
          <div className="mb-6">
            <SearchBar
              onSearch={handleSearch}
              placeholder="Pesquisar por nome..."
              className="max-w-full sm:max-w-md"
            />
          </div>

          {/* Seção de listagem */}
          <div className="mt-2">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="size-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              </div>
            ) : (
              <TeamsTable
                teams={filteredTeams}
                onDelete={handleInitiateDelete}
                onEdit={handleEdit}
                onViewDetails={handleViewDetails}
              />
            )}
          </div>

          {/* Modal de Adição */}
          <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Adicionar Nova Equipa</DialogTitle>
              </DialogHeader>
              <TeamForm onSaveComplete={handleAddTeam} />
            </DialogContent>
          </Dialog>

          {/* Modal de Edição */}
          <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Editar Equipa</DialogTitle>
              </DialogHeader>
              {teamToEdit && (
                <TeamForm
                  initialData={teamToEdit}
                  onSaveComplete={handleEditTeam}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Primeiro Modal de Exclusão - Aviso Inicial */}
          <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Excluir Equipa</DialogTitle>
                <DialogDescription>
                  Tem certeza que deseja excluir esta equipa? Esta ação não pode ser desfeita e resultará na perda de todos os dados associados à equipa.
                </DialogDescription>
              </DialogHeader>
              <div className="p-4 my-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                <p className="text-sm text-yellow-800 dark:text-yellow-400">
                  <strong>Atenção:</strong> A exclusão de uma equipa removerá todos os membros e líderes associados a ela. Os utilizadores permanecerão no sistema, mas perderão o acesso aos dados da equipa.
                </p>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
                  Cancelar
                </Button>
                <Button variant="destructive" onClick={handleContinueDelete}>
                  Continuar com a Exclusão
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Segundo Modal de Exclusão - Confirmação Final */}
          <Dialog open={isDeleteConfirmModalOpen} onOpenChange={setIsDeleteConfirmModalOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Confirmação Final de Exclusão</DialogTitle>
                <DialogDescription>
                  Para confirmar a exclusão, digite o nome exato da equipa: <strong>{teamToDelete?.name}</strong>
                </DialogDescription>
              </DialogHeader>
              <div className="p-4 my-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <p className="text-sm text-red-800 dark:text-red-400">
                  <strong>Aviso:</strong> Esta ação é irreversível! Todos os dados associados a esta equipa serão permanentemente perdidos.
                </p>
              </div>
              <div className="my-4">
                <label htmlFor="confirm-text" className="text-sm font-medium">
                  Nome da equipa:
                </label>
                <input
                  id="confirm-text"
                  type="text"
                  value={deleteConfirmText}
                  onChange={(e) => setDeleteConfirmText(e.target.value)}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder={`Digite "${teamToDelete?.name}" para confirmar`}
                />
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteConfirmModalOpen(false)}>
                  Cancelar
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteTeam}
                  disabled={deleteConfirmText !== teamToDelete?.name}
                >
                  Excluir Permanentemente
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
