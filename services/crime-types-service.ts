import { db } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  query,
  orderBy,
  serverTimestamp,
  onSnapshot,
  Unsubscribe,
  where,
  setDoc,
  deleteDoc
} from "firebase/firestore";

export interface CrimeType {
  id: string;
  name: string;
  nameLowerCase?: string; // Versão em minúsculas para busca case-insensitive
  category?: string;
  count: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

const COLLECTION_NAME = "crimeTypes";

// Normalizar nome do tipo de crime (remover espaços extras, padronizar capitalização)
function normalizeCrimeTypeName(name: string): string {
  if (!name) return "";

  // Remover espaços extras no início e fim
  let normalized = name.trim();

  // Remover espaços duplicados no meio do texto
  normalized = normalized.replace(/\s+/g, ' ');

  // Capitalizar a primeira letra de cada palavra
  normalized = normalized.split(' ')
    .map(word => {
      if (word.length > 0) {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      }
      return word;
    })
    .join(' ');

  // Exceções para palavras que devem permanecer em minúsculas
  const lowerCaseWords = ['de', 'do', 'da', 'dos', 'das', 'e', 'ou', 'em', 'por', 'para', 'com', 'sem'];

  // Aplicar exceções, mas não para a primeira palavra
  const words = normalized.split(' ');
  if (words.length > 1) {
    for (let i = 1; i < words.length; i++) {
      if (lowerCaseWords.includes(words[i].toLowerCase())) {
        words[i] = words[i].toLowerCase();
      }
    }
    normalized = words.join(' ');
  }

  return normalized;
}

// Adicionar um novo tipo de crime
export async function addCrimeType(crimeTypeData: Omit<CrimeType, "id" | "createdAt" | "updatedAt" | "count">): Promise<string> {
  try {
    // Normalizar o nome do tipo de crime
    const normalizedName = normalizeCrimeTypeName(crimeTypeData.name);

    // Se o nome normalizado estiver vazio, não adicionar
    if (!normalizedName) {
      console.warn("Tentativa de adicionar tipo de crime com nome vazio");
      return "";
    }

    // Verificar se já existe um tipo de crime com o mesmo nome (case insensitive)
    const q = query(
      collection(db, COLLECTION_NAME),
      where("nameLowerCase", "==", normalizedName.toLowerCase())
    );

    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      // Se já existe, incrementar o contador e retornar o ID existente
      const existingDoc = querySnapshot.docs[0];
      const existingData = existingDoc.data();

      // Incrementar o contador
      await incrementCrimeTypeCount(existingDoc.id);

      // Se o tipo de crime existente não tem categoria mas o novo tem, atualizar a categoria
      if (crimeTypeData.category && (!existingData.category || existingData.category === "Sem categoria")) {
        await setDoc(
          doc(db, COLLECTION_NAME, existingDoc.id),
          {
            category: crimeTypeData.category,
            updatedAt: serverTimestamp(),
          },
          { merge: true }
        );
        console.log(`Categoria do tipo de crime ${normalizedName} atualizada para ${crimeTypeData.category}`);
      }

      return existingDoc.id;
    }

    // Se não existe, criar um novo
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...crimeTypeData,
      name: normalizedName, // Usar o nome normalizado
      nameLowerCase: normalizedName.toLowerCase(), // Armazenar versão em minúsculas para busca
      count: 1,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar tipo de crime:", error);
    throw error;
  }
}

// Incrementar o contador de uso de um tipo de crime
export async function incrementCrimeTypeCount(id: string): Promise<boolean> {
  try {
    // Obter o documento atual para ler o valor do contador
    const crimeTypeRef = doc(db, COLLECTION_NAME, id);
    const crimeTypeDoc = await getDoc(crimeTypeRef);

    if (!crimeTypeDoc.exists()) {
      console.error(`Tipo de crime com ID ${id} não encontrado`);
      return false;
    }

    const currentData = crimeTypeDoc.data();
    const currentCount = currentData.count || 0;

    // Incrementar o contador
    await setDoc(
      crimeTypeRef,
      {
        count: currentCount + 1,
        updatedAt: serverTimestamp(),
      },
      { merge: true }
    );
    return true;
  } catch (error) {
    console.error("Erro ao incrementar contador de tipo de crime:", error);
    throw error;
  }
}

// Decrementar o contador de uso de um tipo de crime
export async function decrementCrimeTypeCount(id: string): Promise<boolean> {
  try {
    // Obter o documento atual para ler o valor do contador
    const crimeTypeRef = doc(db, COLLECTION_NAME, id);
    const crimeTypeDoc = await getDoc(crimeTypeRef);

    if (!crimeTypeDoc.exists()) {
      console.error(`Tipo de crime com ID ${id} não encontrado`);
      return false;
    }

    const currentData = crimeTypeDoc.data();
    const currentCount = currentData.count || 0;

    // Decrementar o contador, mas não permitir valores negativos
    const newCount = Math.max(0, currentCount - 1);

    // Atualizar o contador
    await setDoc(
      crimeTypeRef,
      {
        count: newCount,
        updatedAt: serverTimestamp(),
      },
      { merge: true }
    );
    return true;
  } catch (error) {
    console.error("Erro ao decrementar contador de tipo de crime:", error);
    throw error;
  }
}

// Obter um tipo de crime pelo nome
export async function getCrimeTypeByName(name: string): Promise<CrimeType | null> {
  try {
    // Normalizar o nome para busca
    const normalizedName = normalizeCrimeTypeName(name);
    if (!normalizedName) return null;

    // Buscar pelo campo nameLowerCase para garantir case-insensitive
    const q = query(
      collection(db, COLLECTION_NAME),
      where("nameLowerCase", "==", normalizedName.toLowerCase())
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data(),
    } as CrimeType;
  } catch (error) {
    console.error("Erro ao buscar tipo de crime por nome:", error);
    return null;
  }
}

// Obter todos os tipos de crime
export async function getCrimeTypes(): Promise<CrimeType[]> {
  try {
    const q = query(collection(db, COLLECTION_NAME), orderBy("count", "desc"));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as CrimeType));
  } catch (error) {
    console.error("Erro ao obter tipos de crime:", error);
    throw error;
  }
}

// Pesquisar tipos de crime por termo
export async function searchCrimeTypes(searchTerm: string): Promise<CrimeType[]> {
  try {
    if (!searchTerm || searchTerm.length < 2) {
      return [];
    }

    // Obter todos os tipos de crime e filtrar no cliente
    // Isso é necessário porque o Firestore não suporta pesquisa de texto completo
    const allCrimeTypes = await getCrimeTypes();

    // Filtrar os resultados que contêm o termo de pesquisa
    const searchTermLower = searchTerm.toLowerCase();

    // Criar um Map para rastrear crimes já adicionados (usando o nome em minúsculas como chave)
    const uniqueResults = new Map();

    // Primeiro, procurar correspondências exatas no início do nome
    const exactStartMatches = allCrimeTypes.filter(crimeType =>
      crimeType.name.toLowerCase().startsWith(searchTermLower)
    );

    // Adicionar correspondências exatas ao Map
    exactStartMatches.forEach(crimeType => {
      uniqueResults.set(crimeType.name.toLowerCase(), crimeType);
    });

    // Depois, procurar correspondências em qualquer parte do nome
    const partialMatches = allCrimeTypes.filter(crimeType =>
      crimeType.name.toLowerCase().includes(searchTermLower) &&
      !crimeType.name.toLowerCase().startsWith(searchTermLower) &&
      !uniqueResults.has(crimeType.name.toLowerCase()) // Verificar se já foi adicionado
    );

    // Adicionar correspondências parciais ao Map
    partialMatches.forEach(crimeType => {
      uniqueResults.set(crimeType.name.toLowerCase(), crimeType);
    });

    // Converter o Map de volta para um array, mantendo a ordem (exatos primeiro, depois parciais)
    return [...exactStartMatches, ...partialMatches];
  } catch (error) {
    console.error("Erro ao pesquisar tipos de crime:", error);
    return [];
  }
}

// Escutar mudanças em tipos de crime em tempo real
export function listenToCrimeTypes(callback: (crimeTypes: CrimeType[]) => void): Unsubscribe {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy("count", "desc")
    );

    return onSnapshot(q, (querySnapshot) => {
      const crimeTypes = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as CrimeType));

      callback(crimeTypes);
    }, (error) => {
      console.error("Erro ao escutar tipos de crime:", error);
    });
  } catch (error) {
    console.error("Erro ao configurar listener de tipos de crime:", error);
    throw error;
  }
}

// Remover um tipo de crime
export async function deleteCrimeType(id: string): Promise<boolean> {
  try {
    const crimeTypeRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(crimeTypeRef);
    return true;
  } catch (error) {
    console.error("Erro ao remover tipo de crime:", error);
    throw error;
  }
}

// Sincronizar contadores de tipos de crime com o uso real nos registos
export async function syncCrimeTypeCounts(): Promise<{ updated: number, total: number }> {
  try {
    // Obter todos os tipos de crime
    const allCrimeTypes = await getCrimeTypes();

    // Obter todos os registos que contêm tipos de crime
    const registosRef = collection(db, "registos");
    const registosSnapshot = await getDocs(registosRef);

    // Criar um mapa para contar ocorrências de cada tipo de crime
    const crimeTypeCounts = new Map<string, number>();

    // Inicializar o mapa com todos os tipos de crime conhecidos
    allCrimeTypes.forEach(crimeType => {
      // Usar o nome normalizado e em minúsculas como chave
      const key = crimeType.nameLowerCase || crimeType.name.toLowerCase();
      crimeTypeCounts.set(key, 0);
    });

    // Contar ocorrências de cada tipo de crime nos registos
    registosSnapshot.forEach(doc => {
      const registo = doc.data();
      if (registo.tipoCrime) {
        const tipoCrimeNormalizado = normalizeCrimeTypeName(registo.tipoCrime).toLowerCase();

        // Incrementar o contador para este tipo de crime
        const currentCount = crimeTypeCounts.get(tipoCrimeNormalizado) || 0;
        crimeTypeCounts.set(tipoCrimeNormalizado, currentCount + 1);
      }
    });

    // Atualizar os contadores no Firestore
    let updatedCount = 0;

    for (const crimeType of allCrimeTypes) {
      const key = crimeType.nameLowerCase || crimeType.name.toLowerCase();
      const realCount = crimeTypeCounts.get(key) || 0;

      // Só atualizar se o contador for diferente
      if (crimeType.count !== realCount) {
        const crimeTypeRef = doc(db, COLLECTION_NAME, crimeType.id);
        await setDoc(
          crimeTypeRef,
          {
            count: realCount,
            updatedAt: serverTimestamp(),
          },
          { merge: true }
        );
        updatedCount++;
      }
    }

    console.log(`Sincronização concluída: ${updatedCount} de ${allCrimeTypes.length} tipos de crime atualizados`);
    return { updated: updatedCount, total: allCrimeTypes.length };
  } catch (error) {
    console.error("Erro ao sincronizar contadores de tipos de crime:", error);
    throw error;
  }
}

// Redefinir todos os contadores de tipos de crime para zero
export async function resetAllCrimeTypeCounts(): Promise<number> {
  try {
    // Obter todos os tipos de crime
    const allCrimeTypes = await getCrimeTypes();
    let resetCount = 0;

    // Redefinir o contador de cada tipo de crime para zero
    for (const crimeType of allCrimeTypes) {
      const crimeTypeRef = doc(db, COLLECTION_NAME, crimeType.id);
      await setDoc(
        crimeTypeRef,
        {
          count: 0,
          updatedAt: serverTimestamp(),
        },
        { merge: true }
      );
      resetCount++;
    }

    console.log(`Contadores redefinidos para ${resetCount} tipos de crime`);
    return resetCount;
  } catch (error) {
    console.error("Erro ao redefinir contadores de tipos de crime:", error);
    throw error;
  }
}

// Verificar e remover duplicatas de tipos de crime
export async function cleanupDuplicateCrimeTypes(): Promise<number> {
  try {
    // Obter todos os tipos de crime
    const allCrimeTypes = await getCrimeTypes();

    // Criar um mapa para rastrear tipos de crime por nome (case insensitive)
    const crimeTypeMap = new Map<string, CrimeType[]>();

    // Agrupar tipos de crime pelo nome em minúsculas
    allCrimeTypes.forEach(crimeType => {
      // Usar o campo nameLowerCase se disponível, ou normalizar o nome
      const lowerName = crimeType.nameLowerCase || normalizeCrimeTypeName(crimeType.name).toLowerCase();
      if (!crimeTypeMap.has(lowerName)) {
        crimeTypeMap.set(lowerName, []);
      }
      crimeTypeMap.get(lowerName)?.push(crimeType);
    });

    let removedCount = 0;

    // Para cada grupo de tipos de crime com o mesmo nome
    for (const [name, crimeTypes] of crimeTypeMap.entries()) {
      // Se houver mais de um tipo de crime com o mesmo nome
      if (crimeTypes.length > 1) {
        console.log(`Encontradas ${crimeTypes.length} duplicatas para "${name}"`);

        // Ordenar por contagem (manter o mais usado) e depois por data de criação (manter o mais antigo)
        crimeTypes.sort((a, b) => {
          if (a.count !== b.count) {
            return b.count - a.count; // Manter o com maior contagem
          }
          // Se a contagem for igual, não ordenar por data (pode causar erros se a data não for um objeto Date válido)
          return 0;
        });

        // Manter o primeiro e remover os outros
        const toKeep = crimeTypes[0];
        const toRemove = crimeTypes.slice(1);

        // Garantir que o tipo de crime que será mantido tenha o campo nameLowerCase
        if (!toKeep.nameLowerCase) {
          const normalizedName = normalizeCrimeTypeName(toKeep.name);
          const crimeTypeRef = doc(db, COLLECTION_NAME, toKeep.id);
          await setDoc(
            crimeTypeRef,
            {
              name: normalizedName,
              nameLowerCase: normalizedName.toLowerCase(),
              updatedAt: serverTimestamp(),
            },
            { merge: true }
          );
        }

        console.log(`Mantendo: ${toKeep.id} (${toKeep.name}, contagem: ${toKeep.count})`);

        // Remover duplicatas
        for (const crimeType of toRemove) {
          console.log(`Removendo: ${crimeType.id} (${crimeType.name}, contagem: ${crimeType.count})`);
          await deleteCrimeType(crimeType.id);
          removedCount++;
        }
      }
    }

    console.log(`Limpeza concluída. Removidas ${removedCount} duplicatas.`);
    return removedCount;
  } catch (error) {
    console.error("Erro ao limpar duplicatas de tipos de crime:", error);
    throw error;
  }
}

// Adicionar alguns tipos de crime comuns para inicializar a coleção
export async function initializeCommonCrimeTypes(userId: string, forceUpdate: boolean = false, cleanupDuplicates: boolean = true): Promise<void> {
  const commonCrimeTypes = [
    // Crimes contra as pessoas
    { name: "Homicídio", category: "Crimes contra as pessoas" },
    { name: "Homicídio qualificado", category: "Crimes contra as pessoas" },
    { name: "Homicídio por negligência", category: "Crimes contra as pessoas" },
    { name: "Infanticídio", category: "Crimes contra as pessoas" },
    { name: "Ofensa à integridade física simples", category: "Crimes contra as pessoas" },
    { name: "Ofensa à integridade física grave", category: "Crimes contra as pessoas" },
    { name: "Ofensa à integridade física qualificada", category: "Crimes contra as pessoas" },
    { name: "Violência doméstica", category: "Crimes contra as pessoas" },
    { name: "Maus tratos", category: "Crimes contra as pessoas" },
    { name: "Ameaça", category: "Crimes contra as pessoas" },
    { name: "Coação", category: "Crimes contra as pessoas" },
    { name: "Perseguição (Stalking)", category: "Crimes contra as pessoas" },
    { name: "Sequestro", category: "Crimes contra as pessoas" },
    { name: "Rapto", category: "Crimes contra as pessoas" },
    { name: "Difamação", category: "Crimes contra as pessoas" },
    { name: "Injúria", category: "Crimes contra as pessoas" },
    { name: "Violação", category: "Crimes contra as pessoas" },
    { name: "Abuso sexual de crianças", category: "Crimes contra as pessoas" },
    { name: "Abuso sexual de menores dependentes", category: "Crimes contra as pessoas" },
    { name: "Atos sexuais com adolescentes", category: "Crimes contra as pessoas" },
    { name: "Lenocínio", category: "Crimes contra as pessoas" },
    { name: "Pornografia de menores", category: "Crimes contra as pessoas" },
    { name: "Importunação sexual", category: "Crimes contra as pessoas" },

    // Crimes contra o património
    { name: "Furto", category: "Crimes contra o património" },
    { name: "Furto qualificado", category: "Crimes contra o património" },
    { name: "Furto de uso de veículo", category: "Crimes contra o património" },
    { name: "Abuso de confiança", category: "Crimes contra o património" },
    { name: "Roubo", category: "Crimes contra o património" },
    { name: "Roubo agravado", category: "Crimes contra o património" },
    { name: "Burla", category: "Crimes contra o património" },
    { name: "Burla qualificada", category: "Crimes contra o património" },
    { name: "Burla informática", category: "Crimes contra o património" },
    { name: "Extorsão", category: "Crimes contra o património" },
    { name: "Dano", category: "Crimes contra o património" },
    { name: "Dano qualificado", category: "Crimes contra o património" },
    { name: "Usurpação de coisa imóvel", category: "Crimes contra o património" },
    { name: "Recetação", category: "Crimes contra o património" },
    { name: "Branqueamento de capitais", category: "Crimes contra o património" },

    // Crimes contra a vida em sociedade
    { name: "Tráfico de estupefacientes", category: "Crimes contra a vida em sociedade" },
    { name: "Tráfico de menor gravidade", category: "Crimes contra a vida em sociedade" },
    { name: "Tráfico-consumo", category: "Crimes contra a vida em sociedade" },
    { name: "Consumo de estupefacientes", category: "Crimes contra a vida em sociedade" },
    { name: "Condução sob efeito de álcool", category: "Crimes contra a vida em sociedade" },
    { name: "Condução sob efeito de estupefacientes", category: "Crimes contra a vida em sociedade" },
    { name: "Condução sem habilitação legal", category: "Crimes contra a vida em sociedade" },
    { name: "Condução perigosa", category: "Crimes contra a vida em sociedade" },
    { name: "Falsificação de documentos", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de moeda", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de títulos", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de selos, cunhos, marcas ou chancelas", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de valores selados", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de cartões de crédito e débito", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de documentos de identificação", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de documentos de viagem", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de certificados", category: "Crimes contra a vida em sociedade" },
    { name: "Contrafação de produtos", category: "Crimes contra a vida em sociedade" },
    { name: "Violação de direitos de autor", category: "Crimes contra a vida em sociedade" },
    { name: "Violação de propriedade industrial", category: "Crimes contra a vida em sociedade" },
    { name: "Uso de documento falso", category: "Crimes contra a vida em sociedade" },
    { name: "Uso de moeda falsa", category: "Crimes contra a vida em sociedade" },
    { name: "Incêndio florestal", category: "Crimes contra a vida em sociedade" },
    { name: "Incêndio urbano", category: "Crimes contra a vida em sociedade" },
    { name: "Dano contra a natureza", category: "Crimes contra a vida em sociedade" },
    { name: "Poluição", category: "Crimes contra a vida em sociedade" },
    { name: "Associação criminosa", category: "Crimes contra a vida em sociedade" },
    { name: "Terrorismo", category: "Crimes contra a vida em sociedade" },

    // Crimes contra o Estado
    { name: "Desobediência", category: "Crimes contra o Estado" },
    { name: "Desobediência qualificada", category: "Crimes contra o Estado" },
    { name: "Resistência e coação sobre funcionário", category: "Crimes contra o Estado" },
    { name: "Corrupção", category: "Crimes contra o Estado" },
    { name: "Corrupção passiva", category: "Crimes contra o Estado" },
    { name: "Corrupção ativa", category: "Crimes contra o Estado" },
    { name: "Peculato", category: "Crimes contra o Estado" },
    { name: "Abuso de poder", category: "Crimes contra o Estado" },
    { name: "Prevaricação", category: "Crimes contra o Estado" },
    { name: "Participação económica em negócio", category: "Crimes contra o Estado" },
    { name: "Falsas declarações", category: "Crimes contra o Estado" },
    { name: "Denúncia caluniosa", category: "Crimes contra o Estado" },
    { name: "Violação de segredo de justiça", category: "Crimes contra o Estado" },

    // Outros crimes
    { name: "Posse de arma proibida", category: "Outros crimes" },
    { name: "Tráfico de armas", category: "Outros crimes" },
    { name: "Imigração ilegal", category: "Outros crimes" },
    { name: "Auxílio à imigração ilegal", category: "Outros crimes" },
    { name: "Tráfico de pessoas", category: "Outros crimes" },
    { name: "Violação de domicílio", category: "Outros crimes" },
    { name: "Violação de correspondência", category: "Outros crimes" },
    { name: "Acesso ilegítimo a sistema informático", category: "Outros crimes" },
    { name: "Sabotagem informática", category: "Outros crimes" },
    { name: "Atentado à segurança de transporte", category: "Outros crimes" },
  ];

  try {
    // Se forceUpdate for true, adicionar todos os tipos de crime
    let typesToAdd = commonCrimeTypes;

    if (!forceUpdate) {
      // Verificar quais tipos de crime já existem na coleção
      const existingCrimeTypes = await getCrimeTypes();
      const existingCrimeNames = new Set(existingCrimeTypes.map(crime => crime.name.toLowerCase()));

      // Filtrar apenas os tipos de crime que ainda não existem
      typesToAdd = commonCrimeTypes.filter(
        crimeType => !existingCrimeNames.has(crimeType.name.toLowerCase())
      );

      // Se não há novos tipos de crime para adicionar, retornar
      if (typesToAdd.length === 0) {
        console.log("Não há novos tipos de crime para adicionar");
        return;
      }
    }

    console.log(`Adicionando ${typesToAdd.length} tipos de crime`);

    // Adicionar os tipos de crime selecionados
    for (const crimeType of typesToAdd) {
      const normalizedName = normalizeCrimeTypeName(crimeType.name);
      await addDoc(collection(db, COLLECTION_NAME), {
        name: normalizedName,
        nameLowerCase: normalizedName.toLowerCase(),
        category: crimeType.category,
        count: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy: userId,
      });
    }

    console.log("Tipos de crime comuns inicializados com sucesso");

    // Limpar duplicatas automaticamente se solicitado
    if (cleanupDuplicates) {
      try {
        const removedCount = await cleanupDuplicateCrimeTypes();
        if (removedCount > 0) {
          console.log(`Limpeza automática: ${removedCount} duplicatas removidas`);
        }
      } catch (cleanupError) {
        console.error("Erro na limpeza automática de duplicatas:", cleanupError);
        // Não propagar o erro para não interromper o fluxo principal
      }
    }
  } catch (error) {
    console.error("Erro ao inicializar tipos de crime comuns:", error);
  }
}
