{"hosting": {"source": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "**/.next/cache/**"], "frameworksBackend": {"region": "europe-west1"}, "cleanUrls": true, "trailingSlash": false, "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico|woff2|woff)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow, noarchive, nosnippet, noimageindex"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "no-referrer"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}}